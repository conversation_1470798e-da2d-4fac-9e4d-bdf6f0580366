# Configuration Reference

This document provides a comprehensive reference for configuring AI Code Review.

## Configuration File Location

AI Code Review looks for configuration files in the following order:

1. `.ai-review.json` (project-specific, current directory)
2. `.ai-review.jsonc` (project-specific with comments)
3. `ai-review.config.json` (alternative naming)
4. `~/.ai-review.json` (global user configuration)

Project-specific configurations take precedence over global ones.

## Configuration Schema

### Complete Configuration Example

```json
{
  "aiProvider": "openai",
  "model": "gpt-4",
  "apiKey": "sk-...",
  "rules": {
    "logic": "strict",
    "performance": "normal",
    "security": "strict", 
    "style": "normal",
    "business": "normal"
  },
  "output": {
    "format": "terminal",
    "detailed": true,
    "verbose": false
  },
  "cache": {
    "enabled": true,
    "ttl": 24
  },
  "ignore": [
    "node_modules/**",
    "*.min.js",
    "*.min.css",
    "dist/**",
    "build/**",
    ".git/**"
  ]
}
```

## Configuration Options

### AI Provider Settings

#### `aiProvider` (string, required)
The AI service provider to use for code analysis.

**Options:**
- `"openai"` - Use OpenAI GPT models
- `"claude"` - Use Anthropic Claude models  
- `"local"` - Use local LLM (requires additional setup)

**Default:** `"openai"`

**Example:**
```json
{
  "aiProvider": "openai"
}
```

#### `model` (string, required)
The specific AI model to use for analysis.

**OpenAI Models:**
- `"gpt-4"` - Most capable, recommended
- `"gpt-4-turbo"` - Faster GPT-4 variant
- `"gpt-3.5-turbo"` - Faster, less comprehensive

**Claude Models:**
- `"claude-3-opus-20240229"` - Most capable Claude model
- `"claude-3-sonnet-20240229"` - Balanced performance/cost (recommended)
- `"claude-3-haiku-20240307"` - Fastest, most cost-effective

**Local Models:**
- `"codellama"` - Code Llama model
- `"deepseek-coder"` - DeepSeek Coder model
- `"starcoder"` - StarCoder model

**Example:**
```json
{
  "model": "gpt-4"
}
```

#### `apiKey` (string, optional)
API key for the chosen provider. Can be omitted if using environment variables.

**Security Note:** It's recommended to use environment variables instead of storing API keys in configuration files.

**Environment Variables:**
- `OPENAI_API_KEY` for OpenAI
- `ANTHROPIC_API_KEY` for Claude
- `AI_REVIEW_API_KEY` generic fallback

### Analysis Rules

#### `rules` (object, required)
Configure analysis strictness for different categories.

**Structure:**
```json
{
  "rules": {
    "logic": "strict|normal|loose",
    "performance": "strict|normal|loose", 
    "security": "strict|normal|loose",
    "style": "strict|normal|loose",
    "business": "strict|normal|loose"
  }
}
```

**Rule Categories:**

- **`logic`** - Programming errors, null checks, undefined variables, logic flaws
- **`performance`** - Efficiency issues, memory leaks, inefficient algorithms
- **`security`** - Vulnerabilities, XSS, injection attacks, data exposure
- **`style`** - Code formatting, naming conventions, organization
- **`business`** - Domain-specific rules, workflow validation

**Strictness Levels:**

- **`strict`** - Maximum scrutiny, catches minor issues
- **`normal`** - Balanced analysis, focuses on important issues (recommended)
- **`loose`** - Minimal analysis, only critical problems

**Recommended Configurations:**

For **production code**:
```json
{
  "rules": {
    "logic": "strict",
    "performance": "normal",
    "security": "strict",
    "style": "normal", 
    "business": "strict"
  }
}
```

For **development/prototyping**:
```json
{
  "rules": {
    "logic": "normal",
    "performance": "loose",
    "security": "normal",
    "style": "loose",
    "business": "normal"
  }
}
```

### Output Configuration

#### `output` (object, required)
Controls how analysis results are displayed and formatted.

```json
{
  "output": {
    "format": "terminal|html|json",
    "detailed": true|false,
    "verbose": true|false
  }
}
```

**Options:**

- **`format`** - Output format
  - `"terminal"` - Colorful console output (default)
  - `"html"` - Interactive web report with charts
  - `"json"` - Structured data for automation

- **`detailed`** - Include detailed explanations
  - `true` - Show full issue descriptions and suggestions (default)
  - `false` - Show only issue summaries

- **`verbose`** - Show debug information
  - `true` - Include performance metrics, cache stats, etc.
  - `false` - Standard output (default)

### Cache Configuration

#### `cache` (object, required)
Configure the analysis caching system for improved performance.

```json
{
  "cache": {
    "enabled": true|false,
    "ttl": 24
  }
}
```

**Options:**

- **`enabled`** - Enable/disable caching
  - `true` - Cache analysis results (recommended)
  - `false` - Always perform fresh analysis

- **`ttl`** - Time-to-live in hours
  - How long to keep cached results before expiring
  - **Default:** `24` hours
  - **Range:** 1-168 hours (1 week max)

**Cache Benefits:**
- 60-80% faster re-analysis of unchanged code
- Significant reduction in API costs
- Improved development workflow

### Ignore Patterns

#### `ignore` (array, required)
Glob patterns for files and directories to exclude from analysis.

```json
{
  "ignore": [
    "node_modules/**",
    "*.min.js",
    "*.min.css", 
    "dist/**",
    "build/**",
    ".git/**",
    "coverage/**",
    "*.test.js",
    "*.spec.ts"
  ]
}
```

**Common Patterns:**

- **Dependencies:** `"node_modules/**"`, `"vendor/**"`
- **Build artifacts:** `"dist/**"`, `"build/**"`, `"out/**"`
- **Minified files:** `"*.min.js"`, `"*.min.css"`
- **Test files:** `"*.test.js"`, `"*.spec.ts"`, `"__tests__/**"`
- **Generated files:** `"*.d.ts"`, `"*.generated.*"`
- **Version control:** `".git/**"`, `".svn/**"`

## Environment Variables

You can override configuration options using environment variables:

```bash
# API Keys
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."

# Configuration overrides
export AI_REVIEW_PROVIDER="openai"
export AI_REVIEW_MODEL="gpt-4"
```

**Priority Order:** Environment variables > Configuration file > Defaults

## Project-Specific Recommendations

AI Code Review automatically detects your project type and provides tailored recommendations:

### React Projects
```json
{
  "rules": {
    "logic": "strict",
    "performance": "normal",
    "security": "normal"
  },
  "ignore": [
    "build/**",
    "public/**",
    "*.test.jsx"
  ]
}
```

### Node.js Projects
```json
{
  "rules": {
    "security": "strict",
    "performance": "normal"
  },
  "ignore": [
    "node_modules/**",
    "logs/**"
  ]
}
```

### TypeScript Projects
```json
{
  "rules": {
    "logic": "strict",
    "style": "normal"
  },
  "ignore": [
    "*.d.ts",
    "dist/**"
  ]
}
```

## Configuration Validation

Use the built-in validation to check your configuration:

```bash
# Validate current configuration
ai-review config --validate

# Auto-fix common issues
ai-review config --validate --fix

# Generate project-specific template
ai-review config --template
```

## Troubleshooting

### Common Issues

**"API key required" Error:**
- Set environment variable: `export OPENAI_API_KEY="your-key"`
- Or add to config: `"apiKey": "your-key"`

**"Model not compatible" Warning:**
- Check model name spelling
- Ensure model is available for your provider
- Run `ai-review config --validate` for suggestions

**"Configuration file not found":**
- Run `ai-review config --wizard` to create one
- Or run `ai-review config --template` for a template

**Performance Issues:**
- Enable caching: `"cache": {"enabled": true}`
- Use faster model: `"model": "gpt-3.5-turbo"`
- Reduce strictness: `"rules": {"style": "loose"}`

For more help, run `ai-review doctor` to diagnose system issues.