# CLI Reference

Complete reference for AI Code Review command-line interface.

## Global Options

These options are available for all commands:

- `--help, -h` - Show help information
- `--version, -V` - Display version number

## Commands Overview

```bash
ai-review [mode] [options]         # Default command - analyze staged files
ai-review analyze [options]        # Full analysis with options
ai-review config [options]         # Configuration management
ai-review cache [options]          # Cache management
ai-review doctor                   # System health check
```

---

## Default Command

**Usage:** `ai-review [mode] [options]`

Quick analysis of Git staged files.

### Arguments

- `mode` - Analysis mode (optional)
  - `quick` - Fast analysis, critical issues only
  - `full` - Comprehensive analysis (default)

### Options

- `-o, --output <format>` - Output format
  - `terminal` - Colorful console output (default)
  - `html` - Interactive web report
  - `json` - Structured data
- `-c, --config <path>` - Path to custom configuration file
- `-v, --verbose` - Enable detailed logging
- `--no-cache` - Skip cache, force fresh analysis

### Examples

```bash
# Quick start - analyze staged files
ai-review

# Quick mode analysis
ai-review quick

# Full analysis with HTML report
ai-review full --output html

# Verbose analysis with custom config
ai-review --verbose --config ./custom-config.json
```

---

## Analyze Command

**Usage:** `ai-review analyze [options]`

Full-featured analysis with all available options.

### Options

#### Analysis Mode
- `-m, --mode <mode>` - Analysis mode
  - `quick` - Fast analysis, critical issues only
  - `full` - Comprehensive analysis (default)

#### File Selection
- `--staged` - Analyze only staged files (default)
- `--all` - Analyze all modified files (staged + unstaged)

#### Output Control
- `-o, --output <format>` - Output format
  - `terminal` - Colorful console output (default)
  - `html` - Interactive web report with charts
  - `json` - Structured data for automation

#### Configuration
- `-c, --config <path>` - Path to custom configuration file
- `-v, --verbose` - Enable detailed logging and progress information
- `--no-cache` - Skip cache and force fresh analysis of all files

### Examples

```bash
# Basic analysis
ai-review analyze

# Quick analysis of all modified files
ai-review analyze --mode quick --all

# Generate HTML report with verbose output
ai-review analyze --output html --verbose

# Fresh analysis without cache
ai-review analyze --no-cache

# Custom configuration
ai-review analyze --config ./project-config.json

# Comprehensive analysis of all files with HTML output
ai-review analyze --mode full --all --output html --verbose
```

---

## Config Command

**Usage:** `ai-review config [options]`

Manage AI Code Review configuration.

### Options

#### Setup Commands
- `--init` - Initialize configuration with interactive wizard
- `--wizard` - Start interactive configuration wizard (same as --init)
- `--template` - Generate a configuration template file

#### Management Commands  
- `--show` - Display current configuration (API keys hidden)
- `--validate` - Validate current configuration
- `--reset` - Reset configuration to defaults

### Examples

```bash
# Interactive first-time setup
ai-review config --wizard

# Generate configuration template
ai-review config --template

# Show current configuration
ai-review config --show

# Validate configuration and auto-fix issues
ai-review config --validate

# Reset to defaults (with confirmation)
ai-review config --reset

# Show help for config command
ai-review config --help
```

### Configuration Wizard Flow

The interactive wizard guides you through:

1. **AI Provider Selection** - Choose between OpenAI, Claude, or local LLM
2. **Model Selection** - Pick the best model for your needs
3. **API Key Configuration** - Secure API key setup
4. **Analysis Rules** - Configure strictness for each category
5. **Output Preferences** - Set default output format and verbosity
6. **Cache Settings** - Enable/configure caching for performance
7. **Project Detection** - Automatic project-specific recommendations

---

## Cache Command

**Usage:** `ai-review cache [options]`

Manage analysis cache for improved performance.

### Options

- `--info` - Show cache usage statistics and information
- `--clear` - Remove all cached analysis results
- `--optimize` - Clean up old entries and optimize storage

### Examples

```bash
# View cache statistics
ai-review cache --info

# Clear entire cache
ai-review cache --clear

# Optimize cache storage
ai-review cache --optimize

# Show cache help
ai-review cache --help
```

### Cache Information Output

The `--info` command shows:
- Total cached entries
- Cache size in MB
- Hit rate percentage
- Oldest and newest entries
- Cache location

---

## Doctor Command

**Usage:** `ai-review doctor`

Comprehensive system health check and diagnostics.

### What It Checks

1. **Environment Check**
   - Node.js version compatibility
   - Required dependencies

2. **Git Repository Check** 
   - Confirms current directory is a Git repository
   - Checks repository status

3. **Configuration Validation**
   - Loads and validates configuration
   - Checks for common issues

4. **API Connectivity Test**
   - Verifies API key configuration
   - Tests connection to AI services

5. **Cache System Check**
   - Validates cache directory permissions
   - Shows cache statistics

### Example Output

```
🩺 AI Code Review Health Check
Diagnosing system configuration and connectivity...

✓ Environment check
✓ Git repository check  
✗ Configuration validation
✓ API connectivity test
✓ Cache system check

❌ Issues detected. Please address the above problems.
💡 Run "ai-review config --wizard" to fix configuration issues.
```

---

## Exit Codes

AI Code Review uses standard exit codes:

- `0` - Success
- `1` - General error (configuration, file access, etc.)
- `2` - Invalid command line arguments
- `130` - Interrupted by user (Ctrl+C)

## Error Handling

### Common Error Scenarios

#### Configuration Errors
- **Missing config file** - Automatically suggests creating one
- **Invalid JSON syntax** - Points to specific line/character
- **Missing required fields** - Lists missing configuration

#### Git Repository Errors
- **Not a Git repository** - Suggests running `git init`
- **No staged files** - Suggests using `git add` or `--all` flag
- **No modified files** - Suggests making changes

#### API Errors
- **Missing API key** - Shows environment variable setup
- **Invalid API key** - Suggests key validation
- **Rate limit exceeded** - Recommends waiting or upgrading plan
- **Network issues** - Suggests connectivity troubleshooting

#### File System Errors  
- **Permission denied** - Suggests checking file permissions
- **Disk full** - Recommends cleaning up space
- **File not found** - Indicates file may have been deleted

### Error Recovery

Most errors include:
- **Clear description** of the problem
- **Specific suggestions** for resolution  
- **Command examples** to fix the issue
- **Links to documentation** when helpful

### Verbose Mode

Use `-v, --verbose` for detailed error information:
- Full error stack traces
- Performance timing data
- Cache hit/miss statistics
- API request/response details

## Integration Examples

### Package.json Scripts

```json
{
  "scripts": {
    "review": "ai-review",
    "review:quick": "ai-review quick", 
    "review:full": "ai-review analyze --mode full --output html",
    "review:check": "ai-review doctor"
  }
}
```

### Git Hooks

Pre-commit hook (`.git/hooks/pre-commit`):
```bash
#!/bin/sh
ai-review --mode quick
```

### CI/CD Integration

GitHub Actions example:
```yaml
- name: AI Code Review
  run: |
    npm install -g ai-review
    ai-review analyze --output json > review-results.json
```

### Shell Aliases

```bash
# Add to ~/.bashrc or ~/.zshrc
alias review="ai-review"
alias review-all="ai-review analyze --all --output html"
alias review-check="ai-review doctor"
```

## Performance Tips

### Faster Analysis
- Use `--mode quick` for rapid feedback
- Enable caching with `"cache": {"enabled": true}`
- Use `gpt-3.5-turbo` or `claude-3-haiku` for speed
- Add more patterns to `ignore` configuration

### Cost Optimization  
- Enable caching to avoid re-analyzing unchanged code
- Use `--staged` to analyze only committed changes
- Consider `loose` rules for non-critical categories
- Use faster/cheaper models for development

### Better Results
- Use `--mode full` for comprehensive analysis
- Enable `"detailed": true` in output configuration  
- Use `gpt-4` or `claude-3-opus` for best quality
- Set `strict` rules for security and logic categories

## Troubleshooting

For issues not covered here:

1. **Run diagnostics:** `ai-review doctor`
2. **Check configuration:** `ai-review config --validate`
3. **Clear cache:** `ai-review cache --clear`  
4. **Enable verbose mode:** `--verbose`
5. **Check documentation:** Visit GitHub repository
6. **Report bugs:** Create an issue on GitHub

---

*For more detailed information, see the [Configuration Reference](configuration.md) and [FAQ](faq.md).*