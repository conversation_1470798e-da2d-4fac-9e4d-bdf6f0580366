# Best Practices Guide

This guide provides recommendations for getting the most out of AI Code Review in your development workflow.

## 🚀 Getting Started Right

### Initial Setup

1. **Start with the wizard:** `ai-review config --wizard`
   - Let it detect your project type
   - Choose appropriate models for your needs
   - Configure rules based on your team standards

2. **Test with a small change first:**
   ```bash
   # Make a small change
   echo "console.log('test');" >> test.js
   git add test.js
   ai-review
   ```

3. **Validate your setup:** `ai-review doctor`
   - Ensures everything is working correctly
   - Identifies potential issues early

### Team Setup

1. **Commit configuration to repository:**
   ```bash
   # Create project-specific config
   ai-review config --template
   # Edit .ai-review.json
   git add .ai-review.json
   git commit -m "Add AI code review configuration"
   ```

2. **Document team standards:**
   - Include config rationale in README
   - Document any custom ignore patterns
   - Set expectations for review frequency

## 📋 Optimal Configuration

### Production-Ready Configuration

```json
{
  "aiProvider": "openai",
  "model": "gpt-4",
  "rules": {
    "logic": "strict",      // Critical for reliability
    "performance": "normal", // Balanced approach
    "security": "strict",   // Never compromise security
    "style": "normal",      // Team consistency
    "business": "strict"    // Domain accuracy
  },
  "output": {
    "format": "terminal",
    "detailed": true,
    "verbose": false
  },
  "cache": {
    "enabled": true,        // Essential for performance
    "ttl": 24
  },
  "ignore": [
    "node_modules/**",
    "*.min.js",
    "*.min.css",
    "dist/**",
    "build/**",
    "coverage/**",
    "*.test.js",            // Focus on production code
    "*.spec.ts"
  ]
}
```

### Development Configuration

```json
{
  "aiProvider": "openai",
  "model": "gpt-3.5-turbo",  // Faster for rapid iteration
  "rules": {
    "logic": "normal",       // Catch important issues
    "performance": "loose",  // Less critical during dev
    "security": "normal",    // Still important
    "style": "loose",        // Focus on functionality
    "business": "normal"
  },
  "cache": {
    "enabled": true,
    "ttl": 12               // Shorter cache for dev
  }
}
```

## 🔄 Workflow Integration

### Git Workflow Integration

#### Pre-commit Hook
```bash
#!/bin/sh
# .git/hooks/pre-commit

# Quick analysis before commit
if ! ai-review --mode quick; then
    echo "Code review failed. Fix issues before committing."
    exit 1
fi
```

#### Pre-push Hook
```bash
#!/bin/sh
# .git/hooks/pre-push

# Full analysis before pushing
ai-review analyze --mode full --output html
echo "Full analysis complete. Check reports/ for detailed results."
```

#### Branch-specific Analysis
```bash
# Analyze changes since main branch
git diff main...HEAD --name-only | xargs git add
ai-review analyze --all
```

### Package.json Scripts

```json
{
  "scripts": {
    "review": "ai-review",
    "review:quick": "ai-review quick",
    "review:full": "ai-review analyze --mode full --output html", 
    "review:security": "ai-review analyze --rules-security strict",
    "pre-commit": "ai-review quick",
    "pre-push": "ai-review analyze --mode full"
  }
}
```

### IDE Integration

#### VSCode Tasks
```json
// .vscode/tasks.json
{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "AI Code Review",
      "type": "shell",
      "command": "ai-review",
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared"
      }
    },
    {
      "label": "AI Code Review - Full",
      "type": "shell", 
      "command": "ai-review analyze --mode full --output html",
      "group": "build"
    }
  ]
}
```

## ⚡ Performance Optimization

### Caching Strategy

1. **Always enable caching:**
   ```json
   {"cache": {"enabled": true, "ttl": 24}}
   ```

2. **Monitor cache effectiveness:**
   ```bash
   ai-review cache --info
   # Look for >70% hit rate
   ```

3. **Optimize cache periodically:**
   ```bash
   # Weekly cleanup
   ai-review cache --optimize
   ```

### Model Selection by Use Case

| Use Case | Recommended Model | Reason |
|----------|------------------|--------|
| **Daily Development** | `gpt-3.5-turbo` | Fast, cost-effective |
| **Code Reviews** | `gpt-4` | Most accurate |
| **Security Audits** | `claude-3-opus` | Excellent security analysis |
| **Large Codebases** | `claude-3-sonnet` | Good balance |
| **Cost Optimization** | `claude-3-haiku` | Most economical |

### File Selection Strategy

```bash
# Analyze only what's needed
git add src/                    # Add only source files
ai-review --staged              # Default behavior

# For comprehensive review
ai-review analyze --all         # All modified files

# For specific components
git add src/components/Button.tsx
ai-review                       # Just the component
```

## 🎯 Analysis Quality

### Getting Better Results

1. **Provide sufficient context:**
   ```bash
   # Good: Include related files
   git add src/utils/validation.ts src/components/Form.tsx
   
   # Less effective: Single file without context
   git add src/components/Form.tsx
   ```

2. **Use descriptive commit messages:**
   ```bash
   # AI can use commit context for better analysis
   git commit -m "Add email validation with regex patterns"
   ```

3. **Stage complete logical units:**
   ```bash
   # Good: Complete feature
   git add src/auth/ tests/auth/
   
   # Less effective: Partial changes
   git add src/auth/login.ts  # Missing related files
   ```

### Rule Configuration Guidelines

#### Security-Critical Projects
```json
{
  "rules": {
    "logic": "strict",
    "security": "strict",     // Maximum security scrutiny
    "performance": "normal",  
    "style": "normal",
    "business": "strict"      // Domain accuracy critical
  }
}
```

#### Rapid Prototyping
```json
{
  "rules": {
    "logic": "normal",        // Catch obvious bugs
    "security": "normal",     // Basic security
    "performance": "loose",   // Speed over optimization
    "style": "loose",         // Focus on functionality
    "business": "loose"       // Flexible requirements
  }
}
```

#### Open Source Projects
```json
{
  "rules": {
    "logic": "strict",        // High quality standards
    "security": "strict",     // Public code needs security
    "performance": "normal",   
    "style": "strict",        // Consistent public code
    "business": "normal"
  }
}
```

## 🔧 Advanced Techniques

### Multi-Environment Configurations

Use environment variables for different contexts:

```bash
# Development
export AI_REVIEW_MODEL="gpt-3.5-turbo"
export AI_REVIEW_MODE="quick"

# Staging  
export AI_REVIEW_MODEL="gpt-4"
export AI_REVIEW_MODE="full"

# Production review
export AI_REVIEW_MODEL="gpt-4"
export AI_REVIEW_RULES_SECURITY="strict"
```

### Custom Analysis Workflows

#### Security-Focused Review
```bash
# Maximum security analysis
ai-review analyze \
  --mode full \
  --rules-security strict \
  --rules-logic strict \
  --output json > security-report.json
```

#### Performance Optimization Review
```bash
# Focus on performance issues
ai-review analyze \
  --mode full \
  --rules-performance strict \
  --output html
```

#### Pre-Release Review
```bash
# Comprehensive analysis before release
ai-review analyze \
  --mode full \
  --all \
  --output html \
  --verbose
```

### Batch Analysis

For large changes or refactoring:

```bash
# Analyze changes in batches
git diff --name-only HEAD~10 | head -5 | xargs git add
ai-review analyze --output json > batch1.json

git reset HEAD
git diff --name-only HEAD~10 | tail -n +6 | head -5 | xargs git add  
ai-review analyze --output json > batch2.json
```

## 📊 Quality Metrics

### Tracking Improvement

1. **Monitor issue trends:**
   ```bash
   # Generate monthly reports
   ai-review analyze --output json > "report-$(date +%Y-%m).json"
   ```

2. **Track fix rates:**
   - Count issues before/after fixes
   - Monitor recurring issue types
   - Measure time to resolution

3. **Team metrics:**
   - Issues per developer
   - Most common issue categories
   - Code quality trends over time

### Success Indicators

- **Reduced critical issues:** <5% critical/high severity
- **High cache hit rate:** >70% for optimal performance  
- **Fast analysis:** <30 seconds for typical changes
- **Team adoption:** All team members using consistently

## 🚨 Common Pitfalls

### What to Avoid

1. **Over-analysis:**
   ```bash
   # Don't analyze everything
   ai-review --all  # Only when necessary
   
   # Better: Focus on changes
   ai-review --staged  # Default behavior
   ```

2. **Wrong strictness levels:**
   ```json
   // Avoid: Too strict for development
   {"rules": {"style": "strict", "performance": "strict"}}
   
   // Better: Balanced approach
   {"rules": {"style": "normal", "performance": "normal"}}
   ```

3. **Ignoring cache:**
   ```json
   // Don't disable cache unnecessarily
   {"cache": {"enabled": false}}  // Slow and expensive
   
   // Better: Use cache effectively
   {"cache": {"enabled": true, "ttl": 24}}
   ```

4. **Not configuring ignore patterns:**
   ```json
   // Missing ignore patterns = wasted analysis
   {"ignore": []}
   
   // Better: Exclude irrelevant files  
   {"ignore": ["node_modules/**", "*.min.js", "dist/**"]}
   ```

### Troubleshooting Common Issues

#### Slow Performance
1. Check cache hit rate: `ai-review cache --info`
2. Use faster model for development: `gpt-3.5-turbo`
3. Reduce file scope with better ignore patterns
4. Use `--mode quick` for rapid feedback

#### High Costs  
1. Enable caching to reduce API calls
2. Use cheaper models: `gpt-3.5-turbo`, `claude-3-haiku`
3. Analyze staged files only (default)
4. Set appropriate rule strictness

#### Poor Analysis Quality
1. Provide more context (stage related files)
2. Use higher-quality models: `gpt-4`, `claude-3-opus`
3. Enable detailed output: `"detailed": true`
4. Use appropriate strictness levels

## 🎉 Success Stories

### Team Integration Success

**Before:** Manual code reviews taking 2-3 days, inconsistent quality standards

**After:** 
- AI pre-review catches 80% of issues instantly
- Human reviewers focus on architecture and business logic  
- Review cycle reduced to same-day turnaround
- Consistent quality standards across team

### Performance Improvement

**Configuration:**
```json
{
  "cache": {"enabled": true, "ttl": 24},
  "model": "gpt-3.5-turbo",
  "rules": {"style": "loose", "performance": "normal"}
}
```

**Results:**
- Analysis time: 60s → 8s (87% reduction)
- API costs: $50/month → $12/month (76% reduction)  
- Team satisfaction: Significantly improved

---

*Remember: AI Code Review is a tool to enhance, not replace, human judgment. Use it to catch common issues and free up mental capacity for creative problem-solving and architectural decisions.*