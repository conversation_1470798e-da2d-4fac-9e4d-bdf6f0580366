# Extension Development Guide

This guide covers how to extend AI Code Review with custom providers, formatters, and other enhancements.

## 🎯 Extension Overview

AI Code Review is designed with extensibility in mind. You can extend the system in several ways:

- **AI Providers:** Add support for new AI services
- **Output Formatters:** Create custom report formats
- **File Type Support:** Add new programming languages
- **Analysis Rules:** Implement custom analysis logic
- **CLI Commands:** Add new command-line functionality

## 🤖 Adding AI Providers

### Provider Interface

All AI providers must implement the `AIProvider` interface:

```typescript
interface AIProvider {
  name: string;
  supportsModel(model: string): boolean;
  analyzeCode(prompt: string, options: AnalysisOptions): Promise<string>;
  validateApiKey(): Promise<boolean>;
  getCostEstimate(prompt: string): number;
}
```

### Example: Custom AI Provider

Let's create a provider for Hugging Face models:

```typescript
// src/ai/providers/huggingface.ts
import { AIProvider, AnalysisOptions } from '../../types';
import { logger } from '../../utils/logger';

export class HuggingFaceProvider implements AIProvider {
  name = 'huggingface';
  private apiKey: string;
  private baseUrl = 'https://api-inference.huggingface.co/models/';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  supportsModel(model: string): boolean {
    const supportedModels = [
      'microsoft/CodeBERT-base',
      'microsoft/codebert-base-mlm',
      'huggingface/CodeBERTa-small-v1'
    ];
    return supportedModels.includes(model);
  }

  async analyzeCode(prompt: string, options: AnalysisOptions): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}${options.model}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            max_new_tokens: 1000,
            temperature: 0.7,
            return_full_text: false
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return this.formatResponse(result);
    } catch (error) {
      logger.error(`HuggingFace API error: ${error}`);
      throw error;
    }
  }

  async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch('https://huggingface.co/api/whoami', {
        headers: { 'Authorization': `Bearer ${this.apiKey}` }
      });
      return response.ok;
    } catch (error) {
      logger.warn('HuggingFace API key validation failed:', error);
      return false;
    }
  }

  getCostEstimate(prompt: string): number {
    // HuggingFace Inference API is often free for smaller models
    const tokens = prompt.length / 4; // Rough estimate
    return tokens < 1000 ? 0 : tokens * 0.0001; // Small cost for large prompts
  }

  private formatResponse(response: any): string {
    // Format the response according to your needs
    if (Array.isArray(response) && response[0]?.generated_text) {
      return response[0].generated_text;
    }
    return JSON.stringify(response);
  }
}
```

### Register the Provider

Add your provider to the AI manager:

```typescript
// src/ai/manager.ts
import { HuggingFaceProvider } from './providers/huggingface';

const PROVIDERS = {
  openai: new OpenAIProvider(),
  claude: new ClaudeProvider(),
  huggingface: new HuggingFaceProvider() // Add your provider
};

// Update configuration validation
const SUPPORTED_PROVIDERS = ['openai', 'claude', 'huggingface'] as const;
```

### Update Configuration Schema

```typescript
// src/config/schemas.ts
export const configSchema = {
  // ... existing schema
  aiProvider: {
    type: 'string',
    enum: ['openai', 'claude', 'huggingface'], // Add your provider
    required: true
  }
};

// Add default models for your provider
export const defaultConfig: AIReviewConfig = {
  // ... existing defaults
  model: 'gpt-4', // Keep existing default
  // Add provider-specific defaults in the CLI wizard
};
```

## 📊 Adding Output Formatters

### Formatter Interface

All formatters must implement the `ReportFormatter` interface:

```typescript
interface ReportFormatter {
  format: string;
  generateReport(issues: EnhancedIssue[], metadata: ReportMetadata): Promise<string>;
  supports(features: string[]): boolean;
}
```

### Example: Markdown Formatter

```typescript
// src/report/formatters/markdown.ts
import { ReportFormatter, EnhancedIssue, ReportMetadata } from '../../types';
import * as fs from 'fs-extra';
import * as path from 'path';

export class MarkdownFormatter implements ReportFormatter {
  format = 'markdown';

  supports(features: string[]): boolean {
    const supportedFeatures = ['tables', 'links', 'code-blocks'];
    return features.every(feature => supportedFeatures.includes(feature));
  }

  async generateReport(issues: EnhancedIssue[], metadata: ReportMetadata): Promise<string> {
    const markdown = this.buildMarkdown(issues, metadata);
    const outputPath = path.join(metadata.outputDir, 'ai-review-report.md');
    await fs.writeFile(outputPath, markdown);
    return outputPath;
  }

  private buildMarkdown(issues: EnhancedIssue[], metadata: ReportMetadata): string {
    let markdown = '';

    // Header
    markdown += `# AI Code Review Report\n\n`;
    markdown += `**Generated:** ${new Date().toISOString()}\n`;
    markdown += `**Project:** ${metadata.projectName}\n`;
    markdown += `**Files Analyzed:** ${metadata.filesAnalyzed}\n`;
    markdown += `**Total Issues:** ${issues.length}\n\n`;

    // Summary table
    markdown += this.buildSummaryTable(issues);

    // Issues by severity
    const severities = ['Critical', 'High', 'Medium', 'Low'];
    for (const severity of severities) {
      const severityIssues = issues.filter(issue => issue.severity === severity);
      if (severityIssues.length > 0) {
        markdown += this.buildSeveritySection(severity, severityIssues);
      }
    }

    return markdown;
  }

  private buildSummaryTable(issues: EnhancedIssue[]): string {
    const summary = this.calculateSummary(issues);
    
    return `## Summary

| Severity | Count | Percentage |
|----------|-------|------------|
| Critical | ${summary.critical} | ${summary.criticalPercent}% |
| High | ${summary.high} | ${summary.highPercent}% |
| Medium | ${summary.medium} | ${summary.mediumPercent}% |
| Low | ${summary.low} | ${summary.lowPercent}% |

`;
  }

  private buildSeveritySection(severity: string, issues: EnhancedIssue[]): string {
    let section = `## ${severity} Issues (${issues.length})\n\n`;

    issues.forEach((issue, index) => {
      section += `### ${index + 1}. ${issue.title}\n\n`;
      section += `**File:** \`${issue.filePath}\`\n`;
      section += `**Line:** ${issue.line}\n`;
      section += `**Type:** ${issue.type}\n\n`;
      section += `**Description:**\n${issue.description}\n\n`;
      
      if (issue.codeSnippet) {
        section += `**Code:**\n\`\`\`${this.getLanguage(issue.filePath)}\n`;
        section += `${issue.codeSnippet}\n\`\`\`\n\n`;
      }

      if (issue.fixSuggestions?.length > 0) {
        section += `**Suggested Fix:**\n`;
        issue.fixSuggestions.forEach(fix => {
          section += `- ${fix.description}\n`;
          if (fix.codeExample) {
            section += `  \`\`\`${this.getLanguage(issue.filePath)}\n`;
            section += `  ${fix.codeExample}\n  \`\`\`\n`;
          }
        });
        section += `\n`;
      }

      section += `---\n\n`;
    });

    return section;
  }

  private calculateSummary(issues: EnhancedIssue[]) {
    const total = issues.length;
    const critical = issues.filter(i => i.severity === 'Critical').length;
    const high = issues.filter(i => i.severity === 'High').length;
    const medium = issues.filter(i => i.severity === 'Medium').length;
    const low = issues.filter(i => i.severity === 'Low').length;

    return {
      critical,
      high,
      medium,
      low,
      criticalPercent: Math.round((critical / total) * 100),
      highPercent: Math.round((high / total) * 100),
      mediumPercent: Math.round((medium / total) * 100),
      lowPercent: Math.round((low / total) * 100)
    };
  }

  private getLanguage(filePath: string): string {
    const ext = path.extname(filePath);
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'jsx',
      '.ts': 'typescript',
      '.tsx': 'tsx',
      '.vue': 'vue',
      '.css': 'css',
      '.scss': 'scss',
      '.html': 'html',
      '.json': 'json'
    };
    return languageMap[ext] || 'text';
  }
}
```

### Register the Formatter

```typescript
// src/report/generator.ts
import { MarkdownFormatter } from './formatters/markdown';

const FORMATTERS = {
  terminal: new TerminalFormatter(),
  html: new HTMLFormatter(),
  json: new JSONFormatter(),
  markdown: new MarkdownFormatter() // Add your formatter
};
```

## 🌍 Adding Language Support

### 1. Update File Type Detection

```typescript
// src/git/scanner.ts
private supportedExtensions = new Set([
  '.js', '.jsx', '.ts', '.tsx',
  '.vue', '.svelte',
  '.css', '.scss', '.sass', '.less',
  '.html', '.htm', '.json',
  // Add new languages
  '.py',           // Python
  '.java',         // Java
  '.go',           // Go
  '.rs',           // Rust
  '.cpp', '.cc',   // C++
  '.c',            // C
  '.php',          // PHP
  '.rb',           // Ruby
  '.swift',        // Swift
  '.kt',           // Kotlin
  '.scala',        // Scala
  '.clj',          // Clojure
]);
```

### 2. Add Language-Specific Analysis Prompts

```typescript
// src/core/analyzer.ts
private getLanguageSpecificPrompt(filePath: string, content: string): string {
  const extension = path.extname(filePath);
  
  const languagePrompts = {
    '.py': this.getPythonPrompt(content),
    '.java': this.getJavaPrompt(content),
    '.go': this.getGoPrompt(content),
    '.rs': this.getRustPrompt(content),
    '.cpp': this.getCppPrompt(content),
    '.php': this.getPhpPrompt(content)
  };

  return languagePrompts[extension] || this.getGenericPrompt(content);
}

private getPythonPrompt(content: string): string {
  return `
Analyze this Python code for potential issues:

1. **Syntax and Logic Issues:**
   - Indentation problems
   - Variable scope issues
   - Exception handling
   - Type hints usage

2. **Python-Specific Issues:**
   - PEP 8 compliance
   - Pythonic idioms
   - Import organization
   - Docstring quality

3. **Performance Issues:**
   - List comprehensions vs loops
   - Generator expressions
   - Memory efficiency

4. **Security Issues:**
   - SQL injection (if using raw SQL)
   - Input validation
   - Pickle usage
   - eval/exec usage

Code to analyze:
\`\`\`python
${content}
\`\`\`

Provide specific, actionable feedback with code examples where helpful.
`;
}

private getJavaPrompt(content: string): string {
  return `
Analyze this Java code for potential issues:

1. **Java Best Practices:**
   - Proper exception handling
   - Resource management (try-with-resources)
   - Null pointer safety
   - Generics usage

2. **Object-Oriented Design:**
   - Encapsulation
   - Inheritance usage
   - Interface implementation
   - Design patterns

3. **Performance:**
   - Collection choice
   - StringBuilder vs concatenation
   - Boxing/unboxing
   - Memory leaks

4. **Security:**
   - Input validation
   - SQL injection prevention
   - Serialization safety

Code to analyze:
\`\`\`java
${content}
\`\`\`

Focus on maintainability, performance, and security.
`;
}
```

### 3. Language-Specific Configuration

```typescript
// src/config/schemas.ts
interface LanguageRules {
  [language: string]: {
    style?: 'strict' | 'normal' | 'loose';
    performance?: 'strict' | 'normal' | 'loose';
    security?: 'strict' | 'normal' | 'loose';
    idioms?: 'strict' | 'normal' | 'loose'; // Language-specific patterns
  };
}

interface AIReviewConfig {
  // ... existing config
  languageRules?: LanguageRules;
}

// Example language-specific rules
const exampleConfig = {
  languageRules: {
    python: {
      style: 'strict',      // PEP 8 compliance
      idioms: 'strict',     // Pythonic patterns
      security: 'strict'    // Python security issues
    },
    java: {
      style: 'normal',      // Standard Java conventions
      performance: 'strict', // JVM performance issues
      security: 'strict'    // Java security patterns
    }
  }
};
```

## 🔧 Adding Custom Analysis Rules

### Rule Interface

```typescript
interface AnalysisRule {
  name: string;
  description: string;
  category: 'logic' | 'performance' | 'security' | 'style' | 'business';
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  applies(filePath: string, content: string): boolean;
  analyze(content: string, context: AnalysisContext): Promise<Issue[]>;
}
```

### Example: Custom Security Rule

```typescript
// src/core/rules/sqlInjectionRule.ts
import { AnalysisRule, Issue, AnalysisContext } from '../../types';

export class SqlInjectionRule implements AnalysisRule {
  name = 'sql-injection-detection';
  description = 'Detects potential SQL injection vulnerabilities';
  category = 'security' as const;
  severity = 'Critical' as const;

  applies(filePath: string, content: string): boolean {
    // Apply to JavaScript/TypeScript files that might use SQL
    const applicableExtensions = ['.js', '.jsx', '.ts', '.tsx'];
    const hasExtension = applicableExtensions.some(ext => filePath.endsWith(ext));
    const hasSqlKeywords = /\b(SELECT|INSERT|UPDATE|DELETE|CREATE|DROP)\b/i.test(content);
    return hasExtension && hasSqlKeywords;
  }

  async analyze(content: string, context: AnalysisContext): Promise<Issue[]> {
    const issues: Issue[] = [];
    const lines = content.split('\n');

    // Pattern for potential SQL injection
    const dangerousPatterns = [
      /query\s*\+=?\s*['"`][^'"`]*['"`]\s*\+\s*\w+/gi,  // String concatenation
      /\$\{[^}]*\}/g,                                    // Template literals
      /execute\([^)]*\+[^)]*\)/gi,                      // Direct concatenation in execute
    ];

    lines.forEach((line, index) => {
      dangerousPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          issues.push({
            type: 'security',
            title: 'Potential SQL Injection',
            description: 'Dynamic SQL query construction detected. This may lead to SQL injection vulnerabilities.',
            line: index + 1,
            severity: this.severity,
            codeSnippet: line.trim(),
            suggestions: [
              'Use parameterized queries or prepared statements',
              'Validate and sanitize all user input',
              'Use an ORM that handles SQL injection prevention'
            ]
          });
        }
      });
    });

    return issues;
  }
}
```

### Register Custom Rules

```typescript
// src/core/analyzer.ts
import { SqlInjectionRule } from './rules/sqlInjectionRule';

export class AnalysisEngine {
  private customRules: AnalysisRule[] = [
    new SqlInjectionRule(),
    // Add more custom rules here
  ];

  private async runCustomRules(content: string, filePath: string): Promise<Issue[]> {
    const allIssues: Issue[] = [];

    for (const rule of this.customRules) {
      if (rule.applies(filePath, content)) {
        const issues = await rule.analyze(content, { filePath });
        allIssues.push(...issues);
      }
    }

    return allIssues;
  }
}
```

## 🖥 Adding CLI Commands

### Command Structure

```typescript
// src/cli/commands/newCommand.ts
import { Command } from 'commander';
import { logger } from '../../utils/logger';

export function createNewCommand(): Command {
  return new Command('new-command')
    .description('Description of your new command')
    .option('-o, --option <value>', 'Command option description')
    .option('--flag', 'Boolean flag description')
    .action(async (options) => {
      try {
        await handleNewCommand(options);
      } catch (error) {
        logger.error('Command failed:', error);
        process.exit(1);
      }
    })
    .addHelpText('after', getCommandHelpText());
}

async function handleNewCommand(options: any): Promise<void> {
  console.log('Executing new command with options:', options);
  
  // Your command implementation here
  
  console.log('Command completed successfully!');
}

function getCommandHelpText(): string {
  return `
Examples:
  ai-review new-command --option value
  ai-review new-command --flag

For more information, visit: https://github.com/ai-code-review/ai-code-review
`;
}
```

### Register Command in CLI

```typescript
// src/cli.ts
import { createNewCommand } from './cli/commands/newCommand';

class AIReviewCLI {
  private setupCommands(): void {
    // ... existing commands
    
    // Add your new command
    this.program.addCommand(createNewCommand());
  }
}
```

## 🔌 Plugin System (Future)

### Plugin Interface (Planned)

```typescript
interface Plugin {
  name: string;
  version: string;
  description: string;
  
  // Lifecycle hooks
  initialize?(context: PluginContext): Promise<void>;
  beforeAnalysis?(files: string[]): Promise<void>;
  afterAnalysis?(results: AnalysisResult[]): Promise<void>;
  cleanup?(): Promise<void>;
  
  // Extension points
  providers?: AIProvider[];
  formatters?: ReportFormatter[];
  rules?: AnalysisRule[];
  commands?: Command[];
}

// Example plugin
export class CustomPlugin implements Plugin {
  name = 'custom-analysis-plugin';
  version = '1.0.0';
  description = 'Adds custom analysis capabilities';
  
  providers = [new CustomAIProvider()];
  formatters = [new CustomFormatter()];
  rules = [new CustomRule()];
  
  async initialize(context: PluginContext): Promise<void> {
    context.logger.info('Custom plugin initialized');
  }
}
```

## 📦 Package and Distribution

### Creating an Extension Package

1. **Package Structure:**
   ```
   my-ai-review-extension/
   ├── package.json
   ├── src/
   │   ├── index.ts
   │   ├── providers/
   │   ├── formatters/
   │   └── rules/
   ├── dist/
   ├── README.md
   └── tests/
   ```

2. **Package.json:**
   ```json
   {
     "name": "ai-review-custom-extension",
     "version": "1.0.0",
     "description": "Custom extension for AI Code Review",
     "main": "dist/index.js",
     "types": "dist/index.d.ts",
     "keywords": ["ai-code-review", "extension", "plugin"],
     "peerDependencies": {
       "ai-code-review": "^1.0.0"
     },
     "devDependencies": {
       "ai-code-review": "^1.0.0",
       "typescript": "^5.0.0"
     }
   }
   ```

3. **Main Export:**
   ```typescript
   // src/index.ts
   export { CustomAIProvider } from './providers/custom';
   export { CustomFormatter } from './formatters/custom';
   export { CustomRule } from './rules/custom';
   
   // Extension metadata
   export const extension = {
     name: 'custom-extension',
     version: '1.0.0',
     providers: ['custom'],
     formatters: ['custom'],
     rules: ['custom-rule']
   };
   ```

### Publishing Extension

```bash
# Build the extension
npm run build

# Test with AI Code Review
npm link
cd /path/to/ai-code-review
npm link your-extension-name

# Publish to npm
npm publish
```

## 🧪 Testing Extensions

### Unit Testing

```typescript
// tests/providers/custom.test.ts
import { CustomAIProvider } from '../src/providers/custom';

describe('CustomAIProvider', () => {
  let provider: CustomAIProvider;
  
  beforeEach(() => {
    provider = new CustomAIProvider('test-api-key');
  });
  
  it('should support correct models', () => {
    expect(provider.supportsModel('custom-model-1')).toBe(true);
    expect(provider.supportsModel('unsupported-model')).toBe(false);
  });
  
  it('should analyze code correctly', async () => {
    const mockResponse = 'Mock analysis result';
    jest.spyOn(provider, 'analyzeCode').mockResolvedValue(mockResponse);
    
    const result = await provider.analyzeCode('test prompt', {});
    expect(result).toBe(mockResponse);
  });
});
```

### Integration Testing

```typescript
// tests/integration/extension.test.ts
import { AIReviewCLI } from 'ai-code-review/dist/cli';
import { CustomAIProvider } from '../src/providers/custom';

describe('Extension Integration', () => {
  it('should integrate with main CLI', async () => {
    // Test that your extension works with the main CLI
    const cli = new AIReviewCLI();
    
    // Mock or configure to use your extension
    const result = await cli.analyze({
      provider: 'custom',
      mode: 'full'
    });
    
    expect(result).toBeDefined();
  });
});
```

## 📚 Documentation

### Extension Documentation Template

```markdown
# Custom AI Code Review Extension

Description of your extension and its purpose.

## Installation

```bash
npm install -g ai-review-custom-extension
```

## Configuration

Add to your `.ai-review.json`:

```json
{
  "extensions": ["custom-extension"],
  "aiProvider": "custom-provider",
  "customOptions": {
    "option1": "value1"
  }
}
```

## Features

- Feature 1 description
- Feature 2 description
- Feature 3 description

## API Reference

Document your extension's API here.

## Examples

Provide usage examples.

## Contributing

How others can contribute to your extension.
```

## 🚀 Best Practices

### 1. Error Handling
- Always handle errors gracefully
- Provide meaningful error messages
- Use the built-in error handling system

### 2. Performance
- Cache expensive operations
- Use async/await properly
- Avoid blocking the event loop

### 3. Compatibility
- Follow semantic versioning
- Test with multiple AI Code Review versions
- Document breaking changes

### 4. Security
- Validate all inputs
- Handle API keys securely
- Don't log sensitive information

### 5. User Experience
- Provide helpful documentation
- Include usage examples
- Offer good default configurations

---

This guide covers the main extension points in AI Code Review. For specific questions or help with your extension, please open a GitHub issue or discussion.

Happy extending! 🎉