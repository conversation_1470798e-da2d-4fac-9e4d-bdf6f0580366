# Architecture Documentation

This document provides a comprehensive overview of the AI Code Review tool architecture, design decisions, and module interfaces.

## 🏗 System Overview

AI Code Review is a modular CLI tool that integrates Git workflow with AI-powered code analysis. The system follows a layered architecture pattern with clear separation of concerns.

```
┌─────────────────────────────────────────────────────────────┐
│                        CLI Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ Command     │ │ Progress    │ │ Interactive Wizard      ││
│  │ Parser      │ │ Display     │ │ Configuration          ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Core Business Logic                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ Analysis    │ │ Issue       │ │ Configuration          ││
│  │ Engine      │ │ Classifier  │ │ Manager                ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    Service Layer                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ AI          │ │ Git         │ │ Report                 ││
│  │ Providers   │ │ Scanner     │ │ Generators             ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │ File        │ │ Cache       │ │ Error Handling &       ││
│  │ System      │ │ Manager     │ │ Logging                ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Core Design Principles

### 1. Modularity
- Each module has a single, well-defined responsibility
- Minimal coupling between modules
- Clear interfaces for easy testing and extension

### 2. Extensibility  
- Plugin-style architecture for AI providers
- Configurable analysis rules and formatters
- Easy addition of new file types and languages

### 3. Performance
- Intelligent caching system
- Lazy loading of heavy dependencies
- Parallel processing where possible

### 4. Reliability
- Comprehensive error handling and recovery
- Retry mechanisms for transient failures
- Graceful degradation when services are unavailable

### 5. User Experience
- Rich CLI with progress indicators
- Helpful error messages with suggestions
- Interactive configuration wizard

## 📁 Directory Structure

```
src/
├── cli.ts                     # Main CLI entry point
├── types/                     # TypeScript type definitions
│   └── index.ts              # Core interfaces and types
├── config/                    # Configuration management
│   ├── manager.ts            # Configuration loading and validation
│   └── schemas.ts            # Configuration schemas and defaults
├── core/                      # Core business logic
│   ├── analyzer.ts           # Main analysis engine
│   ├── classifier.ts         # Issue classification
│   ├── suggestions.ts        # Fix suggestion generator  
│   └── cache.ts              # Caching system
├── git/                       # Git integration
│   └── scanner.ts            # Git change detection and parsing
├── ai/                        # AI provider abstractions
│   ├── manager.ts            # AI provider coordination
│   └── providers/            # Individual provider implementations
│       ├── openai.ts         # OpenAI GPT integration
│       ├── claude.ts         # Anthropic Claude integration
│       └── local.ts          # Local LLM interface
├── report/                    # Report generation
│   ├── generator.ts          # Report coordination
│   └── formatters/           # Output format implementations
│       ├── terminal.ts       # Console output
│       ├── html.ts           # HTML report generation
│       └── json.ts           # JSON export
├── cli/                       # CLI components
│   ├── wizard.ts             # Interactive configuration
│   └── progress.ts           # Progress indicators and dialogs
└── utils/                     # Utilities and helpers
    ├── logger.ts             # Logging system
    ├── errorHandler.ts       # Error handling and recovery
    └── performance.ts        # Performance monitoring
```

## 🔧 Core Modules

### CLI Layer (`src/cli.ts`)

**Purpose:** Main entry point and command-line interface

**Key Responsibilities:**
- Command parsing using Commander.js
- User interaction and feedback
- Orchestrating analysis workflow
- Error handling and user guidance

**Key Methods:**
```typescript
class AIReviewCLI {
  private setupCommands(): void              // Configure CLI commands
  private runAnalysis(options: CLIOptions): Promise<void>    // Execute analysis
  private checkFirstRun(): Promise<void>     // First-time setup detection
  private runDoctorCheck(): Promise<void>    // System diagnostics
}
```

### Configuration Manager (`src/config/manager.ts`)

**Purpose:** Handle configuration loading, validation, and management

**Key Features:**
- Hierarchical configuration (project > global > defaults)
- Environment variable integration
- Advanced validation with auto-fixing
- Project-specific recommendations

**Key Methods:**
```typescript
class ConfigManager {
  loadConfig(customPath?: string): Promise<AIReviewConfig>
  saveConfig(config: AIReviewConfig, filePath?: string): Promise<void>
  validateConfigurationAdvanced(): Promise<ValidationResult>
  generateProjectSpecificConfig(): Promise<ProjectConfig>
  autoFixConfiguration(config: AIReviewConfig): Promise<FixResult>
}
```

### Analysis Engine (`src/core/analyzer.ts`)

**Purpose:** Core AI analysis orchestration

**Key Responsibilities:**
- Coordinate AI provider interactions
- Manage analysis prompts and context
- Handle caching and optimization
- Aggregate and format results

**Key Methods:**
```typescript
class AnalysisEngine {
  analyzeChanges(changes: GitFileChange[]): Promise<AnalysisResult[]>
  analyzeFile(file: string, changes: GitChange[]): Promise<AnalysisResult>
  private buildPrompt(file: string, changes: GitChange[]): string
  cleanup(): Promise<void>
}
```

### Git Scanner (`src/git/scanner.ts`)

**Purpose:** Git integration and change detection

**Key Features:**
- Staged and unstaged file detection
- Diff parsing and change extraction
- File type filtering
- Blame information retrieval

**Key Methods:**
```typescript
class GitScanner {
  getStagedFiles(): Promise<GitFileChange[]>
  getAllModifiedFiles(): Promise<GitFileChange[]>
  getUnstagedFiles(): Promise<GitFileChange[]>
  private parseDiffResult(diffContent: string): GitChange[]
}
```

### AI Provider System (`src/ai/`)

**Purpose:** Abstract AI provider integration

**Architecture:** Strategy pattern with provider-specific implementations

**Provider Interface:**
```typescript
interface AIProvider {
  name: string;
  supportsModel(model: string): boolean;
  analyzeCode(prompt: string, options: AnalysisOptions): Promise<string>;
  validateApiKey(): Promise<boolean>;
  getCostEstimate(prompt: string): number;
}
```

**Implementations:**
- **OpenAI Provider:** GPT-3.5, GPT-4 integration
- **Claude Provider:** Claude-3 models integration  
- **Local Provider:** Local LLM interface (future)

### Report Generation (`src/report/`)

**Purpose:** Multi-format output generation

**Architecture:** Strategy pattern with formatter-specific implementations

**Formatter Interface:**
```typescript
interface ReportFormatter {
  format: 'terminal' | 'html' | 'json';
  generateReport(issues: EnhancedIssue[], summary: AnalysisSummary): Promise<string>;
  supports(features: string[]): boolean;
}
```

**Implementations:**
- **Terminal Formatter:** Colorized console output with progress
- **HTML Formatter:** Interactive web reports with charts
- **JSON Formatter:** Structured data export

### Cache System (`src/core/cache.ts`)

**Purpose:** Intelligent analysis result caching

**Key Features:**
- Content-based hashing
- Automatic expiration (TTL)
- Cache statistics and optimization
- Concurrent access handling

**Methods:**
```typescript
class CacheManager {
  get(key: string): Promise<T | null>
  set(key: string, value: T, ttl?: number): Promise<void>
  invalidate(pattern: string): Promise<void>
  getCacheStats(): Promise<CacheStats>
  optimizeCache(): Promise<void>
}
```

## 🔄 Data Flow

### Analysis Workflow

```mermaid
graph TB
    A[CLI Command] --> B[Load Configuration]
    B --> C[Scan Git Changes]
    C --> D[Filter Supported Files]
    D --> E[Check Cache]
    E --> F{Cache Hit?}
    F -->|Yes| G[Load Cached Results]
    F -->|No| H[AI Analysis]
    H --> I[Cache Results]
    I --> J[Classify Issues]
    G --> J
    J --> K[Generate Suggestions]
    K --> L[Format Output]
    L --> M[Display Results]
```

### Configuration Loading

```mermaid
graph LR
    A[Start] --> B[Check Custom Path]
    B --> C{Custom Config?}
    C -->|Yes| D[Load Custom]
    C -->|No| E[Find Project Config]
    E --> F{Project Config?}
    F -->|Yes| G[Load Project]
    F -->|No| H[Load Global Config]
    D --> I[Validate & Merge]
    G --> I
    H --> I
    I --> J[Apply Environment Variables]
    J --> K[Configuration Ready]
```

## 🔌 Extension Points

### Adding New AI Providers

1. **Create provider class:**
```typescript
// src/ai/providers/custom.ts
export class CustomProvider implements AIProvider {
  name = 'custom';
  
  supportsModel(model: string): boolean {
    return ['custom-model-1', 'custom-model-2'].includes(model);
  }
  
  async analyzeCode(prompt: string, options: AnalysisOptions): Promise<string> {
    // Provider-specific implementation
  }
}
```

2. **Register provider:**
```typescript
// src/ai/manager.ts
import { CustomProvider } from './providers/custom';

const PROVIDERS = {
  openai: new OpenAIProvider(),
  claude: new ClaudeProvider(),
  custom: new CustomProvider()  // Add here
};
```

### Adding New Output Formats

1. **Create formatter:**
```typescript
// src/report/formatters/custom.ts
export class CustomFormatter implements ReportFormatter {
  format: 'custom' = 'custom';
  
  async generateReport(issues: EnhancedIssue[]): Promise<string> {
    // Custom format implementation
  }
}
```

2. **Register formatter:**
```typescript
// src/report/generator.ts
import { CustomFormatter } from './formatters/custom';

const FORMATTERS = {
  terminal: new TerminalFormatter(),
  html: new HTMLFormatter(),
  json: new JSONFormatter(),
  custom: new CustomFormatter()  // Add here
};
```

### Adding New File Types

1. **Update supported extensions:**
```typescript
// src/git/scanner.ts
private supportedExtensions = new Set([
  '.js', '.jsx', '.ts', '.tsx',
  '.vue', '.svelte',
  '.py',    // Add Python
  '.java',  // Add Java
  '.go'     // Add Go
]);
```

2. **Add language-specific prompts:**
```typescript
// src/core/analyzer.ts
private getLanguageSpecificPrompt(fileExtension: string): string {
  const prompts = {
    '.py': 'Analyze this Python code...',
    '.java': 'Review this Java code...',
    '.go': 'Examine this Go code...'
  };
  return prompts[fileExtension] || this.defaultPrompt;
}
```

## 🔧 Key Design Decisions

### 1. TypeScript Strict Mode
**Decision:** Enable strict mode with exact optional properties
**Rationale:** Catch type errors early, ensure robust interfaces
**Trade-off:** More verbose code, but fewer runtime errors

### 2. Singleton Configuration Manager
**Decision:** Use singleton pattern for configuration management
**Rationale:** Single source of truth, easy testing and mocking
**Alternative:** Dependency injection (considered but rejected for simplicity)

### 3. Content-Based Caching
**Decision:** Use file content hash for cache keys
**Rationale:** Accurate cache invalidation, handles file renames
**Alternative:** Timestamp-based (rejected due to accuracy issues)

### 4. Strategy Pattern for Providers/Formatters
**Decision:** Use strategy pattern for extensibility  
**Rationale:** Easy to add new providers/formats without core changes
**Benefits:** Clean interfaces, testable in isolation

### 5. Commander.js for CLI
**Decision:** Use Commander.js over alternatives (yargs, oclif)
**Rationale:** Mature, lightweight, good TypeScript support
**Trade-off:** Less powerful than oclif but simpler to use

## 🧪 Testing Architecture

### Test Organization

```
tests/
├── unit/                      # Unit tests for individual modules
│   ├── config/
│   ├── core/
│   ├── git/
│   └── utils/
├── integration/               # Integration tests
│   ├── e2e-pipeline.test.ts  # End-to-end workflow tests
│   └── ai-providers.test.ts  # AI provider integration
└── fixtures/                  # Test data and mock files
    ├── sample-configs/
    ├── code-samples/
    └── git-repos/
```

### Testing Strategies

1. **Unit Tests:** Individual module functionality
2. **Integration Tests:** Module interaction and workflows
3. **E2E Tests:** Complete CLI workflows with real Git repos
4. **Mock Tests:** AI provider responses for consistent testing

## 🔍 Performance Considerations

### 1. Lazy Loading
Heavy dependencies (AI SDKs) are loaded only when needed:

```typescript
async loadAIProvider(provider: string) {
  switch (provider) {
    case 'openai':
      const { OpenAI } = await import('openai');
      return new OpenAI();
    case 'claude':
      const { Anthropic } = await import('@anthropic-ai/sdk');
      return new Anthropic();
  }
}
```

### 2. Parallel Processing
Multiple files analyzed concurrently where possible:

```typescript
const results = await Promise.all(
  files.map(file => this.analyzeFile(file))
);
```

### 3. Memory Management
- Stream large files instead of loading entirely into memory
- Clear analysis results after processing
- Garbage collection hints for large objects

### 4. Cache Optimization
- LRU eviction for memory cache
- Disk-based persistence for large caches
- Periodic cleanup of expired entries

## 🔐 Security Considerations

### 1. API Key Management
- Never log API keys
- Environment variable preference over config files
- Secure storage recommendations in documentation

### 2. Code Privacy
- Only send changed lines + minimal context to AI
- No persistent storage on AI provider servers
- Local caching with appropriate permissions

### 3. Input Validation
- Sanitize all user inputs
- Validate configuration files thoroughly
- Safe handling of Git command output

## 🚀 Performance Metrics

### Key Performance Indicators

1. **Analysis Speed**
   - Target: <30 seconds for typical changes
   - Measurement: End-to-end analysis time

2. **Cache Hit Rate**
   - Target: >70% for optimal performance
   - Measurement: Cache hits vs misses

3. **Memory Usage**
   - Target: <200MB peak memory usage
   - Measurement: Process memory monitoring

4. **API Cost Efficiency**
   - Target: <$0.50 per file analysis
   - Measurement: Token usage tracking

## 🔮 Future Architecture Considerations

### Planned Improvements

1. **Microservice Architecture:** Split into smaller, focused services
2. **Plugin System:** Dynamic plugin loading for extensions
3. **Distributed Caching:** Redis/shared cache for team usage
4. **Webhook Integration:** Real-time analysis triggers
5. **GraphQL API:** Programmatic access to analysis functions

### Scalability Concerns

1. **Concurrent Analysis:** Queue system for high-volume usage
2. **Cache Storage:** Distributed cache for large teams
3. **API Rate Limiting:** Smart backoff and queuing
4. **Resource Management:** CPU and memory usage optimization

---

*This architecture documentation is maintained alongside the codebase. Please update it when making significant architectural changes.*