# Documentation Index

Welcome to the AI Code Review documentation! This guide will help you find the information you need.

## 📖 User Documentation

### Getting Started
- **[README](../README.md)** - Quick start and overview
- **[CLI Reference](cli-reference.md)** - Complete command-line documentation
- **[Configuration](configuration.md)** - Detailed configuration options
- **[Best Practices](best-practices.md)** - Optimization and workflow tips
- **[FAQ](faq.md)** - Frequently asked questions

### Installation & Setup
- **Installation:** See [README - Installation](../README.md#-installation)
- **Configuration Wizard:** `ai-review config --wizard`
- **First Run:** Follow the interactive setup
- **Health Check:** `ai-review doctor`

### Common Tasks
- **Basic Usage:** `ai-review` (analyze staged files)
- **Full Analysis:** `ai-review analyze --mode full --output html`
- **Configuration Management:** `ai-review config --show | --validate | --reset`
- **Cache Management:** `ai-review cache --info | --clear | --optimize`
- **Troubleshooting:** `ai-review doctor`

## 🛠 Developer Documentation

### Architecture & Design
- **[Architecture](architecture.md)** - System design and module structure
- **[Contributing Guide](../CONTRIBUTING.md)** - How to contribute code
- **[Extension Guide](extension-guide.md)** - Building extensions and plugins

### Development Setup
- **Prerequisites:** Node.js 16+, Git, API keys
- **Clone & Install:** `git clone` → `pnpm install` 
- **Build:** `pnpm build`
- **Test:** `pnpm test`
- **Local Development:** `node dist/cli.js --version`

### Key Concepts
- **Modular Architecture:** Separate concerns, clear interfaces
- **AI Provider System:** Pluggable AI service integration
- **Caching Strategy:** Content-based hashing with TTL
- **Report Formatters:** Multi-format output generation
- **Error Handling:** Comprehensive recovery and user guidance

## 🎯 Quick Reference

### Command Summary
```bash
# Core Commands
ai-review                          # Quick analysis
ai-review analyze                  # Full analysis options
ai-review config --wizard         # Interactive setup
ai-review cache --info            # Cache statistics
ai-review doctor                   # System diagnostics

# Configuration
ai-review config --show           # View current config
ai-review config --validate       # Check configuration
ai-review config --template       # Generate template

# Maintenance
ai-review cache --clear           # Clear cache
ai-review cache --optimize        # Optimize cache
```

### Configuration Quick Start
```json
{
  "aiProvider": "openai",
  "model": "gpt-4",
  "rules": {
    "logic": "strict",
    "security": "strict",
    "performance": "normal",
    "style": "normal",
    "business": "normal"
  },
  "output": {
    "format": "terminal",
    "detailed": true
  },
  "cache": {
    "enabled": true,
    "ttl": 24
  }
}
```

### Environment Variables
```bash
# API Keys
export OPENAI_API_KEY="sk-..."
export ANTHROPIC_API_KEY="sk-ant-..."

# Configuration
export AI_REVIEW_PROVIDER="openai"
export AI_REVIEW_MODEL="gpt-4"
```

## 📋 File Type Support

| Language | Extensions | Status | Notes |
|----------|------------|--------|-------|
| **JavaScript** | `.js`, `.jsx` | ✅ Full | ES6+, React support |
| **TypeScript** | `.ts`, `.tsx` | ✅ Full | Strict mode, React support |
| **Vue.js** | `.vue` | ✅ Full | Single-file components |
| **CSS/SCSS** | `.css`, `.scss`, `.sass`, `.less` | ✅ Full | Style analysis |
| **HTML** | `.html`, `.htm` | ✅ Full | Markup validation |
| **JSON** | `.json` | ✅ Full | Structure validation |
| **Python** | `.py` | 🔄 Planned | Future release |
| **Java** | `.java` | 🔄 Planned | Future release |
| **Go** | `.go` | 🔄 Planned | Future release |

## 🎨 Output Formats

| Format | Description | Use Case |
|--------|-------------|----------|
| **Terminal** | Colorful console output | Development workflow |
| **HTML** | Interactive web reports | Detailed analysis, sharing |
| **JSON** | Structured data export | CI/CD integration, automation |

## 🤖 AI Provider Support

| Provider | Models | Status | Cost | Best For |
|----------|--------|--------|------|----------|
| **OpenAI** | GPT-4, GPT-3.5-turbo | ✅ Full | $$ | General analysis |
| **Anthropic** | Claude-3 (Opus/Sonnet/Haiku) | ✅ Full | $$$ | Security analysis |
| **Local LLM** | Ollama, CodeLlama | 🔄 Planned | Free | Privacy, offline |

## 🔧 Advanced Topics

### Performance Optimization
- **Enable Caching:** 60-80% speed improvement
- **Model Selection:** Balance quality vs speed
- **File Filtering:** Use ignore patterns effectively
- **Batch Analysis:** Process related files together

### Team Integration
- **Shared Configuration:** Commit `.ai-review.json` to repo
- **Git Hooks:** Pre-commit and pre-push integration
- **CI/CD Pipeline:** GitHub Actions, GitLab CI templates
- **Code Review Process:** AI pre-review + human review

### Troubleshooting
- **Configuration Issues:** `ai-review config --validate`
- **Performance Problems:** `ai-review cache --info`
- **API Errors:** Check keys and network connectivity
- **System Diagnostics:** `ai-review doctor`

## 📚 External Resources

### API Documentation
- **[OpenAI API](https://platform.openai.com/docs)** - GPT models
- **[Anthropic API](https://docs.anthropic.com/)** - Claude models
- **[Commander.js](https://github.com/tj/commander.js/)** - CLI framework

### Related Tools
- **[ESLint](https://eslint.org/)** - JavaScript linting
- **[Prettier](https://prettier.io/)** - Code formatting
- **[SonarQube](https://www.sonarqube.org/)** - Code quality
- **[CodeClimate](https://codeclimate.com/)** - Automated code review

### Community
- **[GitHub Repository](https://github.com/ai-code-review/ai-code-review)** - Source code, issues
- **[GitHub Discussions](https://github.com/ai-code-review/ai-code-review/discussions)** - Community Q&A
- **[Changelog](../CHANGELOG.md)** - Version history
- **[License](../LICENSE)** - MIT License

## 🎯 Examples Repository

Find real-world examples and templates:

```bash
# Example configurations
examples/configs/
├── typescript-project.json
├── react-app.json
├── node-server.json
└── vue-spa.json

# Sample workflows
examples/workflows/
├── github-actions.yml
├── gitlab-ci.yml
└── pre-commit-hook.sh

# Integration examples
examples/integrations/
├── vscode-tasks.json
├── package-scripts.js
└── docker-compose.yml
```

## 🆘 Getting Help

### Self-Service
1. **Check documentation** - Most questions are answered here
2. **Run diagnostics** - `ai-review doctor`
3. **Validate configuration** - `ai-review config --validate`
4. **Search existing issues** - GitHub issues page

### Community Support
1. **GitHub Discussions** - General questions and ideas
2. **GitHub Issues** - Bug reports and feature requests
3. **Stack Overflow** - Tag with `ai-code-review`

### Urgent Issues
- **Security vulnerabilities** - Email: <EMAIL>
- **Major bugs** - High-priority GitHub issue
- **Feature requests** - GitHub discussions first

## 📊 Project Status

- **Version:** 1.0.0
- **Status:** Stable release
- **Node.js Support:** 16.0.0+
- **License:** MIT
- **Contributors:** [Contributors Page](https://github.com/ai-code-review/ai-code-review/graphs/contributors)

## 🗺 Roadmap

### Version 1.1 (Q1 2024)
- Python language support
- Local LLM integration (Ollama)
- VSCode extension
- Performance improvements

### Version 1.2 (Q2 2024)
- Java language support
- Plugin system
- Team analytics dashboard
- Advanced rule customization

### Version 2.0 (Q3 2024)
- Multi-language support (Go, Rust, PHP)
- Distributed caching
- Microservice architecture
- Enterprise features

---

**Navigation Tips:**
- Use the search function (Ctrl/Cmd + F) to find specific topics
- Links are provided throughout for easy navigation
- Most documentation includes examples and practical guidance
- When in doubt, start with the [FAQ](faq.md) or run `ai-review doctor`

*This documentation is actively maintained. Last updated: {{ current_date }}*