# Frequently Asked Questions (FAQ)

## Installation & Setup

### Q: How do I install AI Code Review?

**A:** Install globally using npm or pnpm:

```bash
# Using npm
npm install -g ai-code-review

# Using pnpm (recommended)
pnpm add -g ai-code-review

# Using yarn
yarn global add ai-code-review
```

### Q: Do I need an API key?

**A:** Yes, you need an API key from either:
- **OpenAI** (for GPT models) - Get one at [platform.openai.com](https://platform.openai.com/api-keys)
- **Anthropic** (for Claude models) - Get one at [console.anthropic.com](https://console.anthropic.com/)

Set it as an environment variable:
```bash
export OPENAI_API_KEY="your-key-here"
# or
export ANTHROPIC_API_KEY="your-key-here"
```

### Q: How do I get started quickly?

**A:** Three simple steps:
1. `ai-review config --wizard` (set up configuration)
2. `git add <your-files>` (stage your changes)  
3. `ai-review` (analyze your code)

### Q: Can I use AI Code Review without an internet connection?

**A:** Not fully. While the tool can run locally, AI analysis requires an internet connection to reach OpenAI or Anthropic APIs. Local LLM support is planned for future releases.

## Configuration

### Q: Where should I put my configuration file?

**A:** AI Code Review looks for configuration in this order:
1. `.ai-review.json` (project-specific, recommended)
2. `.ai-review.jsonc` (with comments)
3. `ai-review.config.json`
4. `~/.ai-review.json` (global fallback)

For team projects, use project-specific configuration (`.ai-review.json`) and commit it to your repository.

### Q: Which AI model should I choose?

**A:** **Recommended models by use case:**

- **Best Overall:** `gpt-4` - Most accurate and comprehensive
- **Best Performance/Cost:** `claude-3-sonnet-20240229` - Good balance
- **Fastest:** `gpt-3.5-turbo` - Quick feedback, lower cost
- **Most Thorough:** `claude-3-opus-20240229` - Highest quality analysis

### Q: What do the strictness levels mean?

**A:** 
- **`strict`** - Catches even minor issues, very thorough
- **`normal`** - Balanced analysis, focuses on important issues (recommended)
- **`loose`** - Only critical problems, minimal analysis

**Recommended settings:**
- **Security & Logic:** `strict` (safety critical)
- **Performance & Style:** `normal` (balanced)
- **Business:** Depends on your domain requirements

### Q: Can I ignore certain files or directories?

**A:** Yes! Use the `ignore` configuration with glob patterns:

```json
{
  "ignore": [
    "node_modules/**",
    "*.min.js",
    "dist/**",
    "*.test.js",
    "coverage/**"
  ]
}
```

## Usage

### Q: What files can AI Code Review analyze?

**A:** Currently supported file types:
- **JavaScript:** `.js`, `.jsx`
- **TypeScript:** `.ts`, `.tsx`
- **Vue.js:** `.vue`
- **Styles:** `.css`, `.scss`, `.sass`, `.less`
- **Markup:** `.html`, `.htm`
- **Data:** `.json`

More file types (Python, Java, Go, etc.) are planned for future releases.

### Q: Do I need to stage files with Git?

**A:** By default, yes. AI Code Review analyzes staged files (`git add`). However, you can:
- Use `--all` to analyze all modified files (staged + unstaged)
- Use `--staged` to explicitly analyze only staged files (default)

### Q: How does caching work?

**A:** AI Code Review caches analysis results based on file content hash. Benefits:
- **60-80% faster** re-analysis of unchanged code
- **Significant cost reduction** in API usage
- **Automatic invalidation** when files change

Cache is stored in `.ai-review-cache/` and expires after 24 hours by default.

### Q: Can I analyze code without Git?

**A:** No, AI Code Review requires a Git repository to track changes and provide context. Initialize one with `git init` if needed.

## Performance & Cost

### Q: How much does it cost to use?

**A:** Costs depend on your AI provider and usage:

**OpenAI (GPT-4):**
- ~$0.03 per 1K tokens input
- ~$0.06 per 1K tokens output
- Typical cost: $0.10-0.50 per file analysis

**Anthropic (Claude):**
- ~$0.015 per 1K tokens input  
- ~$0.075 per 1K tokens output
- Typical cost: $0.05-0.25 per file analysis

**Cost reduction tips:**
- Enable caching (reduces repeat analysis by 60-80%)
- Use `quick` mode for faster feedback
- Use cheaper models like `gpt-3.5-turbo` for development

### Q: How can I make analysis faster?

**A:** Speed optimization strategies:
1. **Enable caching:** `"cache": {"enabled": true}`
2. **Use quick mode:** `ai-review quick`
3. **Choose faster models:** `gpt-3.5-turbo`, `claude-3-haiku`
4. **Reduce file scope:** More specific `ignore` patterns
5. **Analyze staged only:** Use `--staged` (default)

### Q: The analysis seems slow. What can I do?

**A:** Check these factors:
- **Network speed** - AI analysis requires API calls
- **File size** - Large files take longer to process
- **Model choice** - GPT-4 is slower than GPT-3.5-turbo
- **Cache status** - Run `ai-review cache --info` to check hit rate

Try `ai-review doctor` to diagnose performance issues.

## Troubleshooting

### Q: I get "API key required" error

**A:** Solutions:
1. **Set environment variable:**
   ```bash
   export OPENAI_API_KEY="sk-..."
   ```
2. **Add to config file:**
   ```json
   {"apiKey": "sk-..."}
   ```
3. **Run configuration wizard:**
   ```bash
   ai-review config --wizard
   ```

### Q: "Not a Git repository" error

**A:** AI Code Review requires a Git repository. Solutions:
- **Initialize Git:** `git init`
- **Check current directory:** Ensure you're in the right project folder
- **Verify Git installation:** `git --version`

### Q: "No staged files found" message

**A:** This means no files are staged for commit. Solutions:
- **Stage files:** `git add <files>` or `git add .`
- **Check status:** `git status` to see available files
- **Analyze all modified:** Use `--all` flag
- **Create/modify files:** Make some changes to analyze

### Q: Configuration validation fails

**A:** Common issues and fixes:
1. **Invalid JSON syntax:** Check for missing commas, brackets, quotes
2. **Missing required fields:** Add `aiProvider`, `model`, `rules`, etc.
3. **Incompatible model:** Ensure model matches provider
4. **Run auto-fix:** `ai-review config --validate` (offers auto-fix)

### Q: Analysis results seem inaccurate

**A:** Improvement strategies:
1. **Use better models:** GPT-4 > GPT-3.5-turbo, Claude-3-Opus > Haiku
2. **Adjust strictness:** Try `strict` mode for more thorough analysis
3. **Enable detailed output:** `"detailed": true` in configuration
4. **Provide more context:** Larger files give better context
5. **Clear cache:** Old cached results might be stale

### Q: High API costs

**A:** Cost reduction strategies:
1. **Enable caching:** Reduces API calls by 60-80%
2. **Use cheaper models:** GPT-3.5-turbo, Claude-3-Haiku
3. **Quick mode:** `ai-review quick` for rapid feedback
4. **Analyze less:** Use `ignore` patterns, analyze staged only
5. **Loose rules:** Reduce analysis depth for non-critical categories

## Advanced Usage

### Q: Can I use multiple AI providers?

**A:** Currently, you can configure one provider at a time, but you can:
- Switch providers using `ai-review config --wizard`
- Use different configs per project
- Set provider via environment: `AI_REVIEW_PROVIDER=claude`

Multi-provider support is planned for future releases.

### Q: How do I integrate with CI/CD?

**A:** Example GitHub Actions workflow:

```yaml
name: AI Code Review
on: [pull_request]
jobs:
  review:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install -g ai-code-review
      - run: ai-review analyze --output json
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
```

### Q: Can I customize the analysis prompts?

**A:** Currently, prompts are built-in and optimized for best results. Custom prompt support is planned for future releases.

### Q: How do I contribute or request features?

**A:** We welcome contributions!
- **Bug reports:** [GitHub Issues](https://github.com/ai-code-review/ai-code-review/issues)
- **Feature requests:** [GitHub Discussions](https://github.com/ai-code-review/ai-code-review/discussions)
- **Code contributions:** See [Contributing Guide](../CONTRIBUTING.md)

## Technical Details

### Q: What data is sent to AI providers?

**A:** AI Code Review sends:
- **Code changes** (only modified lines + context)
- **File metadata** (filename, type)
- **Analysis prompts** (built-in, optimized for code review)

**Never sent:**
- Full file contents (only changes)
- Sensitive configuration data
- Personal information
- API keys or secrets

### Q: Is my code secure?

**A:** Security considerations:
- **Only changed lines** are analyzed, not entire codebases
- **API calls** are encrypted (HTTPS)
- **Local caching** stores results locally only
- **No permanent storage** on AI provider servers (per their policies)
- **Open source** - You can review all code

### Q: How accurate is the analysis?

**A:** Analysis accuracy depends on:
- **Model quality:** GPT-4 > GPT-3.5 > local models
- **Code context:** More context = better analysis
- **Rule configuration:** Appropriate strictness levels
- **File type:** Some languages analyzed better than others

Generally: 85-95% accuracy for well-structured code with good context.

### Q: What's the difference between quick and full mode?

**A:** 

**Quick Mode:**
- Focuses on critical issues only
- ~50% faster analysis
- Lower API costs
- Good for development workflow

**Full Mode:**
- Comprehensive analysis including style, best practices
- More thorough explanations
- Higher accuracy
- Better for code reviews, production code

---

## Still Need Help?

If your question isn't answered here:

1. **Run diagnostics:** `ai-review doctor`
2. **Check configuration:** `ai-review config --validate`  
3. **Read documentation:** [CLI Reference](cli-reference.md), [Configuration](configuration.md)
4. **Search issues:** [GitHub Issues](https://github.com/ai-code-review/ai-code-review/issues)
5. **Ask community:** [GitHub Discussions](https://github.com/ai-code-review/ai-code-review/discussions)
6. **Report bugs:** [New Issue](https://github.com/ai-code-review/ai-code-review/issues/new)

*We're here to help make your code review experience as smooth as possible!*