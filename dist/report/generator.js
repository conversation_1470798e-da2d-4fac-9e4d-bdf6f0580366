"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReportGenerator = void 0;
const path = __importStar(require("path"));
const logger_1 = require("../utils/logger");
const terminal_1 = require("./formatters/terminal");
const html_1 = require("./formatters/html");
const json_1 = require("./formatters/json");
class ReportGenerator {
    constructor(config, options = {}) {
        this.config = config;
        this.options = {
            outputDir: './reports',
            formats: ['terminal'],
            projectName: 'AI Code Review',
            includeStatistics: true,
            includeCharts: true,
            includeCodeContext: false,
            includeFixSuggestions: true,
            includeBlame: true,
            theme: 'light',
            ...options
        };
    }
    /**
     * 生成所有格式的报告
     */
    async generateAllReports(enhancedIssues, issueGroups, analysisResults, analysisTime) {
        logger_1.logger.info(`Generating reports in ${this.options.formats.length} formats`);
        const startTime = Date.now();
        const generatedReports = [];
        // 生成各种格式的报告
        for (const format of this.options.formats) {
            try {
                const report = await this.generateReport(format, enhancedIssues, issueGroups, analysisResults, analysisTime);
                generatedReports.push(report);
                if (report.success) {
                    logger_1.logger.info(`Successfully generated ${format} report${report.path ? `: ${report.path}` : ''}`);
                }
                else {
                    logger_1.logger.warn(`Failed to generate ${format} report: ${report.error}`);
                }
            }
            catch (error) {
                logger_1.logger.error(`Error generating ${format} report: ${error}`);
                generatedReports.push({
                    format,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        const totalTime = Date.now() - startTime;
        logger_1.logger.info(`Report generation completed in ${totalTime}ms`);
        return this.buildReportSummary(enhancedIssues, analysisResults, generatedReports, analysisTime);
    }
    /**
     * 生成单个格式的报告
     */
    async generateReport(format, enhancedIssues, issueGroups, analysisResults, analysisTime) {
        switch (format) {
            case 'terminal':
                return this.generateTerminalReport(enhancedIssues, issueGroups, analysisResults);
            case 'html':
                return this.generateHtmlReport(enhancedIssues, issueGroups, analysisResults);
            case 'json':
                return this.generateJsonReport(enhancedIssues, issueGroups, analysisResults, analysisTime);
            case 'json-simple':
                return this.generateJsonSimpleReport(enhancedIssues);
            default:
                return {
                    format,
                    success: false,
                    error: `Unsupported report format: ${format}`
                };
        }
    }
    /**
     * 生成终端报告
     */
    async generateTerminalReport(enhancedIssues, issueGroups, analysisResults) {
        try {
            const terminalOptions = {
                showDetails: this.config.output.verbose || false,
                showSuggestions: this.options.includeFixSuggestions,
                showContext: this.options.includeCodeContext,
                showStatistics: this.options.includeStatistics,
                groupByType: true,
                maxIssuesPerType: 10,
                colorEnabled: true
            };
            const formatter = new terminal_1.TerminalFormatter(terminalOptions);
            const content = formatter.generateReport(enhancedIssues, issueGroups, analysisResults);
            return {
                format: 'terminal',
                content,
                success: true
            };
        }
        catch (error) {
            return {
                format: 'terminal',
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * 生成HTML报告
     */
    async generateHtmlReport(enhancedIssues, issueGroups, analysisResults) {
        try {
            const outputPath = path.join(this.options.outputDir, `${this.getReportFileName()}.html`);
            const htmlOptions = {
                title: `${this.options.projectName} - Code Review Report`,
                includeCharts: this.options.includeCharts,
                includeCodeContext: this.options.includeCodeContext,
                includeFixSuggestions: this.options.includeFixSuggestions,
                theme: this.options.theme || 'light',
                outputPath,
                embedAssets: true
            };
            const formatter = new html_1.HTMLFormatter(htmlOptions);
            const filePath = await formatter.generateReport(enhancedIssues, issueGroups, analysisResults);
            return {
                format: 'html',
                path: filePath,
                success: true
            };
        }
        catch (error) {
            return {
                format: 'html',
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * 生成JSON报告
     */
    async generateJsonReport(enhancedIssues, issueGroups, analysisResults, analysisTime) {
        try {
            const outputPath = path.join(this.options.outputDir, `${this.getReportFileName()}.json`);
            const jsonOptions = {
                outputPath,
                includeContext: this.options.includeCodeContext,
                includeFixSuggestions: this.options.includeFixSuggestions,
                includeBlame: this.options.includeBlame,
                includeStatistics: this.options.includeStatistics,
                minified: false,
                apiVersion: '1.0'
            };
            const formatter = new json_1.JSONFormatter(jsonOptions);
            const filePath = await formatter.generateReport(enhancedIssues, issueGroups, analysisResults, analysisTime);
            return {
                format: 'json',
                path: filePath,
                success: true
            };
        }
        catch (error) {
            return {
                format: 'json',
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * 生成简化JSON报告
     */
    async generateJsonSimpleReport(enhancedIssues) {
        try {
            const outputPath = path.join(this.options.outputDir, `${this.getReportFileName()}-simple.json`);
            const jsonOptions = {
                outputPath,
                includeContext: false,
                includeFixSuggestions: false,
                includeBlame: false,
                includeStatistics: false,
                minified: true,
                apiVersion: '1.0'
            };
            const formatter = new json_1.JSONFormatter(jsonOptions);
            const filePath = await formatter.generateSimpleReport(enhancedIssues, outputPath);
            return {
                format: 'json-simple',
                path: filePath,
                success: true
            };
        }
        catch (error) {
            return {
                format: 'json-simple',
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    /**
     * 构建报告摘要
     */
    buildReportSummary(enhancedIssues, analysisResults, generatedReports, analysisTime) {
        return {
            totalFiles: analysisResults.length,
            totalIssues: enhancedIssues.length,
            criticalCount: enhancedIssues.filter(i => i.severity === 'Critical').length,
            highCount: enhancedIssues.filter(i => i.severity === 'High').length,
            mediumCount: enhancedIssues.filter(i => i.severity === 'Medium').length,
            lowCount: enhancedIssues.filter(i => i.severity === 'Low').length,
            generatedReports,
            analysisTime
        };
    }
    /**
     * 获取报告文件名
     */
    getReportFileName() {
        const timestamp = new Date().toISOString().split('T')[0];
        const projectName = this.options.projectName.toLowerCase().replace(/\s+/g, '-');
        return `${projectName}-review-${timestamp}`;
    }
    /**
     * 生成报告摘要信息（用于控制台输出）
     */
    generateSummaryText(summary) {
        const lines = [];
        lines.push('📋 Report Generation Summary');
        lines.push('================================');
        lines.push('');
        lines.push(`📁 Files analyzed: ${summary.totalFiles}`);
        lines.push(`🐛 Issues found: ${summary.totalIssues}`);
        lines.push('');
        lines.push('Severity Distribution:');
        lines.push(`  🔴 Critical: ${summary.criticalCount}`);
        lines.push(`  🟠 High: ${summary.highCount}`);
        lines.push(`  🟡 Medium: ${summary.mediumCount}`);
        lines.push(`  ⚪ Low: ${summary.lowCount}`);
        lines.push('');
        if (summary.analysisTime) {
            lines.push(`⏱️  Analysis time: ${(summary.analysisTime / 1000).toFixed(1)}s`);
            lines.push('');
        }
        // 报告生成状态
        lines.push('📊 Generated Reports:');
        summary.generatedReports.forEach(report => {
            const status = report.success ? '✅' : '❌';
            const location = report.path ? ` → ${report.path}` : '';
            const error = report.error ? ` (${report.error})` : '';
            lines.push(`  ${status} ${report.format}${location}${error}`);
        });
        // 建议
        lines.push('');
        if (summary.criticalCount > 0) {
            lines.push('🚨 Immediate action required: Critical issues found');
        }
        else if (summary.highCount > 0) {
            lines.push('⚠️  Review recommended: High priority issues found');
        }
        else {
            lines.push('✅ Code quality looks good!');
        }
        return lines.join('\n');
    }
    /**
     * 导出报告到其他格式
     */
    async exportToFormat(enhancedIssues, format, outputPath) {
        const jsonFormatter = new json_1.JSONFormatter({
            outputPath: './temp.json',
            includeContext: false,
            includeFixSuggestions: false,
            includeBlame: this.options.includeBlame,
            includeStatistics: false,
            minified: false,
            apiVersion: '1.0'
        });
        const targetPath = outputPath || path.join(this.options.outputDir, `${this.getReportFileName()}.${format}`);
        return await jsonFormatter.exportToFormat(enhancedIssues, format, targetPath);
    }
    /**
     * 合并多个JSON报告
     */
    async mergeJsonReports(reportPaths, outputPath) {
        const jsonFormatter = new json_1.JSONFormatter({
            outputPath: outputPath || path.join(this.options.outputDir, `${this.getReportFileName()}-merged.json`),
            includeContext: true,
            includeFixSuggestions: true,
            includeBlame: true,
            includeStatistics: true,
            minified: false,
            apiVersion: '1.0'
        });
        const targetPath = outputPath || path.join(this.options.outputDir, `${this.getReportFileName()}-merged.json`);
        return await jsonFormatter.mergeReports(reportPaths, targetPath);
    }
    /**
     * 验证报告文件
     */
    async validateReport(reportPath) {
        if (!reportPath.endsWith('.json')) {
            return {
                valid: false,
                errors: ['Only JSON reports can be validated']
            };
        }
        const jsonFormatter = new json_1.JSONFormatter({
            outputPath: './temp.json',
            includeContext: true,
            includeFixSuggestions: true,
            includeBlame: true,
            includeStatistics: true,
            minified: false,
            apiVersion: '1.0'
        });
        return await jsonFormatter.validateReport(reportPath);
    }
    /**
     * 获取支持的导出格式
     */
    getSupportedExportFormats() {
        return ['csv', 'xml', 'yaml'];
    }
    /**
     * 获取支持的报告格式
     */
    getSupportedReportFormats() {
        return ['terminal', 'html', 'json', 'json-simple'];
    }
    /**
     * 更新配置
     */
    updateOptions(options) {
        this.options = { ...this.options, ...options };
    }
    /**
     * 获取当前配置
     */
    getOptions() {
        return { ...this.options };
    }
}
exports.ReportGenerator = ReportGenerator;
//# sourceMappingURL=generator.js.map