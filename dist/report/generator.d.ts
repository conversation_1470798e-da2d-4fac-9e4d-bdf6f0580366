import { EnhancedIssue, IssueGroup } from '../core/suggestions';
import { AnalysisResult, AIReviewConfig } from '../types';
export interface ReportGeneratorOptions {
    outputDir: string;
    formats: ReportFormat[];
    projectName: string;
    includeStatistics: boolean;
    includeCharts: boolean;
    includeCodeContext: boolean;
    includeFixSuggestions: boolean;
    includeBlame: boolean;
    theme?: 'light' | 'dark' | 'auto';
}
export type ReportFormat = 'terminal' | 'html' | 'json' | 'json-simple';
export interface GeneratedReport {
    format: ReportFormat;
    path?: string;
    content?: string;
    success: boolean;
    error?: string;
}
export interface ReportSummary {
    totalFiles: number;
    totalIssues: number;
    criticalCount: number;
    highCount: number;
    mediumCount: number;
    lowCount: number;
    generatedReports: GeneratedReport[];
    analysisTime?: number;
}
export declare class ReportGenerator {
    private options;
    private config;
    constructor(config: AIReviewConfig, options?: Partial<ReportGeneratorOptions>);
    /**
     * 生成所有格式的报告
     */
    generateAllReports(enhancedIssues: EnhancedIssue[], issueGroups: IssueGroup[], analysisResults: AnalysisResult[], analysisTime?: number): Promise<ReportSummary>;
    /**
     * 生成单个格式的报告
     */
    private generateReport;
    /**
     * 生成终端报告
     */
    private generateTerminalReport;
    /**
     * 生成HTML报告
     */
    private generateHtmlReport;
    /**
     * 生成JSON报告
     */
    private generateJsonReport;
    /**
     * 生成简化JSON报告
     */
    private generateJsonSimpleReport;
    /**
     * 构建报告摘要
     */
    private buildReportSummary;
    /**
     * 获取报告文件名
     */
    private getReportFileName;
    /**
     * 生成报告摘要信息（用于控制台输出）
     */
    generateSummaryText(summary: ReportSummary): string;
    /**
     * 导出报告到其他格式
     */
    exportToFormat(enhancedIssues: EnhancedIssue[], format: 'csv' | 'xml' | 'yaml', outputPath?: string): Promise<string>;
    /**
     * 合并多个JSON报告
     */
    mergeJsonReports(reportPaths: string[], outputPath?: string): Promise<string>;
    /**
     * 验证报告文件
     */
    validateReport(reportPath: string): Promise<{
        valid: boolean;
        errors: string[];
    }>;
    /**
     * 获取支持的导出格式
     */
    getSupportedExportFormats(): string[];
    /**
     * 获取支持的报告格式
     */
    getSupportedReportFormats(): ReportFormat[];
    /**
     * 更新配置
     */
    updateOptions(options: Partial<ReportGeneratorOptions>): void;
    /**
     * 获取当前配置
     */
    getOptions(): ReportGeneratorOptions;
}
//# sourceMappingURL=generator.d.ts.map