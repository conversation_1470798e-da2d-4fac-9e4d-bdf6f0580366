{"version": 3, "file": "generator.js", "sourceRoot": "", "sources": ["../../src/report/generator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAG7B,4CAAyC;AAEzC,oDAAiF;AACjF,4CAAqE;AACrE,4CAAqE;AAmCrE,MAAa,eAAe;IAI1B,YAAY,MAAsB,EAAE,UAA2C,EAAE;QAC/E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,CAAC,UAAU,CAAC;YACrB,WAAW,EAAE,gBAAgB;YAC7B,iBAAiB,EAAE,IAAI;YACvB,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,KAAK;YACzB,qBAAqB,EAAE,IAAI;YAC3B,YAAY,EAAE,IAAI;YAClB,KAAK,EAAE,OAAO;YACd,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB,CAC7B,cAA+B,EAC/B,WAAyB,EACzB,eAAiC,EACjC,YAAqB;QAErB,eAAM,CAAC,IAAI,CAAC,yBAAyB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;QAE5E,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,gBAAgB,GAAsB,EAAE,CAAC;QAE/C,YAAY;QACZ,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YAC1C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CACtC,MAAM,EACN,cAAc,EACd,WAAW,EACX,eAAe,EACf,YAAY,CACb,CAAC;gBACF,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE9B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,eAAM,CAAC,IAAI,CAAC,0BAA0B,MAAM,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;gBACjG,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,sBAAsB,MAAM,YAAY,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,oBAAoB,MAAM,YAAY,KAAK,EAAE,CAAC,CAAC;gBAC5D,gBAAgB,CAAC,IAAI,CAAC;oBACpB,MAAM;oBACN,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QACzC,eAAM,CAAC,IAAI,CAAC,kCAAkC,SAAS,IAAI,CAAC,CAAC;QAE7D,OAAO,IAAI,CAAC,kBAAkB,CAC5B,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,YAAY,CACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAC1B,MAAoB,EACpB,cAA+B,EAC/B,WAAyB,EACzB,eAAiC,EACjC,YAAqB;QAErB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,UAAU;gBACb,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAEnF,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAE/E,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAE7F,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;YAEvD;gBACE,OAAO;oBACL,MAAM;oBACN,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,8BAA8B,MAAM,EAAE;iBAC9C,CAAC;QACN,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAClC,cAA+B,EAC/B,WAAyB,EACzB,eAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,eAAe,GAA0B;gBAC7C,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,KAAK;gBAChD,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;gBACnD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;gBAC5C,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBAC9C,WAAW,EAAE,IAAI;gBACjB,gBAAgB,EAAE,EAAE;gBACpB,YAAY,EAAE,IAAI;aACnB,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,4BAAiB,CAAC,eAAe,CAAC,CAAC;YACzD,MAAM,OAAO,GAAG,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAEvF,OAAO;gBACL,MAAM,EAAE,UAAU;gBAClB,OAAO;gBACP,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,cAA+B,EAC/B,WAAyB,EACzB,eAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YAEzF,MAAM,WAAW,GAAsB;gBACrC,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,uBAAuB;gBACzD,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa;gBACzC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;gBACnD,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;gBACzD,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO;gBACpC,UAAU;gBACV,WAAW,EAAE,IAAI;aAClB,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,oBAAa,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;YAE9F,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,cAA+B,EAC/B,WAAyB,EACzB,eAAiC,EACjC,YAAqB;QAErB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;YAEzF,MAAM,WAAW,GAAsB;gBACrC,UAAU;gBACV,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;gBAC/C,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,qBAAqB;gBACzD,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;gBACvC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;gBACjD,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,KAAK;aAClB,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,oBAAa,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,CAAC,CAAC;YAE5G,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,cAA+B;QAE/B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;YAEhG,MAAM,WAAW,GAAsB;gBACrC,UAAU;gBACV,cAAc,EAAE,KAAK;gBACrB,qBAAqB,EAAE,KAAK;gBAC5B,YAAY,EAAE,KAAK;gBACnB,iBAAiB,EAAE,KAAK;gBACxB,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,KAAK;aAClB,CAAC;YAEF,MAAM,SAAS,GAAG,IAAI,oBAAa,CAAC,WAAW,CAAC,CAAC;YACjD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,oBAAoB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;YAElF,OAAO;gBACL,MAAM,EAAE,aAAa;gBACrB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,MAAM,EAAE,aAAa;gBACrB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CACxB,cAA+B,EAC/B,eAAiC,EACjC,gBAAmC,EACnC,YAAqB;QAErB,OAAO;YACL,UAAU,EAAE,eAAe,CAAC,MAAM;YAClC,WAAW,EAAE,cAAc,CAAC,MAAM;YAClC,aAAa,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM;YAC3E,SAAS,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;YACnE,WAAW,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;YACvE,QAAQ,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;YACjE,gBAAgB;YAChB,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;QAChF,OAAO,GAAG,WAAW,WAAW,SAAS,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,OAAsB;QAC/C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC3C,KAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC/C,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QACvD,KAAK,CAAC,IAAI,CAAC,oBAAoB,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QACtD,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QACrC,KAAK,CAAC,IAAI,CAAC,kBAAkB,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;QACtD,KAAK,CAAC,IAAI,CAAC,cAAc,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;QAC9C,KAAK,CAAC,IAAI,CAAC,gBAAgB,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAClD,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC3C,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAEf,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YAC9E,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,SAAS;QACT,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACpC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACxC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC1C,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACxD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACvD,KAAK,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,QAAQ,GAAG,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;QAEH,KAAK;QACL,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACf,IAAI,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACpE,CAAC;aAAM,IAAI,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QACnE,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACzB,cAA+B,EAC/B,MAA8B,EAC9B,UAAmB;QAEnB,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC;YACtC,UAAU,EAAE,aAAa;YACzB,cAAc,EAAE,KAAK;YACrB,qBAAqB,EAAE,KAAK;YAC5B,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY;YACvC,iBAAiB,EAAE,KAAK;YACxB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,CACxC,IAAI,CAAC,OAAO,CAAC,SAAS,EACtB,GAAG,IAAI,CAAC,iBAAiB,EAAE,IAAI,MAAM,EAAE,CACxC,CAAC;QAEF,OAAO,MAAM,aAAa,CAAC,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,WAAqB,EAAE,UAAmB;QACtE,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC;YACtC,UAAU,EAAE,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC;YACtG,cAAc,EAAE,IAAI;YACpB,qBAAqB,EAAE,IAAI;YAC3B,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAC9G,OAAO,MAAM,aAAa,CAAC,YAAY,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,UAAkB;QAC5C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,CAAC,oCAAoC,CAAC;aAC/C,CAAC;QACJ,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,oBAAa,CAAC;YACtC,UAAU,EAAE,aAAa;YACzB,cAAc,EAAE,IAAI;YACpB,qBAAqB,EAAE,IAAI;YAC3B,YAAY,EAAE,IAAI;YAClB,iBAAiB,EAAE,IAAI;YACvB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;QAEH,OAAO,MAAM,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC9B,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,yBAAyB;QAC9B,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,aAAa,CAAC,OAAwC;QAC3D,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACI,UAAU;QACf,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;CACF;AAxaD,0CAwaC"}