{"version": 3, "file": "generator.d.ts", "sourceRoot": "", "sources": ["../../src/report/generator.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAChE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM,UAAU,CAAC;AAO1D,MAAM,WAAW,sBAAsB;IACrC,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,YAAY,EAAE,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,aAAa,EAAE,OAAO,CAAC;IACvB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,qBAAqB,EAAE,OAAO,CAAC;IAC/B,YAAY,EAAE,OAAO,CAAC;IACtB,KAAK,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC;CACnC;AAED,MAAM,MAAM,YAAY,GAAG,UAAU,GAAG,MAAM,GAAG,MAAM,GAAG,aAAa,CAAC;AAExE,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,YAAY,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,aAAa;IAC5B,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;IACjB,gBAAgB,EAAE,eAAe,EAAE,CAAC;IACpC,YAAY,CAAC,EAAE,MAAM,CAAC;CACvB;AAED,qBAAa,eAAe;IAC1B,OAAO,CAAC,OAAO,CAAyB;IACxC,OAAO,CAAC,MAAM,CAAiB;gBAEnB,MAAM,EAAE,cAAc,EAAE,OAAO,GAAE,OAAO,CAAC,sBAAsB,CAAM;IAgBjF;;OAEG;IACU,kBAAkB,CAC7B,cAAc,EAAE,aAAa,EAAE,EAC/B,WAAW,EAAE,UAAU,EAAE,EACzB,eAAe,EAAE,cAAc,EAAE,EACjC,YAAY,CAAC,EAAE,MAAM,GACpB,OAAO,CAAC,aAAa,CAAC;IA4CzB;;OAEG;YACW,cAAc;IA6B5B;;OAEG;YACW,sBAAsB;IAiCpC;;OAEG;YACW,kBAAkB;IAmChC;;OAEG;YACW,kBAAkB;IAoChC;;OAEG;YACW,wBAAwB;IAiCtC;;OAEG;IACH,OAAO,CAAC,kBAAkB;IAkB1B;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAMzB;;OAEG;IACI,mBAAmB,CAAC,OAAO,EAAE,aAAa,GAAG,MAAM;IA2C1D;;OAEG;IACU,cAAc,CACzB,cAAc,EAAE,aAAa,EAAE,EAC/B,MAAM,EAAE,KAAK,GAAG,KAAK,GAAG,MAAM,EAC9B,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC;IAmBlB;;OAEG;IACU,gBAAgB,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAe1F;;OAEG;IACU,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE,CAAC;IAqB9F;;OAEG;IACI,yBAAyB,IAAI,MAAM,EAAE;IAI5C;;OAEG;IACI,yBAAyB,IAAI,YAAY,EAAE;IAIlD;;OAEG;IACI,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI;IAIpE;;OAEG;IACI,UAAU,IAAI,sBAAsB;CAG5C"}