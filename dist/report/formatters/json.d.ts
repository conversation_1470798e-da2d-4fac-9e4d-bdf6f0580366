import { EnhancedIssue, IssueGroup } from '../../core/suggestions';
import { AnalysisResult } from '../../types';
export interface JSONReportOptions {
    outputPath: string;
    includeContext: boolean;
    includeFixSuggestions: boolean;
    includeBlame: boolean;
    includeStatistics: boolean;
    minified: boolean;
    apiVersion: string;
}
export interface JSONReport {
    metadata: {
        version: string;
        generatedAt: string;
        tool: string;
        apiVersion: string;
    };
    summary: {
        totalFiles: number;
        totalIssues: number;
        issuesBySeverity: Record<string, number>;
        issuesByType: Record<string, number>;
        analysisTime?: number;
    };
    files: FileReport[];
    statistics?: ReportStatistics;
    groups?: IssueGroup[];
}
export interface FileReport {
    filePath: string;
    issues: EnhancedIssue[];
    statistics: {
        issueCount: number;
        severityDistribution: Record<string, number>;
        typeDistribution: Record<string, number>;
    };
}
export interface ReportStatistics {
    performance: {
        analysisTime: number;
        cacheHitRate: number;
        filesAnalyzed: number;
        averageIssuesPerFile: number;
    };
    quality: {
        codeComplexity: {
            average: number;
            highest: number;
            files: string[];
        };
        coverage: {
            criticalIssuesCovered: number;
            automatedFixesAvailable: number;
        };
    };
    trends: {
        mostCommonIssueType: string;
        mostProblematicFiles: Array<{
            file: string;
            issues: number;
        }>;
        authorStatistics: Array<{
            author: string;
            issues: number;
        }>;
    };
}
export declare class JSONFormatter {
    private options;
    constructor(options?: Partial<JSONReportOptions>);
    /**
     * 生成JSON报告
     */
    generateReport(enhancedIssues: EnhancedIssue[], issueGroups: IssueGroup[], analysisResults: AnalysisResult[], analysisTime?: number): Promise<string>;
    /**
     * 构建JSON报告结构
     */
    private buildJSONReport;
    /**
     * 构建元数据
     */
    private buildMetadata;
    /**
     * 构建摘要信息
     */
    private buildSummary;
    /**
     * 构建文件报告
     */
    private buildFileReports;
    /**
     * 构建统计信息
     */
    private buildStatistics;
    /**
     * 计算性能统计
     */
    private calculatePerformanceStats;
    /**
     * 计算质量统计
     */
    private calculateQualityStats;
    /**
     * 计算趋势统计
     */
    private calculateTrendStats;
    /**
     * 过滤问题数据
     */
    private filterIssueData;
    /**
     * 生成简化的JSON报告
     */
    generateSimpleReport(enhancedIssues: EnhancedIssue[], outputPath?: string): Promise<string>;
    /**
     * 验证JSON报告格式
     */
    validateReport(reportPath: string): Promise<{
        valid: boolean;
        errors: string[];
    }>;
    /**
     * 合并多个JSON报告
     */
    mergeReports(reportPaths: string[], outputPath: string): Promise<string>;
    /**
     * 合并多个报告
     */
    private combineReports;
    /**
     * 重新计算严重程度分布
     */
    private recalculateSeverityDistribution;
    /**
     * 重新计算类型分布
     */
    private recalculateTypeDistribution;
    /**
     * 导出为不同格式
     */
    exportToFormat(enhancedIssues: EnhancedIssue[], format: 'csv' | 'xml' | 'yaml', outputPath?: string): Promise<string>;
    /**
     * 导出为CSV
     */
    private exportToCSV;
    /**
     * 导出为XML
     */
    private exportToXML;
    /**
     * 导出为YAML
     */
    private exportToYAML;
    /**
     * XML转义
     */
    private escapeXml;
}
//# sourceMappingURL=json.d.ts.map