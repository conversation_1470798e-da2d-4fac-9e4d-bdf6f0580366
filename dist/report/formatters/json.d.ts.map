{"version": 3, "file": "json.d.ts", "sourceRoot": "", "sources": ["../../../src/report/formatters/json.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAG7C,MAAM,WAAW,iBAAiB;IAChC,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,OAAO,CAAC;IACxB,qBAAqB,EAAE,OAAO,CAAC;IAC/B,YAAY,EAAE,OAAO,CAAC;IACtB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,QAAQ,EAAE,OAAO,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE;QACR,OAAO,EAAE,MAAM,CAAC;QAChB,WAAW,EAAE,MAAM,CAAC;QACpB,IAAI,EAAE,MAAM,CAAC;QACb,UAAU,EAAE,MAAM,CAAC;KACpB,CAAC;IACF,OAAO,EAAE;QACP,UAAU,EAAE,MAAM,CAAC;QACnB,WAAW,EAAE,MAAM,CAAC;QACpB,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACzC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACrC,YAAY,CAAC,EAAE,MAAM,CAAC;KACvB,CAAC;IACF,KAAK,EAAE,UAAU,EAAE,CAAC;IACpB,UAAU,CAAC,EAAE,gBAAgB,CAAC;IAC9B,MAAM,CAAC,EAAE,UAAU,EAAE,CAAC;CACvB;AAED,MAAM,WAAW,UAAU;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,aAAa,EAAE,CAAC;IACxB,UAAU,EAAE;QACV,UAAU,EAAE,MAAM,CAAC;QACnB,oBAAoB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC7C,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC1C,CAAC;CACH;AAED,MAAM,WAAW,gBAAgB;IAC/B,WAAW,EAAE;QACX,YAAY,EAAE,MAAM,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;QACrB,aAAa,EAAE,MAAM,CAAC;QACtB,oBAAoB,EAAE,MAAM,CAAC;KAC9B,CAAC;IACF,OAAO,EAAE;QACP,cAAc,EAAE;YACd,OAAO,EAAE,MAAM,CAAC;YAChB,OAAO,EAAE,MAAM,CAAC;YAChB,KAAK,EAAE,MAAM,EAAE,CAAC;SACjB,CAAC;QACF,QAAQ,EAAE;YACR,qBAAqB,EAAE,MAAM,CAAC;YAC9B,uBAAuB,EAAE,MAAM,CAAC;SACjC,CAAC;KACH,CAAC;IACF,MAAM,EAAE;QACN,mBAAmB,EAAE,MAAM,CAAC;QAC5B,oBAAoB,EAAE,KAAK,CAAC;YAAE,IAAI,EAAE,MAAM,CAAC;YAAC,MAAM,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;QAC9D,gBAAgB,EAAE,KAAK,CAAC;YAAE,MAAM,EAAE,MAAM,CAAC;YAAC,MAAM,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;KAC7D,CAAC;CACH;AAED,qBAAa,aAAa;IACxB,OAAO,CAAC,OAAO,CAAoB;gBAEvB,OAAO,GAAE,OAAO,CAAC,iBAAiB,CAAM;IAapD;;OAEG;IACU,cAAc,CACzB,cAAc,EAAE,aAAa,EAAE,EAC/B,WAAW,EAAE,UAAU,EAAE,EACzB,eAAe,EAAE,cAAc,EAAE,EACjC,YAAY,CAAC,EAAE,MAAM,GACpB,OAAO,CAAC,MAAM,CAAC;IA8BlB;;OAEG;IACH,OAAO,CAAC,eAAe;IAsBvB;;OAEG;IACH,OAAO,CAAC,aAAa;IASrB;;OAEG;IACH,OAAO,CAAC,YAAY;IAwBpB;;OAEG;IACH,OAAO,CAAC,gBAAgB;IA8CxB;;OAEG;IACH,OAAO,CAAC,eAAe;IAqBvB;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAiBjC;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAmC7B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA0C3B;;OAEG;IACH,OAAO,CAAC,eAAe;IAmBvB;;OAEG;IACU,oBAAoB,CAC/B,cAAc,EAAE,aAAa,EAAE,EAC/B,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC;IA+BlB;;OAEG;IACI,cAAc,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE,CAAC;IAwCxF;;OAEG;IACU,YAAY,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAqCrF;;OAEG;IACH,OAAO,CAAC,cAAc;IAyDtB;;OAEG;IACH,OAAO,CAAC,+BAA+B;IAOvC;;OAEG;IACH,OAAO,CAAC,2BAA2B;IAOnC;;OAEG;IACU,cAAc,CACzB,cAAc,EAAE,aAAa,EAAE,EAC/B,MAAM,EAAE,KAAK,GAAG,KAAK,GAAG,MAAM,EAC9B,UAAU,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC,MAAM,CAAC;IAgBlB;;OAEG;YACW,WAAW;IAqBzB;;OAEG;YACW,WAAW;IA4BzB;;OAEG;YACW,YAAY;IA0B1B;;OAEG;IACH,OAAO,CAAC,SAAS;CAQlB"}