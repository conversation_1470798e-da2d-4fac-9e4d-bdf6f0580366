"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalFormatter = void 0;
const chalk_1 = __importDefault(require("chalk"));
class TerminalFormatter {
    constructor(options = {}) {
        this.options = {
            showDetails: true,
            showSuggestions: true,
            showContext: false,
            showStatistics: true,
            groupByType: true,
            maxIssuesPerType: 10,
            colorEnabled: true,
            ...options
        };
    }
    /**
     * 生成完整的终端报告
     */
    generateReport(enhancedIssues, issueGroups, analysisResults) {
        const sections = [];
        // 1. 标题和摘要
        sections.push(this.generateHeader());
        sections.push(this.generateSummary(enhancedIssues, analysisResults));
        // 2. 统计信息
        if (this.options.showStatistics) {
            sections.push(this.generateStatistics(enhancedIssues, analysisResults));
        }
        // 3. 问题报告
        if (this.options.groupByType) {
            sections.push(this.generateGroupedIssues(issueGroups));
        }
        else {
            sections.push(this.generateIssuesList(enhancedIssues));
        }
        // 4. 修复建议总结
        if (this.options.showSuggestions) {
            sections.push(this.generateFixSuggestionsSummary(enhancedIssues));
        }
        // 5. 结论和建议
        sections.push(this.generateConclusion(enhancedIssues));
        return sections.join('\n\n');
    }
    /**
     * 生成报告头部
     */
    generateHeader() {
        const lines = [
            this.colorize('🤖 AI Code Review Report', 'blue', 'bold'),
            this.colorize('═'.repeat(50), 'gray'),
            this.colorize(`Generated at: ${new Date().toLocaleString()}`, 'gray')
        ];
        return lines.join('\n');
    }
    /**
     * 生成摘要信息
     */
    generateSummary(enhancedIssues, analysisResults) {
        const summary = this.calculateSummary(enhancedIssues, analysisResults);
        const lines = [
            this.colorize('📊 Analysis Summary', 'cyan', 'bold'),
            '',
            `${this.colorize('Files analyzed:', 'white')} ${summary.totalFiles}`,
            `${this.colorize('Total issues found:', 'white')} ${summary.totalIssues}`,
            '',
            `${this.colorize('🔴 Critical:', 'red')} ${summary.criticalIssues}`,
            `${this.colorize('🟠 High:', 'yellow')} ${summary.highIssues}`,
            `${this.colorize('🟡 Medium:', 'blue')} ${summary.mediumIssues}`,
            `${this.colorize('⚪ Low:', 'gray')} ${summary.lowIssues}`
        ];
        return lines.join('\n');
    }
    /**
     * 生成统计信息
     */
    generateStatistics(enhancedIssues, analysisResults) {
        const summary = this.calculateSummary(enhancedIssues, analysisResults);
        const lines = [
            this.colorize('📈 Detailed Statistics', 'cyan', 'bold'),
            ''
        ];
        // 按类型统计
        lines.push(this.colorize('Issues by Type:', 'white', 'underline'));
        Object.entries(summary.issuesByType)
            .sort(([, a], [, b]) => b - a)
            .forEach(([type, count]) => {
            const icon = this.getTypeIcon(type);
            const percentage = ((count / summary.totalIssues) * 100).toFixed(1);
            lines.push(`  ${icon} ${type}: ${count} (${percentage}%)`);
        });
        lines.push('');
        // 问题最多的文件
        if (summary.topIssueFiles.length > 0) {
            lines.push(this.colorize('Files with Most Issues:', 'white', 'underline'));
            summary.topIssueFiles.slice(0, 5).forEach((item, index) => {
                const icon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '📄';
                lines.push(`  ${icon} ${item.file}: ${item.count} issues`);
            });
        }
        return lines.join('\n');
    }
    /**
     * 生成分组问题报告
     */
    generateGroupedIssues(issueGroups) {
        const lines = [
            this.colorize('🔍 Issues by Category', 'cyan', 'bold'),
            ''
        ];
        issueGroups.forEach((group, index) => {
            if (index > 0)
                lines.push('');
            // 分组标题
            const priority = this.getPriorityIcon(group.overallPriority);
            lines.push(this.colorize(`${priority} ${group.category} (${group.issues.length} issues, ~${group.estimatedFixTime})`, 'yellow', 'bold'));
            lines.push(this.colorize('─'.repeat(60), 'gray'));
            // 分组摘要
            if (group.summary) {
                lines.push(this.colorize(group.summary, 'white', 'italic'));
                lines.push('');
            }
            // 显示分组中的问题
            const maxIssues = this.options.maxIssuesPerType || group.issues.length;
            group.issues.slice(0, maxIssues).forEach((issue, issueIndex) => {
                lines.push(this.formatIssue(issue, issueIndex + 1));
                if (this.options.showDetails && issueIndex < 3) { // 只显示前3个的详情
                    lines.push(this.formatIssueDetails(issue));
                }
            });
            if (group.issues.length > maxIssues) {
                const remaining = group.issues.length - maxIssues;
                lines.push(this.colorize(`  ... and ${remaining} more issues`, 'gray', 'italic'));
            }
        });
        return lines.join('\n');
    }
    /**
     * 生成问题列表报告
     */
    generateIssuesList(enhancedIssues) {
        const lines = [
            this.colorize('🔍 All Issues', 'cyan', 'bold'),
            ''
        ];
        enhancedIssues.forEach((issue, index) => {
            lines.push(this.formatIssue(issue, index + 1));
            if (this.options.showDetails) {
                lines.push(this.formatIssueDetails(issue));
            }
            if (index < enhancedIssues.length - 1) {
                lines.push('');
            }
        });
        return lines.join('\n');
    }
    /**
     * 格式化单个问题
     */
    formatIssue(issue, index) {
        const severityColor = this.getSeverityColor(issue.severity);
        const typeIcon = this.getTypeIcon(issue.type);
        const severityIcon = this.getSeverityIcon(issue.severity);
        const priority = issue.priority > 80 ? '🔥' : issue.priority > 60 ? '⚠️' : '💡';
        const line = [
            this.colorize(`${index}.`, 'gray'),
            severityIcon,
            typeIcon,
            this.colorize(issue.message, severityColor, 'bold')
        ].join(' ');
        const location = this.colorize(`📁 ${issue.filePath}:${issue.line}${issue.column ? `:${issue.column}` : ''}`, 'blue');
        const author = issue.blame ?
            this.colorize(`👤 ${issue.blame.author} (${new Date(issue.blame.commitTime).toLocaleDateString()})`, 'gray') :
            '';
        return [line, `   ${location}`, author ? `   ${author}` : ''].filter(Boolean).join('\n');
    }
    /**
     * 格式化问题详情
     */
    formatIssueDetails(issue) {
        const lines = [];
        // 问题描述
        if (issue.description && issue.description !== issue.message) {
            lines.push(`   ${this.colorize('Description:', 'white', 'underline')}`);
            lines.push(`   ${this.wordWrap(issue.description, 70, '   ')}`);
        }
        // 代码上下文
        if (this.options.showContext && issue.context.surroundingLines.length > 0) {
            lines.push(`   ${this.colorize('Code Context:', 'white', 'underline')}`);
            issue.context.surroundingLines.forEach(line => {
                lines.push(`   ${this.colorize(line, 'gray')}`);
            });
        }
        // 修复建议
        if (this.options.showSuggestions && issue.fixSuggestions.length > 0) {
            lines.push(`   ${this.colorize('Fix Suggestions:', 'white', 'underline')}`);
            issue.fixSuggestions.slice(0, 2).forEach((suggestion, index) => {
                const effortIcon = suggestion.effort === 'low' ? '🟢' :
                    suggestion.effort === 'medium' ? '🟡' : '🔴';
                const impactIcon = suggestion.impact === 'high' ? '🚀' :
                    suggestion.impact === 'medium' ? '📈' : '💡';
                lines.push(`   ${index + 1}. ${effortIcon}${impactIcon} ${this.colorize(suggestion.title, 'yellow')}`);
                lines.push(`      ${this.wordWrap(suggestion.description, 65, '      ')}`);
                if (suggestion.codeExample) {
                    lines.push(`      ${this.colorize('Before:', 'red')}`);
                    lines.push(`      ${this.colorize(suggestion.codeExample.before, 'red', 'italic')}`);
                    lines.push(`      ${this.colorize('After:', 'green')}`);
                    lines.push(`      ${this.colorize(suggestion.codeExample.after, 'green', 'italic')}`);
                }
            });
            if (issue.fixSuggestions.length > 2) {
                lines.push(`   ${this.colorize(`... and ${issue.fixSuggestions.length - 2} more suggestions`, 'gray', 'italic')}`);
            }
        }
        // 相关问题
        if (issue.relatedIssues.length > 0) {
            lines.push(`   ${this.colorize('Related Issues:', 'white', 'underline')}`);
            lines.push(`   ${issue.relatedIssues.length} related issues found`);
        }
        return lines.join('\n');
    }
    /**
     * 生成修复建议摘要
     */
    generateFixSuggestionsSummary(enhancedIssues) {
        const lines = [
            this.colorize('🔧 Fix Suggestions Summary', 'cyan', 'bold'),
            ''
        ];
        // 统计不同类型的建议
        const suggestionStats = new Map();
        let totalAutomated = 0;
        enhancedIssues.forEach(issue => {
            issue.fixSuggestions.forEach(suggestion => {
                suggestionStats.set(suggestion.type, (suggestionStats.get(suggestion.type) || 0) + 1);
                if (suggestion.automated)
                    totalAutomated++;
            });
        });
        // 显示统计
        const totalSuggestions = Array.from(suggestionStats.values()).reduce((a, b) => a + b, 0);
        lines.push(`${this.colorize('Total suggestions:', 'white')} ${totalSuggestions}`);
        lines.push(`${this.colorize('Automated fixes available:', 'green')} ${totalAutomated}`);
        lines.push('');
        if (suggestionStats.size > 0) {
            lines.push(this.colorize('Suggestions by Type:', 'white', 'underline'));
            Array.from(suggestionStats.entries())
                .sort(([, a], [, b]) => b - a)
                .forEach(([type, count]) => {
                const icon = this.getSuggestionTypeIcon(type);
                lines.push(`  ${icon} ${type}: ${count}`);
            });
        }
        return lines.join('\n');
    }
    /**
     * 生成结论和建议
     */
    generateConclusion(enhancedIssues) {
        const lines = [
            this.colorize('🎯 Recommendations', 'cyan', 'bold'),
            ''
        ];
        const criticalCount = enhancedIssues.filter(i => i.severity === 'Critical').length;
        const highCount = enhancedIssues.filter(i => i.severity === 'High').length;
        if (criticalCount === 0 && highCount === 0) {
            lines.push(this.colorize('✅ Good job! No critical or high-priority issues found.', 'green', 'bold'));
            lines.push('   Focus on medium and low priority improvements for code quality.');
        }
        else {
            if (criticalCount > 0) {
                lines.push(this.colorize(`🚨 Immediate attention required: ${criticalCount} critical issues`, 'red', 'bold'));
                lines.push('   These issues should be fixed before deployment.');
            }
            if (highCount > 0) {
                lines.push(this.colorize(`⚠️  High priority: ${highCount} important issues`, 'yellow', 'bold'));
                lines.push('   Plan to address these issues in the current sprint.');
            }
        }
        lines.push('');
        // 自动化建议
        const automatedFixes = enhancedIssues
            .flatMap(i => i.fixSuggestions)
            .filter(s => s.automated).length;
        if (automatedFixes > 0) {
            lines.push(this.colorize(`💡 ${automatedFixes} issues can be automatically fixed`, 'blue'));
            lines.push('   Consider running automated fixes to improve code quality quickly.');
        }
        // 优先级建议
        const topPriorityIssues = enhancedIssues
            .filter(i => i.priority > 80)
            .slice(0, 3);
        if (topPriorityIssues.length > 0) {
            lines.push('');
            lines.push(this.colorize('🔥 Top Priority Issues:', 'white', 'underline'));
            topPriorityIssues.forEach((issue, index) => {
                lines.push(`   ${index + 1}. ${issue.message} (${issue.filePath}:${issue.line})`);
            });
        }
        return lines.join('\n');
    }
    /**
     * 计算摘要统计
     */
    calculateSummary(enhancedIssues, analysisResults) {
        const issuesByType = {};
        const fileIssueCounts = new Map();
        enhancedIssues.forEach(issue => {
            // 类型统计
            issuesByType[issue.type] = (issuesByType[issue.type] || 0) + 1;
            // 文件统计
            fileIssueCounts.set(issue.filePath, (fileIssueCounts.get(issue.filePath) || 0) + 1);
        });
        const topIssueFiles = Array.from(fileIssueCounts.entries())
            .map(([file, count]) => ({ file, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        return {
            totalFiles: analysisResults.length,
            totalIssues: enhancedIssues.length,
            criticalIssues: enhancedIssues.filter(i => i.severity === 'Critical').length,
            highIssues: enhancedIssues.filter(i => i.severity === 'High').length,
            mediumIssues: enhancedIssues.filter(i => i.severity === 'Medium').length,
            lowIssues: enhancedIssues.filter(i => i.severity === 'Low').length,
            issuesByType,
            topIssueFiles
        };
    }
    /**
     * 获取严重程度颜色
     */
    getSeverityColor(severity) {
        switch (severity) {
            case 'Critical': return 'red';
            case 'High': return 'yellow';
            case 'Medium': return 'blue';
            case 'Low': return 'gray';
            default: return 'gray';
        }
    }
    /**
     * 获取严重程度图标
     */
    getSeverityIcon(severity) {
        switch (severity) {
            case 'Critical': return '🔴';
            case 'High': return '🟠';
            case 'Medium': return '🟡';
            case 'Low': return '⚪';
            default: return '❓';
        }
    }
    /**
     * 获取问题类型图标
     */
    getTypeIcon(type) {
        switch (type) {
            case 'Logic': return '🐛';
            case 'Performance': return '⚡';
            case 'Security': return '🔒';
            case 'Style': return '🎨';
            case 'Business': return '💼';
            default: return '❓';
        }
    }
    /**
     * 获取建议类型图标
     */
    getSuggestionTypeIcon(type) {
        switch (type) {
            case 'quick-fix': return '⚡';
            case 'refactor': return '🔧';
            case 'test-addition': return '🧪';
            case 'documentation': return '📚';
            default: return '💡';
        }
    }
    /**
     * 获取优先级图标
     */
    getPriorityIcon(priority) {
        if (priority > 80)
            return '🔥';
        if (priority > 60)
            return '⚠️';
        if (priority > 40)
            return '💡';
        return '📝';
    }
    /**
     * 着色文本
     */
    colorize(text, color, style) {
        if (!this.options.colorEnabled)
            return text;
        let colored = text;
        // Apply color
        switch (color) {
            case 'red':
                colored = chalk_1.default.red(text);
                break;
            case 'green':
                colored = chalk_1.default.green(text);
                break;
            case 'yellow':
                colored = chalk_1.default.yellow(text);
                break;
            case 'blue':
                colored = chalk_1.default.blue(text);
                break;
            case 'cyan':
                colored = chalk_1.default.cyan(text);
                break;
            case 'gray':
                colored = chalk_1.default.gray(text);
                break;
            case 'white':
                colored = chalk_1.default.white(text);
                break;
            default: colored = text;
        }
        // Apply style
        if (style === 'bold') {
            colored = chalk_1.default.bold(colored);
        }
        else if (style === 'italic') {
            colored = chalk_1.default.italic(colored);
        }
        else if (style === 'underline') {
            colored = chalk_1.default.underline(colored);
        }
        return colored;
    }
    /**
     * 文本换行
     */
    wordWrap(text, maxWidth, indent = '') {
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';
        words.forEach(word => {
            if ((currentLine + word).length <= maxWidth) {
                currentLine += (currentLine ? ' ' : '') + word;
            }
            else {
                if (currentLine) {
                    lines.push(currentLine);
                    currentLine = word;
                }
                else {
                    lines.push(word);
                }
            }
        });
        if (currentLine) {
            lines.push(currentLine);
        }
        return lines.map(line => indent + line).join('\n');
    }
    /**
     * 生成简化报告
     */
    generateSimpleReport(enhancedIssues) {
        const summary = {
            total: enhancedIssues.length,
            critical: enhancedIssues.filter(i => i.severity === 'Critical').length,
            high: enhancedIssues.filter(i => i.severity === 'High').length,
            medium: enhancedIssues.filter(i => i.severity === 'Medium').length,
            low: enhancedIssues.filter(i => i.severity === 'Low').length
        };
        const lines = [
            this.colorize('AI Code Review Summary', 'blue', 'bold'),
            `Total issues: ${summary.total}`,
            `Critical: ${this.colorize(summary.critical.toString(), 'red')} | High: ${this.colorize(summary.high.toString(), 'yellow')} | Medium: ${this.colorize(summary.medium.toString(), 'blue')} | Low: ${this.colorize(summary.low.toString(), 'gray')}`
        ];
        if (summary.critical > 0) {
            lines.push('');
            lines.push(this.colorize('🚨 Critical Issues:', 'red', 'bold'));
            enhancedIssues
                .filter(i => i.severity === 'Critical')
                .slice(0, 3)
                .forEach((issue, index) => {
                lines.push(`${index + 1}. ${issue.message} (${issue.filePath}:${issue.line})`);
            });
        }
        return lines.join('\n');
    }
}
exports.TerminalFormatter = TerminalFormatter;
//# sourceMappingURL=terminal.js.map