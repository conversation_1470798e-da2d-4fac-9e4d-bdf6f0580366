import { EnhancedIssue, IssueGroup } from '../../core/suggestions';
import { AnalysisResult } from '../../types';
export interface TerminalReportOptions {
    showDetails: boolean;
    showSuggestions: boolean;
    showContext: boolean;
    showStatistics: boolean;
    groupByType: boolean;
    maxIssuesPerType?: number;
    colorEnabled: boolean;
}
export interface ReportSummary {
    totalFiles: number;
    totalIssues: number;
    criticalIssues: number;
    highIssues: number;
    mediumIssues: number;
    lowIssues: number;
    issuesByType: Record<string, number>;
    topIssueFiles: {
        file: string;
        count: number;
    }[];
}
export declare class TerminalFormatter {
    private options;
    constructor(options?: Partial<TerminalReportOptions>);
    /**
     * 生成完整的终端报告
     */
    generateReport(enhancedIssues: EnhancedIssue[], issueGroups: IssueGroup[], analysisResults: AnalysisResult[]): string;
    /**
     * 生成报告头部
     */
    private generateHeader;
    /**
     * 生成摘要信息
     */
    private generateSummary;
    /**
     * 生成统计信息
     */
    private generateStatistics;
    /**
     * 生成分组问题报告
     */
    private generateGroupedIssues;
    /**
     * 生成问题列表报告
     */
    private generateIssuesList;
    /**
     * 格式化单个问题
     */
    private formatIssue;
    /**
     * 格式化问题详情
     */
    private formatIssueDetails;
    /**
     * 生成修复建议摘要
     */
    private generateFixSuggestionsSummary;
    /**
     * 生成结论和建议
     */
    private generateConclusion;
    /**
     * 计算摘要统计
     */
    private calculateSummary;
    /**
     * 获取严重程度颜色
     */
    private getSeverityColor;
    /**
     * 获取严重程度图标
     */
    private getSeverityIcon;
    /**
     * 获取问题类型图标
     */
    private getTypeIcon;
    /**
     * 获取建议类型图标
     */
    private getSuggestionTypeIcon;
    /**
     * 获取优先级图标
     */
    private getPriorityIcon;
    /**
     * 着色文本
     */
    private colorize;
    /**
     * 文本换行
     */
    private wordWrap;
    /**
     * 生成简化报告
     */
    generateSimpleReport(enhancedIssues: EnhancedIssue[]): string;
}
//# sourceMappingURL=terminal.d.ts.map