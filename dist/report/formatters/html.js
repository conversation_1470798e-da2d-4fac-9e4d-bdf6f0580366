"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.HTMLFormatter = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const logger_1 = require("../../utils/logger");
class HTMLFormatter {
    constructor(options = {}) {
        this.options = {
            title: 'AI Code Review Report',
            includeCharts: true,
            includeCodeContext: true,
            includeFixSuggestions: true,
            theme: 'light',
            outputPath: './ai-review-report.html',
            embedAssets: true,
            ...options
        };
    }
    /**
     * 生成HTML报告
     */
    async generateReport(enhancedIssues, issueGroups, analysisResults) {
        try {
            logger_1.logger.info(`Generating HTML report: ${this.options.outputPath}`);
            // 确保输出目录存在
            const outputDir = path.dirname(this.options.outputPath);
            await fs.ensureDir(outputDir);
            const chartData = this.generateChartData(enhancedIssues, analysisResults);
            const html = this.buildHTML(enhancedIssues, issueGroups, analysisResults, chartData);
            await fs.writeFile(this.options.outputPath, html, 'utf-8');
            logger_1.logger.info(`HTML report saved to: ${this.options.outputPath}`);
            return this.options.outputPath;
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate HTML report: ${error}`);
            throw error;
        }
    }
    /**
     * 构建完整的HTML文档
     */
    buildHTML(enhancedIssues, issueGroups, analysisResults, chartData) {
        const head = this.buildHTMLHead();
        const body = this.buildHTMLBody(enhancedIssues, issueGroups, analysisResults, chartData);
        return `<!DOCTYPE html>
<html lang="en" data-theme="${this.options.theme}">
${head}
${body}
</html>`;
    }
    /**
     * 构建HTML头部
     */
    buildHTMLHead() {
        return `<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.options.title}</title>
    ${this.getCSS()}
    ${this.options.includeCharts ? this.getChartJS() : ''}
</head>`;
    }
    /**
     * 构建HTML主体
     */
    buildHTMLBody(enhancedIssues, issueGroups, analysisResults, chartData) {
        const sections = [
            this.buildHeader(enhancedIssues, analysisResults),
            this.buildSummarySection(enhancedIssues, analysisResults),
            this.options.includeCharts ? this.buildChartsSection(chartData) : '',
            this.buildIssuesSection(enhancedIssues, issueGroups),
            this.options.includeFixSuggestions ? this.buildFixSuggestionsSection(enhancedIssues) : '',
            this.buildFooter()
        ].filter(Boolean);
        return `<body>
    <div class="container">
        ${sections.join('\n        ')}
    </div>
    ${this.getJavaScript()}
</body>`;
    }
    /**
     * 构建报告头部
     */
    buildHeader(enhancedIssues, analysisResults) {
        const timestamp = new Date().toLocaleString();
        const totalIssues = enhancedIssues.length;
        const criticalCount = enhancedIssues.filter(i => i.severity === 'Critical').length;
        const statusClass = criticalCount > 0 ? 'critical' :
            enhancedIssues.filter(i => i.severity === 'High').length > 0 ? 'warning' : 'success';
        return `<header class="report-header">
    <div class="header-content">
        <h1>🤖 ${this.options.title}</h1>
        <div class="report-meta">
            <div class="meta-item">
                <span class="label">Generated:</span>
                <span class="value">${timestamp}</span>
            </div>
            <div class="meta-item">
                <span class="label">Files Analyzed:</span>
                <span class="value">${analysisResults.length}</span>
            </div>
            <div class="meta-item">
                <span class="label">Total Issues:</span>
                <span class="value badge ${statusClass}">${totalIssues}</span>
            </div>
        </div>
    </div>
</header>`;
    }
    /**
     * 构建摘要部分
     */
    buildSummarySection(enhancedIssues, analysisResults) {
        const severityCounts = {
            Critical: enhancedIssues.filter(i => i.severity === 'Critical').length,
            High: enhancedIssues.filter(i => i.severity === 'High').length,
            Medium: enhancedIssues.filter(i => i.severity === 'Medium').length,
            Low: enhancedIssues.filter(i => i.severity === 'Low').length
        };
        const typeCounts = enhancedIssues.reduce((acc, issue) => {
            acc[issue.type] = (acc[issue.type] || 0) + 1;
            return acc;
        }, {});
        return `<section class="summary-section">
    <h2>📊 Analysis Summary</h2>
    
    <div class="summary-grid">
        <div class="summary-card">
            <h3>Issues by Severity</h3>
            <div class="severity-list">
                <div class="severity-item critical">
                    <span class="severity-icon">🔴</span>
                    <span class="severity-label">Critical</span>
                    <span class="severity-count">${severityCounts.Critical}</span>
                </div>
                <div class="severity-item high">
                    <span class="severity-icon">🟠</span>
                    <span class="severity-label">High</span>
                    <span class="severity-count">${severityCounts.High}</span>
                </div>
                <div class="severity-item medium">
                    <span class="severity-icon">🟡</span>
                    <span class="severity-label">Medium</span>
                    <span class="severity-count">${severityCounts.Medium}</span>
                </div>
                <div class="severity-item low">
                    <span class="severity-icon">⚪</span>
                    <span class="severity-label">Low</span>
                    <span class="severity-count">${severityCounts.Low}</span>
                </div>
            </div>
        </div>

        <div class="summary-card">
            <h3>Issues by Type</h3>
            <div class="type-list">
                ${Object.entries(typeCounts)
            .sort(([, a], [, b]) => b - a)
            .map(([type, count]) => {
            const icon = this.getTypeIcon(type);
            return `<div class="type-item">
                        <span class="type-icon">${icon}</span>
                        <span class="type-label">${type}</span>
                        <span class="type-count">${count}</span>
                    </div>`;
        }).join('')}
            </div>
        </div>
    </div>
</section>`;
    }
    /**
     * 构建图表部分
     */
    buildChartsSection(chartData) {
        return `<section class="charts-section">
    <h2>📈 Visual Analysis</h2>
    
    <div class="charts-grid">
        <div class="chart-container">
            <h3>Issues by Severity</h3>
            <canvas id="severityChart" width="400" height="200"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>Issues by Type</h3>
            <canvas id="typeChart" width="400" height="200"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>Issues per File</h3>
            <canvas id="fileChart" width="400" height="200"></canvas>
        </div>
    </div>

    <script>
        const chartData = ${JSON.stringify(chartData)};
    </script>
</section>`;
    }
    /**
     * 构建问题部分
     */
    buildIssuesSection(enhancedIssues, issueGroups) {
        return `<section class="issues-section">
    <h2>🔍 Detailed Issues</h2>
    
    <div class="filters">
        <div class="filter-group">
            <label>Filter by Severity:</label>
            <select id="severityFilter">
                <option value="">All Severities</option>
                <option value="Critical">Critical</option>
                <option value="High">High</option>
                <option value="Medium">Medium</option>
                <option value="Low">Low</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label>Filter by Type:</label>
            <select id="typeFilter">
                <option value="">All Types</option>
                <option value="Logic">Logic</option>
                <option value="Performance">Performance</option>
                <option value="Security">Security</option>
                <option value="Style">Style</option>
                <option value="Business">Business</option>
            </select>
        </div>
        
        <div class="filter-group">
            <input type="text" id="searchFilter" placeholder="Search issues...">
        </div>
    </div>

    <div class="issues-container">
        ${issueGroups.map((group, index) => this.buildIssueGroup(group, index)).join('')}
    </div>
</section>`;
    }
    /**
     * 构建问题组
     */
    buildIssueGroup(group, index) {
        return `<div class="issue-group" data-group="${index}">
    <div class="group-header" onclick="toggleGroup(${index})">
        <h3>
            <span class="group-icon">${this.getPriorityIcon(group.overallPriority)}</span>
            ${group.category}
            <span class="issue-count">${group.issues.length} issues</span>
        </h3>
        <div class="group-meta">
            <span class="estimated-time">~${group.estimatedFixTime}</span>
            <span class="toggle-icon">▼</span>
        </div>
    </div>
    
    <div class="group-summary">
        <p>${group.summary}</p>
    </div>
    
    <div class="group-issues" id="group-${index}">
        ${group.issues.map((issue, issueIndex) => this.buildIssueCard(issue, issueIndex)).join('')}
    </div>
</div>`;
    }
    /**
     * 构建问题卡片
     */
    buildIssueCard(issue, index) {
        const severityClass = issue.severity.toLowerCase();
        const typeIcon = this.getTypeIcon(issue.type);
        const severityIcon = this.getSeverityIcon(issue.severity);
        return `<div class="issue-card ${severityClass}" data-severity="${issue.severity}" data-type="${issue.type}">
    <div class="issue-header">
        <div class="issue-icons">
            <span class="severity-badge ${severityClass}">${severityIcon} ${issue.severity}</span>
            <span class="type-badge">${typeIcon} ${issue.type}</span>
        </div>
        <div class="issue-priority">
            Priority: <span class="priority-score">${issue.priority}</span>
        </div>
    </div>
    
    <div class="issue-content">
        <h4 class="issue-title">${this.escapeHtml(issue.message)}</h4>
        
        <div class="issue-location">
            <span class="file-icon">📁</span>
            <code>${issue.filePath}:${issue.line}${issue.column ? `:${issue.column}` : ''}</code>
        </div>
        
        ${issue.blame ? `<div class="issue-blame">
            <span class="author-icon">👤</span>
            <span class="author">${issue.blame.author}</span>
            <span class="commit-time">${new Date(issue.blame.commitTime).toLocaleDateString()}</span>
        </div>` : ''}
        
        <div class="issue-description">
            <p>${this.escapeHtml(issue.description)}</p>
        </div>

        ${this.options.includeCodeContext && issue.context.surroundingLines.length > 0 ?
            this.buildCodeContext(issue.context.surroundingLines) : ''}

        ${this.options.includeFixSuggestions && issue.fixSuggestions.length > 0 ?
            this.buildFixSuggestionsCard(issue.fixSuggestions) : ''}
        
        ${issue.references && issue.references.length > 0 ?
            this.buildReferencesCard(issue.references) : ''}
    </div>
</div>`;
    }
    /**
     * 构建代码上下文
     */
    buildCodeContext(surroundingLines) {
        return `<div class="code-context">
    <h5>📄 Code Context</h5>
    <pre class="code-block">${surroundingLines.map(line => this.escapeHtml(line)).join('\n')}</pre>
</div>`;
    }
    /**
     * 构建修复建议卡片
     */
    buildFixSuggestionsCard(suggestions) {
        return `<div class="fix-suggestions">
    <h5>🔧 Fix Suggestions</h5>
    <div class="suggestions-list">
        ${suggestions.slice(0, 3).map((suggestion, index) => `
        <div class="suggestion-item">
            <div class="suggestion-header">
                <span class="suggestion-type">${this.getSuggestionIcon(suggestion.type)} ${suggestion.title}</span>
                <div class="suggestion-meta">
                    <span class="effort ${suggestion.effort}">${suggestion.effort} effort</span>
                    <span class="impact ${suggestion.impact}">${suggestion.impact} impact</span>
                    ${suggestion.automated ? '<span class="automated">🤖 Automated</span>' : ''}
                </div>
            </div>
            <p class="suggestion-description">${this.escapeHtml(suggestion.description)}</p>
            ${suggestion.codeExample ? `
            <div class="code-example">
                <div class="code-before">
                    <label>Before:</label>
                    <pre><code>${this.escapeHtml(suggestion.codeExample.before)}</code></pre>
                </div>
                <div class="code-after">
                    <label>After:</label>
                    <pre><code>${this.escapeHtml(suggestion.codeExample.after)}</code></pre>
                </div>
            </div>` : ''}
        </div>`).join('')}
        ${suggestions.length > 3 ? `<p class="more-suggestions">... and ${suggestions.length - 3} more suggestions</p>` : ''}
    </div>
</div>`;
    }
    /**
     * 构建参考资料卡片
     */
    buildReferencesCard(references) {
        return `<div class="references">
    <h5>📚 References</h5>
    <ul class="references-list">
        ${references.map(ref => `<li><a href="${ref}" target="_blank">${ref}</a></li>`).join('')}
    </ul>
</div>`;
    }
    /**
     * 构建修复建议部分
     */
    buildFixSuggestionsSection(enhancedIssues) {
        const allSuggestions = enhancedIssues.flatMap(issue => issue.fixSuggestions.map(suggestion => ({
            ...suggestion,
            issueId: issue.id,
            issueFile: issue.filePath
        })));
        const suggestionsByType = allSuggestions.reduce((acc, suggestion) => {
            if (!acc[suggestion.type])
                acc[suggestion.type] = [];
            acc[suggestion.type].push(suggestion);
            return acc;
        }, {});
        const automatedSuggestions = allSuggestions.filter(s => s.automated);
        return `<section class="fix-suggestions-section">
    <h2>🔧 Fix Suggestions Overview</h2>
    
    <div class="suggestions-summary">
        <div class="summary-stat">
            <span class="stat-number">${allSuggestions.length}</span>
            <span class="stat-label">Total Suggestions</span>
        </div>
        <div class="summary-stat">
            <span class="stat-number">${automatedSuggestions.length}</span>
            <span class="stat-label">Automated Fixes</span>
        </div>
        <div class="summary-stat">
            <span class="stat-number">${Object.keys(suggestionsByType).length}</span>
            <span class="stat-label">Suggestion Types</span>
        </div>
    </div>

    <div class="suggestions-breakdown">
        <h3>Suggestions by Type</h3>
        <div class="suggestion-types">
            ${Object.entries(suggestionsByType).map(([type, suggestions]) => `
            <div class="suggestion-type-card">
                <h4>${this.getSuggestionIcon(type)} ${type}</h4>
                <p class="type-count">${suggestions.length} suggestions</p>
                <div class="type-details">
                    <span class="automated-count">${suggestions.filter(s => s.automated).length} automated</span>
                </div>
            </div>`).join('')}
        </div>
    </div>
</section>`;
    }
    /**
     * 构建页脚
     */
    buildFooter() {
        return `<footer class="report-footer">
    <div class="footer-content">
        <p>Generated by AI Code Review Tool</p>
        <p class="disclaimer">
            This report is generated by AI analysis. Please review suggestions carefully and test thoroughly before applying changes.
        </p>
    </div>
</footer>`;
    }
    /**
     * 生成图表数据
     */
    generateChartData(enhancedIssues, analysisResults) {
        // 严重程度数据
        const severityCounts = {
            Critical: enhancedIssues.filter(i => i.severity === 'Critical').length,
            High: enhancedIssues.filter(i => i.severity === 'High').length,
            Medium: enhancedIssues.filter(i => i.severity === 'Medium').length,
            Low: enhancedIssues.filter(i => i.severity === 'Low').length
        };
        // 类型数据
        const typeCounts = enhancedIssues.reduce((acc, issue) => {
            acc[issue.type] = (acc[issue.type] || 0) + 1;
            return acc;
        }, {});
        // 文件数据
        const fileCounts = enhancedIssues.reduce((acc, issue) => {
            const fileName = issue.filePath.split('/').pop() || issue.filePath;
            acc[fileName] = (acc[fileName] || 0) + 1;
            return acc;
        }, {});
        const topFiles = Object.entries(fileCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10);
        return {
            severityChart: {
                labels: Object.keys(severityCounts),
                data: Object.values(severityCounts),
                colors: ['#dc3545', '#fd7e14', '#ffc107', '#6c757d']
            },
            typeChart: {
                labels: Object.keys(typeCounts),
                data: Object.values(typeCounts),
                colors: ['#007bff', '#28a745', '#dc3545', '#17a2b8', '#6f42c1']
            },
            fileChart: {
                labels: topFiles.map(([file]) => file),
                data: topFiles.map(([, count]) => count)
            }
        };
    }
    /**
     * 获取CSS样式
     */
    getCSS() {
        return `<style>
    :root {
        --primary-color: #007bff;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --text-color: #495057;
        --bg-color: #ffffff;
    }

    [data-theme="dark"] {
        --bg-color: #2d3748;
        --text-color: #e2e8f0;
        --border-color: #4a5568;
        --light-color: #4a5568;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        background-color: var(--bg-color);
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .report-header {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        padding: 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
    }

    .header-content h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: 300;
    }

    .report-meta {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .meta-item .label {
        font-size: 0.85rem;
        opacity: 0.8;
    }

    .meta-item .value {
        font-weight: 600;
        font-size: 1.1rem;
    }

    .badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .badge.success { background: var(--success-color); }
    .badge.warning { background: var(--warning-color); color: #000; }
    .badge.critical { background: var(--danger-color); }

    section {
        background: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    h2 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        font-size: 1.75rem;
        font-weight: 600;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .summary-card {
        background: var(--light-color);
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .summary-card h3 {
        margin-bottom: 1rem;
        color: var(--text-color);
    }

    .severity-item, .type-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .severity-item:last-child, .type-item:last-child {
        border-bottom: none;
    }

    .severity-icon, .type-icon {
        font-size: 1.2rem;
        margin-right: 0.5rem;
    }

    .severity-count, .type-count {
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        background: var(--primary-color);
        color: white;
        border-radius: 12px;
        font-size: 0.85rem;
    }

    .charts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .chart-container {
        text-align: center;
    }

    .chart-container h3 {
        margin-bottom: 1rem;
        color: var(--text-color);
    }

    .filters {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .filter-group label {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .filter-group select, .filter-group input {
        padding: 0.5rem;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: var(--bg-color);
        color: var(--text-color);
    }

    .issue-group {
        margin-bottom: 1.5rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        overflow: hidden;
    }

    .group-header {
        background: var(--light-color);
        padding: 1rem;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--border-color);
    }

    .group-header:hover {
        background: var(--border-color);
    }

    .group-header h3 {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
    }

    .issue-count {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .group-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .estimated-time {
        color: var(--info-color);
        font-weight: 600;
    }

    .group-issues {
        padding: 1rem;
    }

    .issue-card {
        background: var(--bg-color);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border-left: 4px solid var(--primary-color);
    }

    .issue-card.critical { border-left-color: var(--danger-color); }
    .issue-card.high { border-left-color: var(--warning-color); }
    .issue-card.medium { border-left-color: var(--info-color); }
    .issue-card.low { border-left-color: var(--border-color); }

    .issue-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .issue-icons {
        display: flex;
        gap: 0.5rem;
    }

    .severity-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
    }

    .severity-badge.critical { background: var(--danger-color); }
    .severity-badge.high { background: var(--warning-color); color: #000; }
    .severity-badge.medium { background: var(--info-color); }
    .severity-badge.low { background: var(--border-color); color: var(--text-color); }

    .type-badge {
        padding: 0.25rem 0.5rem;
        background: var(--light-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        font-size: 0.8rem;
        color: var(--text-color);
    }

    .priority-score {
        font-weight: 600;
        color: var(--primary-color);
    }

    .issue-title {
        color: var(--text-color);
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .issue-location {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
        color: var(--info-color);
        font-family: 'Courier New', monospace;
    }

    .issue-blame {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        color: var(--text-color);
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .issue-description {
        margin-bottom: 1rem;
        line-height: 1.6;
    }

    .code-context {
        background: #f8f9fa;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .code-context h5 {
        margin-bottom: 0.5rem;
        color: var(--text-color);
    }

    .code-block {
        background: #f1f3f4;
        padding: 0.75rem;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        overflow-x: auto;
        margin: 0;
    }

    .fix-suggestions {
        border: 1px solid var(--success-color);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        background: rgba(40, 167, 69, 0.05);
    }

    .fix-suggestions h5 {
        color: var(--success-color);
        margin-bottom: 1rem;
    }

    .suggestion-item {
        background: white;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .suggestion-item:last-child {
        margin-bottom: 0;
    }

    .suggestion-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .suggestion-type {
        font-weight: 600;
        color: var(--primary-color);
    }

    .suggestion-meta {
        display: flex;
        gap: 0.5rem;
    }

    .effort, .impact {
        padding: 0.15rem 0.4rem;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .effort.low, .impact.low { background: #d4edda; color: #155724; }
    .effort.medium, .impact.medium { background: #fff3cd; color: #856404; }
    .effort.high, .impact.high { background: #f8d7da; color: #721c24; }

    .automated {
        background: var(--info-color);
        color: white;
        padding: 0.15rem 0.4rem;
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .code-example {
        margin-top: 1rem;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .code-before, .code-after {
        border: 1px solid var(--border-color);
        border-radius: 4px;
        overflow: hidden;
    }

    .code-before label {
        background: #f8d7da;
        color: #721c24;
        padding: 0.25rem 0.5rem;
        display: block;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .code-after label {
        background: #d4edda;
        color: #155724;
        padding: 0.25rem 0.5rem;
        display: block;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .code-before pre, .code-after pre {
        margin: 0;
        padding: 0.75rem;
        background: #f8f9fa;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
    }

    .references {
        border: 1px solid var(--info-color);
        border-radius: 8px;
        padding: 1rem;
        background: rgba(23, 162, 184, 0.05);
    }

    .references h5 {
        color: var(--info-color);
        margin-bottom: 0.5rem;
    }

    .references-list {
        list-style: none;
    }

    .references-list li {
        padding: 0.25rem 0;
    }

    .references-list a {
        color: var(--info-color);
        text-decoration: none;
        font-size: 0.9rem;
    }

    .references-list a:hover {
        text-decoration: underline;
    }

    .report-footer {
        background: var(--light-color);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        margin-top: 2rem;
    }

    .disclaimer {
        font-size: 0.9rem;
        color: var(--text-color);
        opacity: 0.8;
        margin-top: 0.5rem;
    }

    @media (max-width: 768px) {
        .container {
            padding: 10px;
        }
        
        .report-meta {
            flex-direction: column;
            gap: 1rem;
        }
        
        .charts-grid {
            grid-template-columns: 1fr;
        }
        
        .code-example {
            grid-template-columns: 1fr;
        }
    }
</style>`;
    }
    /**
     * 获取Chart.js库
     */
    getChartJS() {
        if (this.options.embedAssets) {
            // 在实际实现中，这里应该包含Chart.js的完整代码
            return `<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>`;
        }
        return `<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>`;
    }
    /**
     * 获取JavaScript代码
     */
    getJavaScript() {
        return `<script>
    // 切换分组显示/隐藏
    function toggleGroup(groupIndex) {
        const groupElement = document.getElementById('group-' + groupIndex);
        const toggleIcon = document.querySelector('[data-group="' + groupIndex + '"] .toggle-icon');
        
        if (groupElement.style.display === 'none') {
            groupElement.style.display = 'block';
            toggleIcon.textContent = '▼';
        } else {
            groupElement.style.display = 'none';
            toggleIcon.textContent = '▶';
        }
    }

    // 过滤功能
    function setupFilters() {
        const severityFilter = document.getElementById('severityFilter');
        const typeFilter = document.getElementById('typeFilter');
        const searchFilter = document.getElementById('searchFilter');

        function applyFilters() {
            const severity = severityFilter.value;
            const type = typeFilter.value;
            const search = searchFilter.value.toLowerCase();

            const issueCards = document.querySelectorAll('.issue-card');
            
            issueCards.forEach(card => {
                const cardSeverity = card.dataset.severity;
                const cardType = card.dataset.type;
                const cardText = card.textContent.toLowerCase();

                const severityMatch = !severity || cardSeverity === severity;
                const typeMatch = !type || cardType === type;
                const searchMatch = !search || cardText.includes(search);

                card.style.display = (severityMatch && typeMatch && searchMatch) ? 'block' : 'none';
            });

            // 隐藏空的组
            document.querySelectorAll('.issue-group').forEach(group => {
                const visibleCards = group.querySelectorAll('.issue-card:not([style*="display: none"])');
                group.style.display = visibleCards.length > 0 ? 'block' : 'none';
            });
        }

        severityFilter.addEventListener('change', applyFilters);
        typeFilter.addEventListener('change', applyFilters);
        searchFilter.addEventListener('input', applyFilters);
    }

    // 初始化图表
    function initializeCharts() {
        if (typeof Chart !== 'undefined' && window.chartData) {
            // 严重程度饼图
            const severityCtx = document.getElementById('severityChart');
            if (severityCtx) {
                new Chart(severityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: chartData.severityChart.labels,
                        datasets: [{
                            data: chartData.severityChart.data,
                            backgroundColor: chartData.severityChart.colors,
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 类型柱状图
            const typeCtx = document.getElementById('typeChart');
            if (typeCtx) {
                new Chart(typeCtx, {
                    type: 'bar',
                    data: {
                        labels: chartData.typeChart.labels,
                        datasets: [{
                            label: 'Issues',
                            data: chartData.typeChart.data,
                            backgroundColor: chartData.typeChart.colors,
                            borderWidth: 1,
                            borderColor: chartData.typeChart.colors
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }

            // 文件水平柱状图
            const fileCtx = document.getElementById('fileChart');
            if (fileCtx) {
                new Chart(fileCtx, {
                    type: 'horizontalBar',
                    data: {
                        labels: chartData.fileChart.labels,
                        datasets: [{
                            label: 'Issues per File',
                            data: chartData.fileChart.data,
                            backgroundColor: '#17a2b8',
                            borderColor: '#17a2b8',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    // DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        setupFilters();
        initializeCharts();
        
        // 默认折叠所有分组
        document.querySelectorAll('.issue-group').forEach((group, index) => {
            const groupIssues = group.querySelector('.group-issues');
            groupIssues.style.display = 'none';
            group.querySelector('.toggle-icon').textContent = '▶';
        });
    });
</script>`;
    }
    /**
     * 工具函数
     */
    escapeHtml(text) {
        const div = { innerHTML: '' };
        div.textContent = text;
        return div.innerHTML;
    }
    getTypeIcon(type) {
        const icons = {
            Logic: '🐛',
            Performance: '⚡',
            Security: '🔒',
            Style: '🎨',
            Business: '💼'
        };
        return icons[type] || '❓';
    }
    getSeverityIcon(severity) {
        const icons = {
            Critical: '🔴',
            High: '🟠',
            Medium: '🟡',
            Low: '⚪'
        };
        return icons[severity] || '❓';
    }
    getSuggestionIcon(type) {
        const icons = {
            'quick-fix': '⚡',
            refactor: '🔧',
            'test-addition': '🧪',
            documentation: '📚'
        };
        return icons[type] || '💡';
    }
    getPriorityIcon(priority) {
        if (priority > 80)
            return '🔥';
        if (priority > 60)
            return '⚠️';
        if (priority > 40)
            return '💡';
        return '📝';
    }
}
exports.HTMLFormatter = HTMLFormatter;
//# sourceMappingURL=html.js.map