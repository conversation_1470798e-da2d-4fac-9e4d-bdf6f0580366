"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONFormatter = void 0;
const fs = __importStar(require("fs-extra"));
const logger_1 = require("../../utils/logger");
class JSONFormatter {
    constructor(options = {}) {
        this.options = {
            outputPath: './ai-review-report.json',
            includeContext: true,
            includeFixSuggestions: true,
            includeBlame: true,
            includeStatistics: true,
            minified: false,
            apiVersion: '1.0',
            ...options
        };
    }
    /**
     * 生成JSON报告
     */
    async generateReport(enhancedIssues, issueGroups, analysisResults, analysisTime) {
        try {
            logger_1.logger.info(`Generating JSON report: ${this.options.outputPath}`);
            // 确保输出目录存在
            const path = require('path');
            const outputDir = path.dirname(this.options.outputPath);
            await fs.ensureDir(outputDir);
            const report = this.buildJSONReport(enhancedIssues, issueGroups, analysisResults, analysisTime);
            const jsonString = this.options.minified
                ? JSON.stringify(report)
                : JSON.stringify(report, null, 2);
            await fs.writeFile(this.options.outputPath, jsonString, 'utf-8');
            logger_1.logger.info(`JSON report saved to: ${this.options.outputPath}`);
            return this.options.outputPath;
        }
        catch (error) {
            logger_1.logger.error(`Failed to generate JSON report: ${error}`);
            throw error;
        }
    }
    /**
     * 构建JSON报告结构
     */
    buildJSONReport(enhancedIssues, issueGroups, analysisResults, analysisTime) {
        const metadata = this.buildMetadata();
        const summary = this.buildSummary(enhancedIssues, analysisResults, analysisTime);
        const files = this.buildFileReports(enhancedIssues, analysisResults);
        const statistics = this.options.includeStatistics
            ? this.buildStatistics(enhancedIssues, analysisResults, analysisTime)
            : undefined;
        return {
            metadata,
            summary,
            files,
            statistics,
            groups: issueGroups
        };
    }
    /**
     * 构建元数据
     */
    buildMetadata() {
        return {
            version: '1.0.0',
            generatedAt: new Date().toISOString(),
            tool: 'AI Code Review',
            apiVersion: this.options.apiVersion
        };
    }
    /**
     * 构建摘要信息
     */
    buildSummary(enhancedIssues, analysisResults, analysisTime) {
        const issuesBySeverity = enhancedIssues.reduce((acc, issue) => {
            acc[issue.severity] = (acc[issue.severity] || 0) + 1;
            return acc;
        }, {});
        const issuesByType = enhancedIssues.reduce((acc, issue) => {
            acc[issue.type] = (acc[issue.type] || 0) + 1;
            return acc;
        }, {});
        return {
            totalFiles: analysisResults.length,
            totalIssues: enhancedIssues.length,
            issuesBySeverity,
            issuesByType,
            analysisTime
        };
    }
    /**
     * 构建文件报告
     */
    buildFileReports(enhancedIssues, analysisResults) {
        // 按文件分组问题
        const issuesByFile = enhancedIssues.reduce((acc, issue) => {
            if (!acc[issue.filePath]) {
                acc[issue.filePath] = [];
            }
            acc[issue.filePath].push(issue);
            return acc;
        }, {});
        // 为每个分析的文件创建报告
        const fileReports = [];
        analysisResults.forEach(result => {
            const fileIssues = issuesByFile[result.file] || [];
            const severityDistribution = fileIssues.reduce((acc, issue) => {
                acc[issue.severity] = (acc[issue.severity] || 0) + 1;
                return acc;
            }, {});
            const typeDistribution = fileIssues.reduce((acc, issue) => {
                acc[issue.type] = (acc[issue.type] || 0) + 1;
                return acc;
            }, {});
            // 过滤问题数据（移除不需要的字段）
            const filteredIssues = fileIssues.map(issue => this.filterIssueData(issue));
            fileReports.push({
                filePath: result.file,
                issues: filteredIssues,
                statistics: {
                    issueCount: fileIssues.length,
                    severityDistribution,
                    typeDistribution
                }
            });
        });
        return fileReports.sort((a, b) => b.issues.length - a.issues.length);
    }
    /**
     * 构建统计信息
     */
    buildStatistics(enhancedIssues, analysisResults, analysisTime) {
        const performance = this.calculatePerformanceStats(enhancedIssues, analysisResults, analysisTime);
        const quality = this.calculateQualityStats(enhancedIssues);
        const trends = this.calculateTrendStats(enhancedIssues);
        return {
            performance,
            quality,
            trends
        };
    }
    /**
     * 计算性能统计
     */
    calculatePerformanceStats(enhancedIssues, analysisResults, analysisTime) {
        const cacheHits = analysisResults.filter(r => r.metadata?.fromCache).length;
        const cacheHitRate = analysisResults.length > 0 ? cacheHits / analysisResults.length : 0;
        const averageIssuesPerFile = analysisResults.length > 0 ? enhancedIssues.length / analysisResults.length : 0;
        return {
            analysisTime: analysisTime || 0,
            cacheHitRate: Math.round(cacheHitRate * 100) / 100,
            filesAnalyzed: analysisResults.length,
            averageIssuesPerFile: Math.round(averageIssuesPerFile * 100) / 100
        };
    }
    /**
     * 计算质量统计
     */
    calculateQualityStats(enhancedIssues) {
        const complexities = enhancedIssues
            .map(issue => issue.context.complexity)
            .filter(c => c > 0);
        const averageComplexity = complexities.length > 0
            ? complexities.reduce((a, b) => a + b, 0) / complexities.length
            : 0;
        const highestComplexity = Math.max(...complexities, 0);
        const highComplexityFiles = enhancedIssues
            .filter(issue => issue.context.complexity > 10)
            .map(issue => issue.filePath);
        const uniqueHighComplexityFiles = [...new Set(highComplexityFiles)];
        const criticalIssuesCovered = enhancedIssues.filter(i => i.severity === 'Critical').length;
        const automatedFixes = enhancedIssues
            .flatMap(issue => issue.fixSuggestions)
            .filter(suggestion => suggestion.automated).length;
        return {
            codeComplexity: {
                average: Math.round(averageComplexity * 100) / 100,
                highest: highestComplexity,
                files: uniqueHighComplexityFiles
            },
            coverage: {
                criticalIssuesCovered,
                automatedFixesAvailable: automatedFixes
            }
        };
    }
    /**
     * 计算趋势统计
     */
    calculateTrendStats(enhancedIssues) {
        // 最常见的问题类型
        const typeCounts = enhancedIssues.reduce((acc, issue) => {
            acc[issue.type] = (acc[issue.type] || 0) + 1;
            return acc;
        }, {});
        const mostCommonIssueType = Object.entries(typeCounts)
            .sort(([, a], [, b]) => b - a)[0]?.[0] || 'None';
        // 问题最多的文件
        const fileCounts = enhancedIssues.reduce((acc, issue) => {
            acc[issue.filePath] = (acc[issue.filePath] || 0) + 1;
            return acc;
        }, {});
        const mostProblematicFiles = Object.entries(fileCounts)
            .map(([file, issues]) => ({ file, issues }))
            .sort((a, b) => b.issues - a.issues)
            .slice(0, 10);
        // 作者统计
        const authorCounts = enhancedIssues
            .filter(issue => issue.blame?.author)
            .reduce((acc, issue) => {
            const author = issue.blame.author;
            acc[author] = (acc[author] || 0) + 1;
            return acc;
        }, {});
        const authorStatistics = Object.entries(authorCounts)
            .map(([author, issues]) => ({ author, issues }))
            .sort((a, b) => b.issues - a.issues)
            .slice(0, 10);
        return {
            mostCommonIssueType,
            mostProblematicFiles,
            authorStatistics
        };
    }
    /**
     * 过滤问题数据
     */
    filterIssueData(issue) {
        const filtered = { ...issue };
        // 根据选项过滤数据
        if (!this.options.includeContext) {
            delete filtered.context;
        }
        if (!this.options.includeFixSuggestions) {
            delete filtered.fixSuggestions;
        }
        if (!this.options.includeBlame) {
            delete filtered.blame;
        }
        return filtered;
    }
    /**
     * 生成简化的JSON报告
     */
    async generateSimpleReport(enhancedIssues, outputPath) {
        const path = outputPath || this.options.outputPath.replace('.json', '-simple.json');
        const simpleReport = {
            summary: {
                totalIssues: enhancedIssues.length,
                critical: enhancedIssues.filter(i => i.severity === 'Critical').length,
                high: enhancedIssues.filter(i => i.severity === 'High').length,
                medium: enhancedIssues.filter(i => i.severity === 'Medium').length,
                low: enhancedIssues.filter(i => i.severity === 'Low').length
            },
            issues: enhancedIssues.map(issue => ({
                file: issue.filePath,
                line: issue.line,
                type: issue.type,
                severity: issue.severity,
                message: issue.message,
                priority: issue.priority
            }))
        };
        const jsonString = this.options.minified
            ? JSON.stringify(simpleReport)
            : JSON.stringify(simpleReport, null, 2);
        await fs.writeFile(path, jsonString, 'utf-8');
        logger_1.logger.info(`Simple JSON report saved to: ${path}`);
        return path;
    }
    /**
     * 验证JSON报告格式
     */
    validateReport(reportPath) {
        return new Promise(async (resolve) => {
            const errors = [];
            try {
                if (!await fs.pathExists(reportPath)) {
                    errors.push('Report file does not exist');
                    return resolve({ valid: false, errors });
                }
                const content = await fs.readFile(reportPath, 'utf-8');
                const report = JSON.parse(content);
                // 验证必需字段
                if (!report.metadata) {
                    errors.push('Missing metadata section');
                }
                if (!report.summary) {
                    errors.push('Missing summary section');
                }
                if (!Array.isArray(report.files)) {
                    errors.push('Files section must be an array');
                }
                // 验证API版本兼容性
                if (report.metadata?.apiVersion !== this.options.apiVersion) {
                    errors.push(`API version mismatch: expected ${this.options.apiVersion}, got ${report.metadata?.apiVersion}`);
                }
                resolve({ valid: errors.length === 0, errors });
            }
            catch (error) {
                errors.push(`Failed to parse JSON: ${error}`);
                resolve({ valid: false, errors });
            }
        });
    }
    /**
     * 合并多个JSON报告
     */
    async mergeReports(reportPaths, outputPath) {
        try {
            logger_1.logger.info(`Merging ${reportPaths.length} reports into: ${outputPath}`);
            const reports = [];
            // 读取所有报告
            for (const reportPath of reportPaths) {
                if (await fs.pathExists(reportPath)) {
                    const content = await fs.readFile(reportPath, 'utf-8');
                    const report = JSON.parse(content);
                    reports.push(report);
                }
            }
            if (reports.length === 0) {
                throw new Error('No valid reports found to merge');
            }
            // 合并报告
            const mergedReport = this.combineReports(reports);
            const jsonString = this.options.minified
                ? JSON.stringify(mergedReport)
                : JSON.stringify(mergedReport, null, 2);
            await fs.writeFile(outputPath, jsonString, 'utf-8');
            logger_1.logger.info(`Merged report saved to: ${outputPath}`);
            return outputPath;
        }
        catch (error) {
            logger_1.logger.error(`Failed to merge reports: ${error}`);
            throw error;
        }
    }
    /**
     * 合并多个报告
     */
    combineReports(reports) {
        const baseReport = reports[0];
        // 合并文件列表
        const allFiles = new Map();
        reports.forEach(report => {
            report.files.forEach(file => {
                if (allFiles.has(file.filePath)) {
                    // 合并同一文件的问题
                    const existing = allFiles.get(file.filePath);
                    existing.issues.push(...file.issues);
                    // 重新计算统计信息
                    existing.statistics.issueCount = existing.issues.length;
                    existing.statistics.severityDistribution = this.recalculateSeverityDistribution(existing.issues);
                    existing.statistics.typeDistribution = this.recalculateTypeDistribution(existing.issues);
                }
                else {
                    allFiles.set(file.filePath, file);
                }
            });
        });
        // 重新计算摘要
        const allIssues = Array.from(allFiles.values()).flatMap(file => file.issues);
        // Convert FileReport[] to AnalysisResult[] for compatibility
        const analysisResults = Array.from(allFiles.values()).map(file => ({
            file: file.filePath,
            issues: [],
            statistics: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            metadata: {
                analyzedAt: new Date().toISOString(),
                aiModel: 'merged',
                fromCache: false
            }
        }));
        const summary = this.buildSummary(allIssues, analysisResults, undefined);
        return {
            metadata: {
                ...baseReport.metadata,
                generatedAt: new Date().toISOString()
            },
            summary,
            files: Array.from(allFiles.values()),
            statistics: this.buildStatistics(allIssues, analysisResults, undefined),
            groups: [] // 分组信息在合并时会丢失
        };
    }
    /**
     * 重新计算严重程度分布
     */
    recalculateSeverityDistribution(issues) {
        return issues.reduce((acc, issue) => {
            acc[issue.severity] = (acc[issue.severity] || 0) + 1;
            return acc;
        }, {});
    }
    /**
     * 重新计算类型分布
     */
    recalculateTypeDistribution(issues) {
        return issues.reduce((acc, issue) => {
            acc[issue.type] = (acc[issue.type] || 0) + 1;
            return acc;
        }, {});
    }
    /**
     * 导出为不同格式
     */
    async exportToFormat(enhancedIssues, format, outputPath) {
        const baseName = this.options.outputPath.replace('.json', '');
        const targetPath = outputPath || `${baseName}.${format}`;
        switch (format) {
            case 'csv':
                return await this.exportToCSV(enhancedIssues, targetPath);
            case 'xml':
                return await this.exportToXML(enhancedIssues, targetPath);
            case 'yaml':
                return await this.exportToYAML(enhancedIssues, targetPath);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }
    /**
     * 导出为CSV
     */
    async exportToCSV(enhancedIssues, outputPath) {
        const headers = ['File', 'Line', 'Type', 'Severity', 'Message', 'Priority', 'Author', 'CommitTime'];
        const rows = enhancedIssues.map(issue => [
            issue.filePath,
            issue.line.toString(),
            issue.type,
            issue.severity,
            `"${issue.message.replace(/"/g, '""')}"`, // 转义CSV中的引号
            issue.priority.toString(),
            issue.blame?.author || 'Unknown',
            issue.blame?.commitTime || new Date().toISOString()
        ]);
        const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
        await fs.writeFile(outputPath, csvContent, 'utf-8');
        logger_1.logger.info(`CSV report saved to: ${outputPath}`);
        return outputPath;
    }
    /**
     * 导出为XML
     */
    async exportToXML(enhancedIssues, outputPath) {
        const xmlLines = ['<?xml version="1.0" encoding="UTF-8"?>', '<issues>'];
        enhancedIssues.forEach(issue => {
            xmlLines.push('  <issue>');
            xmlLines.push(`    <file>${this.escapeXml(issue.filePath)}</file>`);
            xmlLines.push(`    <line>${issue.line}</line>`);
            xmlLines.push(`    <type>${issue.type}</type>`);
            xmlLines.push(`    <severity>${issue.severity}</severity>`);
            xmlLines.push(`    <message>${this.escapeXml(issue.message)}</message>`);
            xmlLines.push(`    <priority>${issue.priority}</priority>`);
            if (issue.blame) {
                xmlLines.push(`    <author>${this.escapeXml(issue.blame.author)}</author>`);
                xmlLines.push(`    <commitTime>${issue.blame.commitTime}</commitTime>`);
            }
            xmlLines.push('  </issue>');
        });
        xmlLines.push('</issues>');
        const xmlContent = xmlLines.join('\n');
        await fs.writeFile(outputPath, xmlContent, 'utf-8');
        logger_1.logger.info(`XML report saved to: ${outputPath}`);
        return outputPath;
    }
    /**
     * 导出为YAML
     */
    async exportToYAML(enhancedIssues, outputPath) {
        // 简单的YAML生成（在实际项目中应该使用YAML库）
        const yamlLines = ['issues:'];
        enhancedIssues.forEach((issue, index) => {
            yamlLines.push(`  - id: ${index + 1}`);
            yamlLines.push(`    file: "${issue.filePath}"`);
            yamlLines.push(`    line: ${issue.line}`);
            yamlLines.push(`    type: ${issue.type}`);
            yamlLines.push(`    severity: ${issue.severity}`);
            yamlLines.push(`    message: "${issue.message}"`);
            yamlLines.push(`    priority: ${issue.priority}`);
            if (issue.blame) {
                yamlLines.push(`    author: "${issue.blame.author}"`);
                yamlLines.push(`    commitTime: "${issue.blame.commitTime}"`);
            }
        });
        const yamlContent = yamlLines.join('\n');
        await fs.writeFile(outputPath, yamlContent, 'utf-8');
        logger_1.logger.info(`YAML report saved to: ${outputPath}`);
        return outputPath;
    }
    /**
     * XML转义
     */
    escapeXml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }
}
exports.JSONFormatter = JSONFormatter;
//# sourceMappingURL=json.js.map