import { EnhancedIssue, IssueGroup } from '../../core/suggestions';
import { AnalysisResult } from '../../types';
export interface HTMLReportOptions {
    title: string;
    includeCharts: boolean;
    includeCodeContext: boolean;
    includeFixSuggestions: boolean;
    theme: 'light' | 'dark' | 'auto';
    outputPath: string;
    embedAssets: boolean;
}
export interface ChartData {
    severityChart: {
        labels: string[];
        data: number[];
        colors: string[];
    };
    typeChart: {
        labels: string[];
        data: number[];
        colors: string[];
    };
    fileChart: {
        labels: string[];
        data: number[];
    };
    timelineData?: {
        dates: string[];
        issues: number[];
    };
}
export declare class HTMLFormatter {
    private options;
    constructor(options?: Partial<HTMLReportOptions>);
    /**
     * 生成HTML报告
     */
    generateReport(enhancedIssues: EnhancedIssue[], issueGroups: IssueGroup[], analysisResults: AnalysisResult[]): Promise<string>;
    /**
     * 构建完整的HTML文档
     */
    private buildHTML;
    /**
     * 构建HTML头部
     */
    private buildHTMLHead;
    /**
     * 构建HTML主体
     */
    private buildHTMLBody;
    /**
     * 构建报告头部
     */
    private buildHeader;
    /**
     * 构建摘要部分
     */
    private buildSummarySection;
    /**
     * 构建图表部分
     */
    private buildChartsSection;
    /**
     * 构建问题部分
     */
    private buildIssuesSection;
    /**
     * 构建问题组
     */
    private buildIssueGroup;
    /**
     * 构建问题卡片
     */
    private buildIssueCard;
    /**
     * 构建代码上下文
     */
    private buildCodeContext;
    /**
     * 构建修复建议卡片
     */
    private buildFixSuggestionsCard;
    /**
     * 构建参考资料卡片
     */
    private buildReferencesCard;
    /**
     * 构建修复建议部分
     */
    private buildFixSuggestionsSection;
    /**
     * 构建页脚
     */
    private buildFooter;
    /**
     * 生成图表数据
     */
    private generateChartData;
    /**
     * 获取CSS样式
     */
    private getCSS;
    /**
     * 获取Chart.js库
     */
    private getChartJS;
    /**
     * 获取JavaScript代码
     */
    private getJavaScript;
    /**
     * 工具函数
     */
    private escapeHtml;
    private getTypeIcon;
    private getSeverityIcon;
    private getSuggestionIcon;
    private getPriorityIcon;
}
//# sourceMappingURL=html.d.ts.map