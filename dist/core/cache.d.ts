import { Issue } from '../types';
export interface CacheStats {
    totalEntries: number;
    totalSize: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
    hitRate: number;
}
export declare class CacheManager {
    private cacheDir;
    private maxCacheSize;
    private defaultTTL;
    private stats;
    constructor(projectRoot?: string, maxCacheSize?: number, defaultTTL?: number);
    /**
     * 获取文件的缓存键
     */
    getCacheKey(filePath: string): Promise<string>;
    /**
     * 获取缓存的分析结果
     */
    getCachedResults(filePath: string): Promise<Issue[] | null>;
    /**
     * 缓存分析结果
     */
    cacheResults(filePath: string, results: Issue[]): Promise<void>;
    /**
     * 删除特定文件的缓存
     */
    deleteCachedResults(filePath: string): Promise<void>;
    /**
     * 清空所有缓存
     */
    clearAllCache(): Promise<void>;
    /**
     * 清理过期缓存
     */
    cleanupExpiredCache(): Promise<number>;
    /**
     * 获取缓存统计信息
     */
    getCacheStats(): Promise<CacheStats>;
    /**
     * 优化缓存存储
     */
    optimizeCache(): Promise<void>;
    /**
     * 预热缓存（为常见文件生成缓存）
     */
    warmupCache(filePaths: string[]): Promise<void>;
    /**
     * 获取缓存命中率
     */
    getCacheHitRate(): number;
    /**
     * 重置统计信息
     */
    resetStats(): void;
    private getCacheFilePath;
    private ensureCacheDirectory;
    private deleteCacheEntry;
    private isCacheExpired;
    private cleanupIfNeeded;
    private cleanupBySize;
}
//# sourceMappingURL=cache.d.ts.map