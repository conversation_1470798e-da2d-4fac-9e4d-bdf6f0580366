import { GitFileChange } from '../types';
import { CacheManager } from './cache';
export interface AnalysisTarget {
    file: string;
    changedLines: number[];
    contextLines: number[];
    analysisType: 'full' | 'incremental' | 'cached';
    priority: 'high' | 'normal' | 'low';
}
export interface IncrementalAnalysisResult {
    targets: AnalysisTarget[];
    cacheHits: number;
    cacheMisses: number;
    totalLines: number;
    linesToAnalyze: number;
}
export declare class IncrementalAnalyzer {
    private cacheManager;
    private contextSize;
    constructor(cacheManager: CacheManager, contextSize?: number);
    /**
     * 准备增量分析目标
     */
    prepareIncrementalAnalysis(fileChanges: GitFileChange[]): Promise<IncrementalAnalysisResult>;
    /**
     * 准备增量分析目标（有缓存的情况）
     */
    private prepareIncrementalTarget;
    /**
     * 准备完整分析目标（无缓存的情况）
     */
    private prepareFullTarget;
    /**
     * 提取变更的行号
     */
    private extractChangedLines;
    /**
     * 获取上下文行（变更行周围的行）
     */
    private getContextLines;
    /**
     * 判断是否应该使用完整分析
     */
    private shouldUseFullAnalysis;
    /**
     * 计算文件分析优先级
     */
    private calculateFilePriority;
    /**
     * 获取优先级权重
     */
    private getPriorityWeight;
    /**
     * 计算文件行数
     */
    private countFileLines;
    /**
     * 优化分析批次
     */
    optimizeAnalysisBatches(targets: AnalysisTarget[], maxBatchSize?: number): AnalysisTarget[][];
    /**
     * 计算分析目标的复杂度
     */
    private calculateTargetComplexity;
    /**
     * 生成增量分析报告
     */
    generateIncrementalReport(result: IncrementalAnalysisResult): string;
    /**
     * 更新分析进度
     */
    updateAnalysisProgress(completedTargets: number, totalTargets: number, currentTarget?: AnalysisTarget): void;
}
//# sourceMappingURL=incremental.d.ts.map