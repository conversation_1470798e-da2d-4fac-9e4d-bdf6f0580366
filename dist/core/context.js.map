{"version": 3, "file": "context.js", "sourceRoot": "", "sources": ["../../src/core/context.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA6B;AAC7B,6CAA+B;AAC/B,qCAAkD;AAElD,4CAAyC;AAiCzC,MAAa,eAAe;IAK1B,YAAY,WAAoB;QAFxB,eAAU,GAA4B,IAAI,GAAG,EAAE,CAAC;QAGtD,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAU,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CAAC,UAAyB;QACnD,IAAI,CAAC;YACH,eAAM,CAAC,KAAK,CAAC,0BAA0B,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;YAE1D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;YAEzD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;YAED,SAAS;YACT,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YAEjD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAChE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;YACpE,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEtD,OAAO;gBACL,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,UAAU;gBACV,YAAY;gBACZ,YAAY;gBACZ,SAAS;gBACT,UAAU;aACX,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,UAAU,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAsB;QACtD,MAAM,YAAY,GAAqB,EAAE,CAAC;QAE1C,KAAK,MAAM,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC1C,MAAM,OAAO,GAAmB;gBAC9B,MAAM,EAAE,GAAG;gBACX,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,UAAU,CAAC,OAAO,CAAC;gBACrD,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,UAAU,CAAC,OAAO,CAAC;gBACtD,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBACpC,YAAY,EAAE,SAAS;aACxB,CAAC;YAEF,gBAAgB;YAChB,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO,CAAC,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;YACpF,CAAC;YAED,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,UAAsB;QACnD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAElD,YAAY;QACZ,KAAK,MAAM,GAAG,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC1C,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAChF,IAAI,YAAY,EAAE,CAAC;oBACjB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;QACH,CAAC;QAED,cAAc;QACd,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEvF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBAE/D,eAAe;gBACf,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7E,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAClC,CAAC;gBAED,SAAS;gBACT,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvD,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC;oBAC5D,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;wBAC9B,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,OAAO,KAAK,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK;IAC1C,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,UAAsB,EAAE,OAAc;QACzD,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QACxC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,GAAG,EAAU,CAAC;QAEtC,YAAY;QACZ,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC;aACzD,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEhD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,SAAS;YACT,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;oBACnC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,SAAS;YACT,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC;YACpC,UAAU,EAAE,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;SACzD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,UAAsB;QAC9C,MAAM,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACtC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAC7C,CAAC,MAAM,CAAC;QAET,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC,CAAC;QAC5E,MAAM,mBAAmB,GAAG,IAAI,CAAC,4BAA4B,CAAC,UAAU,CAAC,CAAC;QAC1E,MAAM,oBAAoB,GAAG,IAAI,CAAC,6BAA6B,CAC7D,WAAW,EACX,oBAAoB,EACpB,UAAU,CAAC,SAAS,CAAC,MAAM,CAC5B,CAAC;QAEF,OAAO;YACL,oBAAoB;YACpB,mBAAmB;YACnB,WAAW;YACX,oBAAoB;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,UAAsB;QAC1D,IAAI,CAAC,UAAU,CAAC,GAAG;YAAE,OAAO,CAAC,CAAC;QAE9B,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,QAAQ;QAC5B,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QAEnC,aAAa;QACb,MAAM,QAAQ,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QAE5F,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,OAAO,KAAK,EAAE,GAAG,CAAC,CAAC;YAClD,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,OAAO,EAAE,CAAC;gBACZ,UAAU,IAAI,OAAO,CAAC,MAAM,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,UAAsB;QACzD,aAAa;QACb,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QACnC,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,YAAY;QACZ,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAChE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAE5B,OAAO;YACP,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;gBACtC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE,CAAC;oBACxE,OAAO,EAAE,CAAC;oBACV,UAAU,IAAI,OAAO,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,OAAO;YACP,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;gBACpB,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;YACrC,CAAC;YAED,QAAQ;YACR,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrD,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,6BAA6B,CAAC,GAAW,EAAE,UAAkB,EAAE,SAAiB;QACtF,cAAc;QACd,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC;QAC7D,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;QACtH,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,YAAsB;QACtD,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAE5B,OAAO;YACP,IAAI,iCAAiC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpD,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;YAED,OAAO;YACP,IAAI,8BAA8B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACjD,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;YAED,OAAO;YACP,IAAI,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9B,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,GAAW,EAAE,OAAe;QACpD,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACxD,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC;gBACjF,OAAO,SAAS,CAAC;YACnB,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,GAAW,EAAE,OAAe;QACrD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,CAAC;YACf,CAAC;QACH,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,GAAW;QACnC,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IAC9E,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,GAAW,EAAE,QAAgB;QAC/D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAE5C,WAAW;YACX,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;YAE1D,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC7B,MAAM,OAAO,GAAG,QAAQ,GAAG,GAAG,CAAC;gBAC/B,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;oBACjC,OAAO,OAAO,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,YAAY;YACZ,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;gBAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC;gBACrD,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBACnC,OAAO,SAAS,CAAC;gBACnB,CAAC;YACH,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,IAAY;QACrC,OAAO;YACL,IAAI;YACJ,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,EAAE;YAChB,SAAS,EAAE;gBACT,aAAa,EAAE,EAAE;gBACjB,aAAa,EAAE,EAAE;gBACjB,WAAW,EAAE,EAAE;gBACf,UAAU,EAAE,CAAC;aACd;YACD,UAAU,EAAE;gBACV,oBAAoB,EAAE,CAAC;gBACvB,mBAAmB,EAAE,CAAC;gBACtB,WAAW,EAAE,CAAC;gBACd,oBAAoB,EAAE,GAAG;aAC1B;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,QAAgB;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;CACF;AApYD,0CAoYC"}