export interface ParsedFile {
    filePath: string;
    ast: any;
    content: string;
    language: 'javascript' | 'typescript' | 'vue' | 'css';
    dependencies: string[];
    exports: string[];
    functions: FunctionInfo[];
    variables: VariableInfo[];
}
export interface FunctionInfo {
    name: string;
    line: number;
    column: number;
    params: string[];
    isAsync: boolean;
    isArrow: boolean;
}
export interface VariableInfo {
    name: string;
    line: number;
    column: number;
    type: 'const' | 'let' | 'var';
    isExported: boolean;
}
export declare class CodeParser {
    private supportedExtensions;
    /**
     * 解析文件内容
     */
    parseFile(filePath: string): Promise<ParsedFile | null>;
    /**
     * 解析JavaScript/TypeScript文件
     */
    private parseJavaScriptTypeScript;
    /**
     * 解析Vue单文件组件
     */
    private parseVue;
    /**
     * 解析CSS文件
     */
    private parseCSS;
    /**
     * 提取依赖关系
     */
    private extractDependencies;
    /**
     * 提取导出信息
     */
    private extractExports;
    /**
     * 提取函数信息
     */
    private extractFunctions;
    /**
     * 提取变量信息
     */
    private extractVariables;
    /**
     * 检查文件是否支持解析
     */
    isSupportedFile(filePath: string): boolean;
    /**
     * 获取文件的语言类型
     */
    getFileLanguage(filePath: string): string | undefined;
}
//# sourceMappingURL=parser.d.ts.map