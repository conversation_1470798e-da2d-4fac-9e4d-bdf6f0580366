"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeParser = void 0;
const parser_1 = require("@babel/parser");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const logger_1 = require("../utils/logger");
class CodeParser {
    constructor() {
        this.supportedExtensions = new Map([
            ['.js', 'javascript'],
            ['.jsx', 'javascript'],
            ['.ts', 'typescript'],
            ['.tsx', 'typescript'],
            ['.vue', 'vue'],
            ['.css', 'css'],
            ['.scss', 'css'],
            ['.sass', 'css'],
            ['.less', 'css']
        ]);
    }
    /**
     * 解析文件内容
     */
    async parseFile(filePath) {
        try {
            const ext = path.extname(filePath).toLowerCase();
            const language = this.supportedExtensions.get(ext);
            if (!language) {
                logger_1.logger.debug(`Unsupported file type: ${filePath}`);
                return null;
            }
            const content = await fs.readFile(filePath, 'utf-8');
            logger_1.logger.debug(`Parsing file: ${filePath} (${language})`);
            switch (language) {
                case 'javascript':
                case 'typescript':
                    return await this.parseJavaScriptTypeScript(filePath, content, language);
                case 'vue':
                    return await this.parseVue(filePath, content);
                case 'css':
                    return await this.parseCSS(filePath, content);
                default:
                    return null;
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to parse file ${filePath}: ${error}`);
            return null;
        }
    }
    /**
     * 解析JavaScript/TypeScript文件
     */
    async parseJavaScriptTypeScript(filePath, content, language) {
        let ast;
        try {
            // 使用 Babel 解析器支持 TypeScript
            const parserOptions = {
                sourceType: 'module',
                allowImportExportEverywhere: true,
                allowReturnOutsideFunction: true,
                plugins: [
                    'jsx',
                    'typescript', // 支持 TypeScript
                    'asyncGenerators',
                    'bigInt',
                    'classProperties',
                    'decorators-legacy',
                    'doExpressions',
                    'dynamicImport',
                    'exportDefaultFrom',
                    'exportNamespaceFrom',
                    'functionBind',
                    'functionSent',
                    'importMeta',
                    'nullishCoalescingOperator',
                    'numericSeparator',
                    'objectRestSpread',
                    'optionalCatchBinding',
                    'optionalChaining',
                    'throwExpressions',
                    'topLevelAwait'
                ]
            };
            ast = (0, parser_1.parse)(content, parserOptions);
            const dependencies = this.extractDependencies(ast);
            const exports = this.extractExports(ast);
            const functions = this.extractFunctions(ast);
            const variables = this.extractVariables(ast);
            return {
                filePath,
                ast,
                content,
                language,
                dependencies,
                exports,
                functions,
                variables
            };
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse ${language} file ${filePath}: ${error}`);
            // 返回基本信息，即使解析失败
            return {
                filePath,
                ast: null,
                content,
                language,
                dependencies: [],
                exports: [],
                functions: [],
                variables: []
            };
        }
    }
    /**
     * 解析Vue单文件组件
     */
    async parseVue(filePath, content) {
        // 简单的Vue SFC解析
        const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
        const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/);
        const styleMatch = content.match(/<style[^>]*>([\s\S]*?)<\/style>/);
        let dependencies = [];
        let exports = [];
        let functions = [];
        let variables = [];
        if (scriptMatch) {
            const scriptContent = scriptMatch[1];
            try {
                // 检查是否是TypeScript
                const isTS = content.includes('lang="ts"') || content.includes("lang='ts'");
                const scriptResult = await this.parseJavaScriptTypeScript(filePath, scriptContent, isTS ? 'typescript' : 'javascript');
                dependencies = scriptResult.dependencies;
                exports = scriptResult.exports;
                functions = scriptResult.functions;
                variables = scriptResult.variables;
            }
            catch (error) {
                logger_1.logger.debug(`Failed to parse Vue script section: ${error}`);
            }
        }
        return {
            filePath,
            ast: {
                script: scriptMatch ? scriptMatch[1] : null,
                template: templateMatch ? templateMatch[1] : null,
                style: styleMatch ? styleMatch[1] : null
            },
            content,
            language: 'vue',
            dependencies,
            exports,
            functions,
            variables
        };
    }
    /**
     * 解析CSS文件
     */
    async parseCSS(filePath, content) {
        // 简单的CSS导入提取
        const importRegex = /@import\s+['"`]([^'"`]+)['"`]/g;
        const dependencies = [];
        let match;
        while ((match = importRegex.exec(content)) !== null) {
            dependencies.push(match[1]);
        }
        return {
            filePath,
            ast: { type: 'css', content },
            content,
            language: 'css',
            dependencies,
            exports: [],
            functions: [],
            variables: []
        };
    }
    /**
     * 提取依赖关系
     */
    extractDependencies(ast) {
        const dependencies = [];
        if (!ast || !ast.body)
            return dependencies;
        const traverse = (node) => {
            if (!node || typeof node !== 'object')
                return;
            // Import声明
            if (node.type === 'ImportDeclaration' && node.source) {
                dependencies.push(node.source.value);
            }
            // Require调用
            if (node.type === 'CallExpression' &&
                node.callee &&
                node.callee.name === 'require' &&
                node.arguments.length > 0 &&
                node.arguments[0].type === 'Literal') {
                dependencies.push(node.arguments[0].value);
            }
            // 动态import
            if (node.type === 'Import' ||
                (node.type === 'CallExpression' && node.callee.type === 'Import')) {
                if (node.arguments && node.arguments[0] && node.arguments[0].type === 'Literal') {
                    dependencies.push(node.arguments[0].value);
                }
            }
            // 递归遍历所有属性
            for (const key in node) {
                if (key !== 'parent' && node[key] && typeof node[key] === 'object') {
                    if (Array.isArray(node[key])) {
                        node[key].forEach(traverse);
                    }
                    else {
                        traverse(node[key]);
                    }
                }
            }
        };
        ast.body.forEach(traverse);
        return [...new Set(dependencies)]; // 去重
    }
    /**
     * 提取导出信息
     */
    extractExports(ast) {
        const exports = [];
        if (!ast || !ast.body)
            return exports;
        for (const node of ast.body) {
            if (node.type === 'ExportNamedDeclaration') {
                if (node.declaration) {
                    if (node.declaration.type === 'VariableDeclaration') {
                        node.declaration.declarations.forEach((decl) => {
                            if (decl.id && decl.id.name) {
                                exports.push(decl.id.name);
                            }
                        });
                    }
                    else if (node.declaration.id && node.declaration.id.name) {
                        exports.push(node.declaration.id.name);
                    }
                }
                if (node.specifiers) {
                    node.specifiers.forEach((spec) => {
                        if (spec.exported && spec.exported.name) {
                            exports.push(spec.exported.name);
                        }
                    });
                }
            }
            else if (node.type === 'ExportDefaultDeclaration') {
                exports.push('default');
            }
        }
        return exports;
    }
    /**
     * 提取函数信息
     */
    extractFunctions(ast) {
        const functions = [];
        if (!ast || !ast.body)
            return functions;
        const traverse = (node) => {
            if (!node || typeof node !== 'object')
                return;
            if (node.type === 'FunctionDeclaration' ||
                node.type === 'FunctionExpression' ||
                node.type === 'ArrowFunctionExpression') {
                const funcInfo = {
                    name: node.id ? node.id.name : 'anonymous',
                    line: node.loc ? node.loc.start.line : 0,
                    column: node.loc ? node.loc.start.column : 0,
                    params: node.params ? node.params.map((p) => p.name || p.type) : [],
                    isAsync: node.async || false,
                    isArrow: node.type === 'ArrowFunctionExpression'
                };
                functions.push(funcInfo);
            }
            // 递归遍历
            for (const key in node) {
                if (key !== 'parent' && node[key] && typeof node[key] === 'object') {
                    if (Array.isArray(node[key])) {
                        node[key].forEach(traverse);
                    }
                    else {
                        traverse(node[key]);
                    }
                }
            }
        };
        ast.body.forEach(traverse);
        return functions;
    }
    /**
     * 提取变量信息
     */
    extractVariables(ast) {
        const variables = [];
        if (!ast || !ast.body)
            return variables;
        for (const node of ast.body) {
            if (node.type === 'VariableDeclaration') {
                const isExported = false; // 这里需要检查是否在export声明中
                node.declarations.forEach((decl) => {
                    if (decl.id && decl.id.name) {
                        variables.push({
                            name: decl.id.name,
                            line: decl.loc ? decl.loc.start.line : 0,
                            column: decl.loc ? decl.loc.start.column : 0,
                            type: node.kind,
                            isExported
                        });
                    }
                });
            }
        }
        return variables;
    }
    /**
     * 检查文件是否支持解析
     */
    isSupportedFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.supportedExtensions.has(ext);
    }
    /**
     * 获取文件的语言类型
     */
    getFileLanguage(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.supportedExtensions.get(ext);
    }
}
exports.CodeParser = CodeParser;
//# sourceMappingURL=parser.js.map