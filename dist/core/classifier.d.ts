import { Issue, AIReviewConfig } from '../types';
export interface ClassificationRule {
    type: Issue['type'];
    severityMapping: {
        keywords: string[];
        severity: Issue['severity'];
        priority: number;
    }[];
    strictnessLevels: {
        strict: string[];
        normal: string[];
        loose: string[];
    };
}
export interface ClassificationResult {
    originalIssue: Issue;
    adjustedIssue: Issue;
    confidence: number;
    reasoning: string[];
    suggestions: string[];
}
export declare class IssueClassifier {
    private rules;
    private config;
    constructor(config: AIReviewConfig);
    /**
     * 分类和调整AI返回的问题
     */
    classifyIssues(issues: Issue[]): ClassificationResult[];
    /**
     * 分类单个问题
     */
    private classifySingleIssue;
    /**
     * 验证并调整问题类型
     */
    private validateAndAdjustType;
    /**
     * 重新评估严重程度
     */
    private assessSeverity;
    /**
     * 增强问题描述
     */
    private enhanceDescription;
    /**
     * 增强修复建议
     */
    private enhanceSuggestion;
    /**
     * 添加最佳实践参考
     */
    private addBestPracticeReferences;
    /**
     * 生成改进建议
     */
    private generateImprovementSuggestions;
    /**
     * 根据严格程度过滤问题
     */
    private applyStrictnessFilter;
    /**
     * 生成影响分析
     */
    private generateImpactAnalysis;
    /**
     * 生成根因分析
     */
    private generateRootCauseAnalysis;
    /**
     * 生成修复步骤
     */
    private generateFixingSteps;
    /**
     * 生成验证步骤
     */
    private generateVerificationSteps;
    /**
     * 初始化分类规则
     */
    private initializeClassificationRules;
    /**
     * 获取分类统计
     */
    getClassificationStats(results: ClassificationResult[]): {
        totalIssues: number;
        adjustedIssues: number;
        averageConfidence: number;
        severityDistribution: Record<Issue['severity'], number>;
        typeDistribution: Record<Issue['type'], number>;
    };
}
//# sourceMappingURL=classifier.d.ts.map