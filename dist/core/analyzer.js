"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalysisEngine = void 0;
const manager_1 = require("../ai/manager");
const logger_1 = require("../utils/logger");
const performance_1 = require("../utils/performance");
const errorHandler_1 = require("../utils/errorHandler");
class AnalysisEngine {
    constructor(config) {
        this.config = config;
        this.aiManager = new manager_1.AIManager(config);
    }
    /**
     * 分析文件变更并返回发现的问题
     */
    async analyzeChanges(changes, options = {}) {
        const finalOptions = {
            includeContext: true,
            maxContextLines: 20,
            analysisDepth: 'detailed',
            ignorePatterns: [],
            ...options
        };
        logger_1.logger.info(`Starting analysis of ${changes.length} changed files`);
        return performance_1.performanceMonitor.timeOperation('analyze_changes', async () => {
            // 确定并发数量（根据CPU核心数和文件数量）
            const os = require('os');
            const cpuCount = os.cpus().length;
            const batchSize = Math.min(cpuCount, Math.max(2, Math.floor(cpuCount / 2)));
            logger_1.logger.debug(`Using parallel processing with batch size: ${batchSize}`);
            // 批处理文件以实现并行分析
            const results = [];
            for (let i = 0; i < changes.length; i += batchSize) {
                const batch = changes.slice(i, i + batchSize);
                // 并行分析批次中的文件
                const batchPromises = batch.map(async (change) => {
                    try {
                        return await this.analyzeSingleFile(change, finalOptions);
                    }
                    catch (error) {
                        logger_1.logger.error(`Failed to analyze file ${change.file}: ${error}`);
                        return this.createEmptyAnalysisResult(change.file, error);
                    }
                });
                // 等待批次完成
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
                // 记录进度
                const progress = Math.min(i + batchSize, changes.length);
                logger_1.logger.debug(`Progress: ${progress}/${changes.length} files analyzed`);
            }
            logger_1.logger.info(`Analysis completed: ${results.length} files processed`);
            return results;
        }, { fileCount: changes.length, depth: finalOptions.analysisDepth });
    }
    /**
     * 分析单个文件
     */
    async analyzeSingleFile(change, options) {
        return performance_1.performanceMonitor.timeOperation('analyze_single_file', async () => {
            // 检查文件是否存在
            const fs = require('fs-extra');
            if (!await fs.pathExists(change.file)) {
                logger_1.logger.debug(`File ${change.file} does not exist, returning empty result`);
                return this.createEmptyAnalysisResult(change.file, new Error('File not found'));
            }
            // 简化版本 - 直接分析文件变更
            const issues = await this.performAIAnalysis(change, options);
            const result = {
                file: change.file,
                issues,
                statistics: this.calculateStatistics(issues),
                metadata: {
                    analyzedAt: new Date().toISOString(),
                    aiModel: 'ai-analysis',
                    fromCache: false
                }
            };
            return result;
        }, { file: change.file });
    }
    /**
     * 执行AI分析
     */
    async performAIAnalysis(change, options) {
        const prompt = this.buildAnalysisPrompt(change, options);
        return errorHandler_1.errorHandler.withRetry(async () => {
            // 检查文件是否存在
            const fs = require('fs-extra');
            if (!await fs.pathExists(change.file)) {
                logger_1.logger.debug(`File ${change.file} does not exist, skipping analysis`);
                return [];
            }
            // TODO: 实际的AI调用将在后续版本实现
            // 现在返回模拟结果用于演示
            logger_1.logger.debug(`Analyzing ${change.file} with AI (simulated)`);
            return this.createMockIssues(change);
        }, `AI analysis for ${change.file}`, { maxRetries: 1, baseDelay: 1000 });
    }
    /**
     * 构建分析提示词
     */
    buildAnalysisPrompt(change, options) {
        const fileContent = change.changes.map(c => c.content).join('\n');
        return `Analyze this ${this.getFileType(change.file)} code for issues:

File: ${change.file}
Status: ${change.status}

Code:
\`\`\`${this.getFileType(change.file)}
${fileContent}
\`\`\`

Focus on:
1. Logic errors and bugs
2. Performance issues
3. Security vulnerabilities
4. Code quality issues

Return JSON array of issues with: type, severity, line, message, description, suggestion.`;
    }
    /**
     * 创建模拟问题（用于演示和测试）
     */
    createMockIssues(change) {
        const issues = [];
        // 为每个变更创建一个示例问题
        change.changes.forEach((gitChange, index) => {
            if (gitChange.type === 'added' && gitChange.content.trim()) {
                issues.push({
                    type: 'Logic',
                    severity: 'Medium',
                    line: gitChange.line,
                    message: 'Code review suggestion',
                    description: `New code added at line ${gitChange.line} should be reviewed for potential improvements`,
                    suggestion: 'Consider adding error handling and validation',
                    author: 'AI Analysis',
                    commitTime: new Date().toISOString()
                });
            }
        });
        return issues;
    }
    /**
     * 计算统计信息
     */
    calculateStatistics(issues) {
        return {
            critical: issues.filter(i => i.severity === 'Critical').length,
            high: issues.filter(i => i.severity === 'High').length,
            medium: issues.filter(i => i.severity === 'Medium').length,
            low: issues.filter(i => i.severity === 'Low').length
        };
    }
    /**
     * 创建空的分析结果（用于失败情况）
     */
    createEmptyAnalysisResult(fileName, error) {
        return {
            file: fileName,
            issues: [],
            statistics: {
                critical: 0,
                high: 0,
                medium: 0,
                low: 0
            },
            metadata: {
                analyzedAt: new Date().toISOString(),
                aiModel: 'failed',
                fromCache: false
            }
        };
    }
    /**
     * 获取文件类型
     */
    getFileType(fileName) {
        const ext = fileName.split('.').pop()?.toLowerCase();
        switch (ext) {
            case 'js':
            case 'jsx':
                return 'javascript';
            case 'ts':
            case 'tsx':
                return 'typescript';
            case 'vue':
                return 'vue';
            case 'css':
            case 'scss':
            case 'sass':
                return 'css';
            case 'py':
                return 'python';
            case 'java':
                return 'java';
            case 'go':
                return 'go';
            default:
                return 'text';
        }
    }
    /**
     * 获取支持的文件类型
     */
    getSupportedFileTypes() {
        return [
            'javascript', 'typescript', 'vue', 'css', 'python', 'java', 'go'
        ];
    }
    /**
     * 更新配置
     */
    updateConfig(config) {
        this.config = { ...this.config, ...config };
    }
    /**
     * 获取分析统计信息
     */
    getAnalysisStats() {
        const perfStats = performance_1.performanceMonitor.getAggregatedMetrics('analyze_single_file');
        return {
            totalAnalyzed: perfStats?.count || 0,
            cacheHitRate: 0, // Simplified - no cache
            averageAnalysisTime: perfStats?.averageDuration || 0
        };
    }
    /**
     * 清理资源
     */
    async cleanup() {
        logger_1.logger.debug('Analysis engine cleanup completed');
    }
}
exports.AnalysisEngine = AnalysisEngine;
//# sourceMappingURL=analyzer.js.map