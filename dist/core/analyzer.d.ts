import { AnalysisResult, GitFileChange, AIReviewConfig } from '../types';
export interface AnalysisOptions {
    includeContext: boolean;
    maxContextLines: number;
    analysisDepth: 'quick' | 'detailed';
    ignorePatterns: string[];
}
export declare class AnalysisEngine {
    private aiManager;
    private config;
    constructor(config: AIReviewConfig);
    /**
     * 分析文件变更并返回发现的问题
     */
    analyzeChanges(changes: GitFileChange[], options?: Partial<AnalysisOptions>): Promise<AnalysisResult[]>;
    /**
     * 分析单个文件
     */
    private analyzeSingleFile;
    /**
     * 执行AI分析
     */
    private performAIAnalysis;
    /**
     * 构建分析提示词
     */
    private buildAnalysisPrompt;
    /**
     * 创建模拟问题（用于演示和测试）
     */
    private createMockIssues;
    /**
     * 计算统计信息
     */
    private calculateStatistics;
    /**
     * 创建空的分析结果（用于失败情况）
     */
    private createEmptyAnalysisResult;
    /**
     * 获取文件类型
     */
    private getFileType;
    /**
     * 获取支持的文件类型
     */
    getSupportedFileTypes(): string[];
    /**
     * 更新配置
     */
    updateConfig(config: Partial<AIReviewConfig>): void;
    /**
     * 获取分析统计信息
     */
    getAnalysisStats(): {
        totalAnalyzed: number;
        cacheHitRate: number;
        averageAnalysisTime: number;
    };
    /**
     * 清理资源
     */
    cleanup(): Promise<void>;
}
//# sourceMappingURL=analyzer.d.ts.map