{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../../src/core/analyzer.ts"], "names": [], "mappings": ";;;AAAA,2CAA0C;AAE1C,4CAAyC;AACzC,sDAA0D;AAC1D,wDAAqD;AASrD,MAAa,cAAc;IAIzB,YAAY,MAAsB;QAChC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,mBAAS,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACzB,OAAwB,EACxB,UAAoC,EAAE;QAEtC,MAAM,YAAY,GAAoB;YACpC,cAAc,EAAE,IAAI;YACpB,eAAe,EAAE,EAAE;YACnB,aAAa,EAAE,UAAU;YACzB,cAAc,EAAE,EAAE;YAClB,GAAG,OAAO;SACX,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,MAAM,gBAAgB,CAAC,CAAC;QAEpE,OAAO,gCAAkB,CAAC,aAAa,CACrC,iBAAiB,EACjB,KAAK,IAAI,EAAE;YACT,MAAM,OAAO,GAAqB,EAAE,CAAC;YAErC,kBAAkB;YAClB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;oBAClE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;oBAChE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC,MAAM,kBAAkB,CAAC,CAAC;YACrE,OAAO,OAAO,CAAC;QACjB,CAAC,EACD,EAAE,SAAS,EAAE,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,aAAa,EAAE,CACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAqB,EACrB,OAAwB;QAExB,OAAO,gCAAkB,CAAC,aAAa,CACrC,qBAAqB,EACrB,KAAK,IAAI,EAAE;YACT,WAAW;YACX,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,IAAI,yCAAyC,CAAC,CAAC;gBAC3E,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAClF,CAAC;YAED,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAE7D,MAAM,MAAM,GAAmB;gBAC7B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM;gBACN,UAAU,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;gBAC5C,QAAQ,EAAE;oBACR,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACpC,OAAO,EAAE,aAAa;oBACtB,SAAS,EAAE,KAAK;iBACjB;aACF,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,CACtB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAqB,EACrB,OAAwB;QAExB,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEzD,OAAO,2BAAY,CAAC,SAAS,CAC3B,KAAK,IAAI,EAAE;YACT,WAAW;YACX,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAI,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtC,eAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,IAAI,oCAAoC,CAAC,CAAC;gBACtE,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,wBAAwB;YACxB,eAAe;YACf,eAAM,CAAC,KAAK,CAAC,aAAa,MAAM,CAAC,IAAI,sBAAsB,CAAC,CAAC;YAE7D,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC,EACD,mBAAmB,MAAM,CAAC,IAAI,EAAE,EAChC,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CACnC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAqB,EAAE,OAAwB;QACzE,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElE,OAAO,gBAAgB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;;QAEhD,MAAM,CAAC,IAAI;UACT,MAAM,CAAC,MAAM;;;QAGf,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;EACnC,WAAW;;;;;;;;;0FAS6E,CAAC;IACzF,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAqB;QAC5C,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,gBAAgB;QAChB,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;YAC1C,IAAI,SAAS,CAAC,IAAI,KAAK,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC3D,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,SAAS,CAAC,IAAI;oBACpB,OAAO,EAAE,wBAAwB;oBACjC,WAAW,EAAE,0BAA0B,SAAS,CAAC,IAAI,gDAAgD;oBACrG,UAAU,EAAE,+CAA+C;oBAC3D,MAAM,EAAE,aAAa;oBACrB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACrC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAe;QACzC,OAAO;YACL,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM;YAC9D,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;YACtD,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;YAC1D,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,QAAgB,EAAE,KAAU;QAC5D,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,EAAE;YACV,UAAU,EAAE;gBACV,QAAQ,EAAE,CAAC;gBACX,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,GAAG,EAAE,CAAC;aACP;YACD,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,OAAO,EAAE,QAAQ;gBACjB,SAAS,EAAE,KAAK;aACjB;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB;QAClC,MAAM,GAAG,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;QAErD,QAAQ,GAAG,EAAE,CAAC;YACZ,KAAK,IAAI,CAAC;YACV,KAAK,KAAK;gBACR,OAAO,YAAY,CAAC;YACtB,KAAK,IAAI,CAAC;YACV,KAAK,KAAK;gBACR,OAAO,YAAY,CAAC;YACtB,KAAK,KAAK;gBACR,OAAO,KAAK,CAAC;YACf,KAAK,KAAK,CAAC;YACX,KAAK,MAAM,CAAC;YACZ,KAAK,MAAM;gBACT,OAAO,KAAK,CAAC;YACf,KAAK,IAAI;gBACP,OAAO,QAAQ,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,qBAAqB;QAC1B,OAAO;YACL,YAAY,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI;SACjE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,MAA+B;QACjD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;IAC9C,CAAC;IAED;;OAEG;IACI,gBAAgB;QAKrB,MAAM,SAAS,GAAG,gCAAkB,CAAC,oBAAoB,CAAC,qBAAqB,CAAC,CAAC;QAEjF,OAAO;YACL,aAAa,EAAE,SAAS,EAAE,KAAK,IAAI,CAAC;YACpC,YAAY,EAAE,CAAC,EAAE,wBAAwB;YACzC,mBAAmB,EAAE,SAAS,EAAE,eAAe,IAAI,CAAC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,OAAO;QAClB,eAAM,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;IACpD,CAAC;CACF;AA3QD,wCA2QC"}