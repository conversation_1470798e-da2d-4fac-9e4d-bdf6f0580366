"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const crypto = __importStar(require("crypto"));
const logger_1 = require("../utils/logger");
class CacheManager {
    constructor(projectRoot, maxCacheSize = 100, defaultTTL = 24) {
        this.stats = { hits: 0, misses: 0 };
        this.cacheDir = path.join(projectRoot || process.cwd(), '.ai-review-cache');
        this.maxCacheSize = maxCacheSize;
        this.defaultTTL = defaultTTL;
    }
    /**
     * 获取文件的缓存键
     */
    async getCacheKey(filePath) {
        try {
            const stat = await fs.stat(filePath);
            const content = await fs.readFile(filePath, 'utf-8');
            // 基于文件内容和修改时间生成hash
            const hash = crypto.createHash('sha256');
            hash.update(content);
            hash.update(stat.mtime.toISOString());
            return hash.digest('hex');
        }
        catch (error) {
            logger_1.logger.debug(`Failed to generate cache key for ${filePath}: ${error}`);
            return '';
        }
    }
    /**
     * 获取缓存的分析结果
     */
    async getCachedResults(filePath) {
        try {
            const cacheKey = await this.getCacheKey(filePath);
            if (!cacheKey)
                return null;
            const cacheFile = this.getCacheFilePath(cacheKey);
            if (!await fs.pathExists(cacheFile)) {
                this.stats.misses++;
                return null;
            }
            const cacheEntry = await fs.readJSON(cacheFile);
            // 检查缓存是否过期
            if (this.isCacheExpired(cacheEntry)) {
                await this.deleteCacheEntry(cacheKey);
                this.stats.misses++;
                return null;
            }
            // 验证文件hash
            if (cacheEntry.fileHash !== cacheKey) {
                await this.deleteCacheEntry(cacheKey);
                this.stats.misses++;
                return null;
            }
            this.stats.hits++;
            logger_1.logger.debug(`Cache hit for ${filePath}`);
            return cacheEntry.results;
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get cached results for ${filePath}: ${error}`);
            this.stats.misses++;
            return null;
        }
    }
    /**
     * 缓存分析结果
     */
    async cacheResults(filePath, results) {
        try {
            const cacheKey = await this.getCacheKey(filePath);
            if (!cacheKey)
                return;
            await this.ensureCacheDirectory();
            const cacheEntry = {
                fileHash: cacheKey,
                lastModified: new Date().toISOString(),
                results
            };
            const cacheFile = this.getCacheFilePath(cacheKey);
            await fs.writeJSON(cacheFile, cacheEntry, { spaces: 2 });
            logger_1.logger.debug(`Cached results for ${filePath}`);
            // 检查缓存大小并清理
            await this.cleanupIfNeeded();
        }
        catch (error) {
            logger_1.logger.error(`Failed to cache results for ${filePath}: ${error}`);
        }
    }
    /**
     * 删除特定文件的缓存
     */
    async deleteCachedResults(filePath) {
        try {
            const cacheKey = await this.getCacheKey(filePath);
            if (!cacheKey)
                return;
            await this.deleteCacheEntry(cacheKey);
            logger_1.logger.debug(`Deleted cache for ${filePath}`);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to delete cache for ${filePath}: ${error}`);
        }
    }
    /**
     * 清空所有缓存
     */
    async clearAllCache() {
        try {
            if (await fs.pathExists(this.cacheDir)) {
                await fs.remove(this.cacheDir);
                logger_1.logger.info('All cache cleared');
            }
            // 重置统计
            this.stats = { hits: 0, misses: 0 };
        }
        catch (error) {
            logger_1.logger.error(`Failed to clear cache: ${error}`);
            throw error;
        }
    }
    /**
     * 清理过期缓存
     */
    async cleanupExpiredCache() {
        try {
            if (!await fs.pathExists(this.cacheDir)) {
                return 0;
            }
            const files = await fs.readdir(this.cacheDir);
            let cleanedCount = 0;
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.cacheDir, file);
                try {
                    const cacheEntry = await fs.readJSON(filePath);
                    if (this.isCacheExpired(cacheEntry)) {
                        await fs.remove(filePath);
                        cleanedCount++;
                        logger_1.logger.debug(`Removed expired cache: ${file}`);
                    }
                }
                catch (error) {
                    // 删除无效的缓存文件
                    await fs.remove(filePath);
                    cleanedCount++;
                    logger_1.logger.debug(`Removed invalid cache: ${file}`);
                }
            }
            if (cleanedCount > 0) {
                logger_1.logger.info(`Cleaned up ${cleanedCount} expired cache entries`);
            }
            return cleanedCount;
        }
        catch (error) {
            logger_1.logger.error(`Failed to cleanup expired cache: ${error}`);
            return 0;
        }
    }
    /**
     * 获取缓存统计信息
     */
    async getCacheStats() {
        try {
            if (!await fs.pathExists(this.cacheDir)) {
                return {
                    totalEntries: 0,
                    totalSize: 0,
                    oldestEntry: null,
                    newestEntry: null,
                    hitRate: 0
                };
            }
            const files = await fs.readdir(this.cacheDir);
            const jsonFiles = files.filter(f => f.endsWith('.json'));
            let totalSize = 0;
            let oldestEntry = null;
            let newestEntry = null;
            for (const file of jsonFiles) {
                const filePath = path.join(this.cacheDir, file);
                const stat = await fs.stat(filePath);
                totalSize += stat.size;
                if (!oldestEntry || stat.mtime < oldestEntry) {
                    oldestEntry = stat.mtime;
                }
                if (!newestEntry || stat.mtime > newestEntry) {
                    newestEntry = stat.mtime;
                }
            }
            const totalRequests = this.stats.hits + this.stats.misses;
            const hitRate = totalRequests > 0 ? (this.stats.hits / totalRequests) * 100 : 0;
            return {
                totalEntries: jsonFiles.length,
                totalSize: totalSize / (1024 * 1024), // Convert to MB
                oldestEntry,
                newestEntry,
                hitRate
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to get cache stats: ${error}`);
            throw error;
        }
    }
    /**
     * 优化缓存存储
     */
    async optimizeCache() {
        try {
            logger_1.logger.info('Optimizing cache...');
            // 清理过期缓存
            await this.cleanupExpiredCache();
            // 清理超过大小限制的缓存
            await this.cleanupBySize();
            logger_1.logger.info('Cache optimization completed');
        }
        catch (error) {
            logger_1.logger.error(`Failed to optimize cache: ${error}`);
        }
    }
    /**
     * 预热缓存（为常见文件生成缓存）
     */
    async warmupCache(filePaths) {
        logger_1.logger.info(`Warming up cache for ${filePaths.length} files...`);
        // 这里只是创建缓存结构，实际的缓存数据需要在分析时生成
        await this.ensureCacheDirectory();
        logger_1.logger.info('Cache warmup completed');
    }
    /**
     * 获取缓存命中率
     */
    getCacheHitRate() {
        const total = this.stats.hits + this.stats.misses;
        return total > 0 ? (this.stats.hits / total) * 100 : 0;
    }
    /**
     * 重置统计信息
     */
    resetStats() {
        this.stats = { hits: 0, misses: 0 };
    }
    // Private methods
    getCacheFilePath(cacheKey) {
        return path.join(this.cacheDir, `${cacheKey}.json`);
    }
    async ensureCacheDirectory() {
        await fs.ensureDir(this.cacheDir);
    }
    async deleteCacheEntry(cacheKey) {
        const cacheFile = this.getCacheFilePath(cacheKey);
        if (await fs.pathExists(cacheFile)) {
            await fs.remove(cacheFile);
        }
    }
    isCacheExpired(cacheEntry) {
        const lastModified = new Date(cacheEntry.lastModified);
        const now = new Date();
        const diffHours = (now.getTime() - lastModified.getTime()) / (1000 * 60 * 60);
        return diffHours > this.defaultTTL;
    }
    async cleanupIfNeeded() {
        const stats = await this.getCacheStats();
        if (stats.totalSize > this.maxCacheSize) {
            await this.cleanupBySize();
        }
    }
    async cleanupBySize() {
        try {
            const files = await fs.readdir(this.cacheDir);
            const cacheFiles = [];
            // 收集所有缓存文件信息
            for (const file of files) {
                if (!file.endsWith('.json'))
                    continue;
                const filePath = path.join(this.cacheDir, file);
                const stat = await fs.stat(filePath);
                cacheFiles.push({
                    path: filePath,
                    name: file,
                    size: stat.size,
                    mtime: stat.mtime
                });
            }
            // 按修改时间排序（最旧的先删除）
            cacheFiles.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());
            let currentSize = cacheFiles.reduce((sum, file) => sum + file.size, 0);
            const targetSize = this.maxCacheSize * 1024 * 1024; // Convert to bytes
            let deletedCount = 0;
            // 删除最旧的文件直到达到目标大小
            for (const file of cacheFiles) {
                if (currentSize <= targetSize)
                    break;
                await fs.remove(file.path);
                currentSize -= file.size;
                deletedCount++;
                logger_1.logger.debug(`Removed old cache file: ${file.name}`);
            }
            if (deletedCount > 0) {
                logger_1.logger.info(`Cleaned up ${deletedCount} cache files to free space`);
            }
        }
        catch (error) {
            logger_1.logger.error(`Failed to cleanup cache by size: ${error}`);
        }
    }
}
exports.CacheManager = CacheManager;
//# sourceMappingURL=cache.js.map