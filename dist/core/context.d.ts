import { ParsedFile } from './parser';
import { GitFileChange } from '../types';
export interface CodeContext {
    file: string;
    parsedFile: ParsedFile | null;
    relatedFiles: string[];
    dependencies: DependencyInfo[];
    usageInfo: UsageInfo;
    complexity: ComplexityInfo;
}
export interface DependencyInfo {
    module: string;
    type: 'import' | 'require' | 'dynamic';
    line: number;
    isLocal: boolean;
    resolvedPath?: string | undefined;
}
export interface UsageInfo {
    functionsUsed: string[];
    variablesUsed: string[];
    exportsUsed: string[];
    complexity: number;
}
export interface ComplexityInfo {
    cyclomaticComplexity: number;
    cognitiveComplexity: number;
    linesOfCode: number;
    maintainabilityIndex: number;
}
export declare class ContextAnalyzer {
    private parser;
    private projectRoot;
    private parseCache;
    constructor(projectRoot?: string);
    /**
     * 分析文件的代码上下文
     */
    analyzeContext(fileChange: GitFileChange): Promise<CodeContext>;
    /**
     * 分析依赖关系
     */
    private analyzeDependencies;
    /**
     * 查找相关文件
     */
    private findRelatedFiles;
    /**
     * 分析使用情况
     */
    private analyzeUsage;
    /**
     * 分析代码复杂度
     */
    private analyzeComplexity;
    /**
     * 计算圈复杂度
     */
    private calculateCyclomaticComplexity;
    /**
     * 计算认知复杂度
     */
    private calculateCognitiveComplexity;
    /**
     * 计算可维护性指数
     */
    private calculateMaintainabilityIndex;
    /**
     * 计算变更复杂度
     */
    private calculateChangeComplexity;
    /**
     * 获取依赖类型
     */
    private getDependencyType;
    /**
     * 查找依赖在代码中的行号
     */
    private findDependencyLine;
    /**
     * 判断是否为本地依赖
     */
    private isLocalDependency;
    /**
     * 解析依赖路径
     */
    private resolveDependencyPath;
    /**
     * 创建空的上下文信息
     */
    private createEmptyContext;
    /**
     * 清理缓存
     */
    clearCache(): void;
    /**
     * 获取缓存的解析结果
     */
    getCachedParsedFile(filePath: string): ParsedFile | undefined;
}
//# sourceMappingURL=context.d.ts.map