"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IssueClassifier = void 0;
const logger_1 = require("../utils/logger");
class IssueClassifier {
    constructor(config) {
        this.rules = new Map();
        this.config = config;
        this.initializeClassificationRules();
    }
    /**
     * 分类和调整AI返回的问题
     */
    classifyIssues(issues) {
        const results = [];
        for (const issue of issues) {
            try {
                const result = this.classifySingleIssue(issue);
                results.push(result);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to classify issue: ${error}`);
                // 返回未调整的问题
                results.push({
                    originalIssue: issue,
                    adjustedIssue: issue,
                    confidence: 0.5,
                    reasoning: ['Classification failed, using original assessment'],
                    suggestions: []
                });
            }
        }
        // 根据严格程度过滤问题
        const filteredResults = this.applyStrictnessFilter(results);
        logger_1.logger.info(`Classified ${results.length} issues, ${filteredResults.length} passed strictness filter`);
        return filteredResults;
    }
    /**
     * 分类单个问题
     */
    classifySingleIssue(issue) {
        const reasoning = [];
        const suggestions = [];
        let adjustedIssue = { ...issue };
        let confidence = 0.7; // 基础置信度
        // 1. 验证和调整问题类型
        const typeResult = this.validateAndAdjustType(issue);
        if (typeResult.adjusted) {
            adjustedIssue.type = typeResult.type;
            reasoning.push(`Adjusted type from ${issue.type} to ${typeResult.type}: ${typeResult.reason}`);
            confidence += typeResult.confidenceBoost;
        }
        // 2. 重新评估严重程度
        const severityResult = this.assessSeverity(adjustedIssue);
        if (severityResult.adjusted) {
            adjustedIssue.severity = severityResult.severity;
            reasoning.push(`Adjusted severity from ${issue.severity} to ${severityResult.severity}: ${severityResult.reason}`);
            confidence += severityResult.confidenceBoost;
        }
        // 3. 增强问题描述和建议
        const enhancedDescription = this.enhanceDescription(adjustedIssue);
        const enhancedSuggestion = this.enhanceSuggestion(adjustedIssue);
        if (enhancedDescription !== adjustedIssue.description) {
            adjustedIssue.description = enhancedDescription;
            reasoning.push('Enhanced problem description with additional context');
        }
        if (enhancedSuggestion !== adjustedIssue.suggestion) {
            adjustedIssue.suggestion = enhancedSuggestion;
            reasoning.push('Enhanced suggestion with specific action items');
        }
        // 4. 添加最佳实践参考
        const references = this.addBestPracticeReferences(adjustedIssue);
        if (references.length > (adjustedIssue.references?.length || 0)) {
            adjustedIssue.references = references;
            reasoning.push('Added relevant best practice references');
        }
        // 5. 生成改进建议
        suggestions.push(...this.generateImprovementSuggestions(adjustedIssue));
        return {
            originalIssue: issue,
            adjustedIssue,
            confidence: Math.min(1.0, confidence),
            reasoning,
            suggestions
        };
    }
    /**
     * 验证并调整问题类型
     */
    validateAndAdjustType(issue) {
        const keywords = {
            Logic: ['null', 'undefined', 'error', 'exception', 'bug', 'crash', 'fail', 'wrong', 'incorrect'],
            Performance: ['slow', 'performance', 'memory', 'leak', 'inefficient', 'optimize', 'speed', 'lag'],
            Security: ['xss', 'csrf', 'injection', 'vulnerability', 'exploit', 'attack', 'security', 'auth'],
            Style: ['format', 'naming', 'convention', 'style', 'consistent', 'readable', 'clean'],
            Business: ['logic', 'requirement', 'workflow', 'process', 'business', 'rule', 'validation']
        };
        const content = `${issue.message} ${issue.description}`.toLowerCase();
        const scores = new Map();
        // 计算每个类型的匹配分数
        for (const [type, typeKeywords] of Object.entries(keywords)) {
            let score = 0;
            for (const keyword of typeKeywords) {
                if (content.includes(keyword)) {
                    score++;
                }
            }
            scores.set(type, score);
        }
        // 找到最高分的类型
        const maxScore = Math.max(...scores.values());
        const bestType = [...scores.entries()].find(([_, score]) => score === maxScore)?.[0];
        if (bestType && bestType !== issue.type && maxScore >= 2) {
            return {
                type: bestType,
                adjusted: true,
                reason: `Keyword analysis suggests ${bestType} (score: ${maxScore})`,
                confidenceBoost: 0.1
            };
        }
        return {
            type: issue.type,
            adjusted: false,
            reason: 'Original type classification is appropriate',
            confidenceBoost: 0.05
        };
    }
    /**
     * 重新评估严重程度
     */
    assessSeverity(issue) {
        const criticalKeywords = ['crash', 'security', 'exploit', 'vulnerability', 'data loss', 'corruption'];
        const highKeywords = ['error', 'exception', 'fail', 'memory leak', 'performance'];
        const mediumKeywords = ['warning', 'improvement', 'optimization', 'refactor'];
        const lowKeywords = ['style', 'format', 'naming', 'comment'];
        const content = `${issue.message} ${issue.description}`.toLowerCase();
        // 安全问题自动升级到Critical
        if (issue.type === 'Security') {
            const hasCriticalSecurity = criticalKeywords.some(keyword => content.includes(keyword));
            if (hasCriticalSecurity && issue.severity !== 'Critical') {
                return {
                    severity: 'Critical',
                    adjusted: true,
                    reason: 'Security issues with critical keywords upgraded to Critical',
                    confidenceBoost: 0.2
                };
            }
        }
        // 基于关键词重新评估
        let suggestedSeverity = issue.severity;
        let reason = '';
        if (criticalKeywords.some(keyword => content.includes(keyword))) {
            suggestedSeverity = 'Critical';
            reason = 'Contains critical impact keywords';
        }
        else if (highKeywords.some(keyword => content.includes(keyword))) {
            suggestedSeverity = 'High';
            reason = 'Contains high impact keywords';
        }
        else if (mediumKeywords.some(keyword => content.includes(keyword))) {
            suggestedSeverity = 'Medium';
            reason = 'Contains medium impact keywords';
        }
        else if (lowKeywords.some(keyword => content.includes(keyword))) {
            suggestedSeverity = 'Low';
            reason = 'Contains low impact keywords';
        }
        // 只在建议严重程度比原始严重程度更高时调整
        const severityOrder = { Low: 1, Medium: 2, High: 3, Critical: 4 };
        if (severityOrder[suggestedSeverity] > severityOrder[issue.severity]) {
            return {
                severity: suggestedSeverity,
                adjusted: true,
                reason: `Upgraded severity: ${reason}`,
                confidenceBoost: 0.15
            };
        }
        return {
            severity: issue.severity,
            adjusted: false,
            reason: 'Original severity assessment is appropriate',
            confidenceBoost: 0.05
        };
    }
    /**
     * 增强问题描述
     */
    enhanceDescription(issue) {
        let enhanced = issue.description;
        // 添加影响分析
        const impactAnalysis = this.generateImpactAnalysis(issue);
        if (impactAnalysis) {
            enhanced += `\n\n**Impact**: ${impactAnalysis}`;
        }
        // 添加根因分析
        const rootCause = this.generateRootCauseAnalysis(issue);
        if (rootCause) {
            enhanced += `\n\n**Root Cause**: ${rootCause}`;
        }
        return enhanced;
    }
    /**
     * 增强修复建议
     */
    enhanceSuggestion(issue) {
        let enhanced = issue.suggestion;
        // 添加具体步骤
        const steps = this.generateFixingSteps(issue);
        if (steps.length > 0) {
            enhanced += '\n\n**Step-by-step fix**:\n';
            steps.forEach((step, index) => {
                enhanced += `${index + 1}. ${step}\n`;
            });
        }
        // 添加验证方法
        const verification = this.generateVerificationSteps(issue);
        if (verification.length > 0) {
            enhanced += '\n**Verification**:\n';
            verification.forEach(step => {
                enhanced += `- ${step}\n`;
            });
        }
        return enhanced;
    }
    /**
     * 添加最佳实践参考
     */
    addBestPracticeReferences(issue) {
        const references = [...(issue.references || [])];
        const referenceMap = new Map([
            ['Logic', [
                    'https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Errors',
                    'https://javascript.info/debugging-chrome'
                ]],
            ['Performance', [
                    'https://web.dev/performance/',
                    'https://developer.mozilla.org/en-US/docs/Web/Performance'
                ]],
            ['Security', [
                    'https://owasp.org/www-project-top-ten/',
                    'https://developer.mozilla.org/en-US/docs/Web/Security'
                ]],
            ['Style', [
                    'https://github.com/airbnb/javascript',
                    'https://standardjs.com/'
                ]],
            ['Business', [
                    'https://refactoring.guru/',
                    'https://clean-code-developer.com/'
                ]]
        ]);
        const typeReferences = referenceMap.get(issue.type);
        if (typeReferences) {
            typeReferences.forEach(ref => {
                if (!references.includes(ref)) {
                    references.push(ref);
                }
            });
        }
        return references;
    }
    /**
     * 生成改进建议
     */
    generateImprovementSuggestions(issue) {
        const suggestions = [];
        switch (issue.type) {
            case 'Logic':
                suggestions.push('Consider adding unit tests to catch logic errors early');
                suggestions.push('Use TypeScript for better type safety');
                break;
            case 'Performance':
                suggestions.push('Profile the code to identify bottlenecks');
                suggestions.push('Consider code splitting for better loading performance');
                break;
            case 'Security':
                suggestions.push('Implement security linting rules');
                suggestions.push('Regular security audits and dependency updates');
                break;
            case 'Style':
                suggestions.push('Set up automatic code formatting with Prettier');
                suggestions.push('Use ESLint for consistent code style');
                break;
            case 'Business':
                suggestions.push('Add comprehensive documentation for business logic');
                suggestions.push('Consider code review by domain experts');
                break;
        }
        return suggestions;
    }
    /**
     * 根据严格程度过滤问题
     */
    applyStrictnessFilter(results) {
        return results.filter(result => {
            const issue = result.adjustedIssue;
            const strictness = this.config.rules[issue.type.toLowerCase()];
            if (strictness === 'loose') {
                return issue.severity === 'Critical';
            }
            else if (strictness === 'normal') {
                return ['Critical', 'High'].includes(issue.severity);
            }
            else { // strict
                return true; // 显示所有问题
            }
        });
    }
    /**
     * 生成影响分析
     */
    generateImpactAnalysis(issue) {
        const impactMap = new Map([
            ['Critical', 'This issue could cause system failure, data loss, or security breaches'],
            ['High', 'This issue may significantly impact functionality or user experience'],
            ['Medium', 'This issue could cause minor inconveniences or maintainability problems'],
            ['Low', 'This issue primarily affects code quality or style consistency']
        ]);
        return impactMap.get(issue.severity) || 'Impact assessment unavailable';
    }
    /**
     * 生成根因分析
     */
    generateRootCauseAnalysis(issue) {
        const typeMap = new Map([
            ['Logic', 'Often caused by insufficient input validation, incorrect assumptions, or missing edge case handling'],
            ['Performance', 'Usually results from inefficient algorithms, unnecessary computations, or resource leaks'],
            ['Security', 'Typically stems from insufficient input sanitization, missing authentication, or insecure data handling'],
            ['Style', 'Generally caused by inconsistent coding standards or lack of automated formatting'],
            ['Business', 'Often results from unclear requirements, incomplete understanding of business rules, or changing specifications']
        ]);
        return typeMap.get(issue.type) || 'Root cause analysis not available';
    }
    /**
     * 生成修复步骤
     */
    generateFixingSteps(issue) {
        const steps = [];
        switch (issue.type) {
            case 'Logic':
                steps.push('Identify the specific condition causing the error');
                steps.push('Add appropriate null/undefined checks');
                steps.push('Test with various input scenarios');
                break;
            case 'Performance':
                steps.push('Profile the code to measure current performance');
                steps.push('Implement the suggested optimization');
                steps.push('Measure performance improvement');
                break;
            case 'Security':
                steps.push('Implement proper input validation');
                steps.push('Use secure coding practices');
                steps.push('Test with security scanning tools');
                break;
            case 'Style':
                steps.push('Apply consistent formatting');
                steps.push('Follow established naming conventions');
                steps.push('Update code documentation');
                break;
            case 'Business':
                steps.push('Review business requirements');
                steps.push('Implement proper validation logic');
                steps.push('Add comprehensive error handling');
                break;
        }
        return steps;
    }
    /**
     * 生成验证步骤
     */
    generateVerificationSteps(issue) {
        const steps = [];
        steps.push('Run existing tests to ensure no regressions');
        steps.push('Add new tests covering the fixed issue');
        switch (issue.type) {
            case 'Logic':
                steps.push('Test edge cases and boundary conditions');
                break;
            case 'Performance':
                steps.push('Run performance benchmarks');
                break;
            case 'Security':
                steps.push('Perform security testing');
                break;
            case 'Style':
                steps.push('Run linting tools to verify style compliance');
                break;
            case 'Business':
                steps.push('Validate with business stakeholders');
                break;
        }
        return steps;
    }
    /**
     * 初始化分类规则
     */
    initializeClassificationRules() {
        // 这里可以扩展为更复杂的规则系统
        // this.rules could be used here in the future
        logger_1.logger.debug('Initialized issue classification rules');
    }
    /**
     * 获取分类统计
     */
    getClassificationStats(results) {
        const adjustedCount = results.filter(r => r.confidence < 1.0).length;
        const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
        const severityDist = results.reduce((dist, r) => {
            dist[r.adjustedIssue.severity] = (dist[r.adjustedIssue.severity] || 0) + 1;
            return dist;
        }, {});
        const typeDist = results.reduce((dist, r) => {
            dist[r.adjustedIssue.type] = (dist[r.adjustedIssue.type] || 0) + 1;
            return dist;
        }, {});
        return {
            totalIssues: results.length,
            adjustedIssues: adjustedCount,
            averageConfidence: avgConfidence,
            severityDistribution: severityDist,
            typeDistribution: typeDist
        };
    }
}
exports.IssueClassifier = IssueClassifier;
//# sourceMappingURL=classifier.js.map