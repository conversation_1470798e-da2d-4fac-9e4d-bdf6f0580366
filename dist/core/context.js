"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextAnalyzer = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs-extra"));
const parser_1 = require("./parser");
const logger_1 = require("../utils/logger");
class ContextAnalyzer {
    constructor(projectRoot) {
        this.parseCache = new Map();
        this.parser = new parser_1.CodeParser();
        this.projectRoot = projectRoot || process.cwd();
    }
    /**
     * 分析文件的代码上下文
     */
    async analyzeContext(fileChange) {
        try {
            logger_1.logger.debug(`Analyzing context for: ${fileChange.file}`);
            const filePath = path.resolve(this.projectRoot, fileChange.file);
            const parsedFile = await this.parser.parseFile(filePath);
            if (!parsedFile) {
                return this.createEmptyContext(fileChange.file);
            }
            // 缓存解析结果
            this.parseCache.set(fileChange.file, parsedFile);
            const dependencies = await this.analyzeDependencies(parsedFile);
            const relatedFiles = await this.findRelatedFiles(parsedFile);
            const usageInfo = this.analyzeUsage(parsedFile, fileChange.changes);
            const complexity = this.analyzeComplexity(parsedFile);
            return {
                file: fileChange.file,
                parsedFile,
                relatedFiles,
                dependencies,
                usageInfo,
                complexity
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to analyze context for ${fileChange.file}: ${error}`);
            return this.createEmptyContext(fileChange.file);
        }
    }
    /**
     * 分析依赖关系
     */
    async analyzeDependencies(parsedFile) {
        const dependencies = [];
        for (const dep of parsedFile.dependencies) {
            const depInfo = {
                module: dep,
                type: this.getDependencyType(dep, parsedFile.content),
                line: this.findDependencyLine(dep, parsedFile.content),
                isLocal: this.isLocalDependency(dep),
                resolvedPath: undefined
            };
            // 尝试解析本地依赖的完整路径
            if (depInfo.isLocal) {
                depInfo.resolvedPath = await this.resolveDependencyPath(dep, parsedFile.filePath);
            }
            dependencies.push(depInfo);
        }
        return dependencies;
    }
    /**
     * 查找相关文件
     */
    async findRelatedFiles(parsedFile) {
        const relatedFiles = [];
        const fileDir = path.dirname(parsedFile.filePath);
        // 查找导入的本地文件
        for (const dep of parsedFile.dependencies) {
            if (this.isLocalDependency(dep)) {
                const resolvedPath = await this.resolveDependencyPath(dep, parsedFile.filePath);
                if (resolvedPath) {
                    relatedFiles.push(path.relative(this.projectRoot, resolvedPath));
                }
            }
        }
        // 查找同目录下的相关文件
        try {
            const files = await fs.readdir(fileDir);
            const baseName = path.basename(parsedFile.filePath, path.extname(parsedFile.filePath));
            for (const file of files) {
                const filePath = path.join(fileDir, file);
                const relativePath = path.relative(this.projectRoot, filePath);
                // 查找同名的不同扩展名文件
                if (file.startsWith(baseName) && file !== path.basename(parsedFile.filePath)) {
                    relatedFiles.push(relativePath);
                }
                // 查找测试文件
                if (file.includes('.test.') || file.includes('.spec.')) {
                    const testBaseName = file.replace(/\.(test|spec)\..*$/, '');
                    if (testBaseName === baseName) {
                        relatedFiles.push(relativePath);
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.debug(`Failed to scan directory ${fileDir}: ${error}`);
        }
        return [...new Set(relatedFiles)]; // 去重
    }
    /**
     * 分析使用情况
     */
    analyzeUsage(parsedFile, changes) {
        const functionsUsed = new Set();
        const variablesUsed = new Set();
        const exportsUsed = new Set();
        // 简单的使用情况分析
        const changedLines = changes.filter(c => c.type === 'added' || c.type === 'modified')
            .map(c => c.content);
        for (const line of changedLines) {
            // 查找函数调用
            parsedFile.functions.forEach(func => {
                if (line.includes(func.name + '(')) {
                    functionsUsed.add(func.name);
                }
            });
            // 查找变量使用
            parsedFile.variables.forEach(variable => {
                if (line.includes(variable.name)) {
                    variablesUsed.add(variable.name);
                }
            });
            // 查找导出使用
            parsedFile.exports.forEach(exp => {
                if (line.includes(exp)) {
                    exportsUsed.add(exp);
                }
            });
        }
        return {
            functionsUsed: Array.from(functionsUsed),
            variablesUsed: Array.from(variablesUsed),
            exportsUsed: Array.from(exportsUsed),
            complexity: this.calculateChangeComplexity(changedLines)
        };
    }
    /**
     * 分析代码复杂度
     */
    analyzeComplexity(parsedFile) {
        const lines = parsedFile.content.split('\n');
        const linesOfCode = lines.filter(line => line.trim() && !line.trim().startsWith('//')).length;
        const cyclomaticComplexity = this.calculateCyclomaticComplexity(parsedFile);
        const cognitiveComplexity = this.calculateCognitiveComplexity(parsedFile);
        const maintainabilityIndex = this.calculateMaintainabilityIndex(linesOfCode, cyclomaticComplexity, parsedFile.functions.length);
        return {
            cyclomaticComplexity,
            cognitiveComplexity,
            linesOfCode,
            maintainabilityIndex
        };
    }
    /**
     * 计算圈复杂度
     */
    calculateCyclomaticComplexity(parsedFile) {
        if (!parsedFile.ast)
            return 1;
        let complexity = 1; // 基础复杂度
        const content = parsedFile.content;
        // 简单的关键字计数方式
        const keywords = ['if', 'else', 'while', 'for', 'switch', 'case', 'catch', '&&', '||', '?'];
        for (const keyword of keywords) {
            const regex = new RegExp(`\\b${keyword}\\b`, 'g');
            const matches = content.match(regex);
            if (matches) {
                complexity += matches.length;
            }
        }
        return Math.max(1, complexity);
    }
    /**
     * 计算认知复杂度
     */
    calculateCognitiveComplexity(parsedFile) {
        // 简化的认知复杂度计算
        const content = parsedFile.content;
        let complexity = 0;
        // 嵌套结构增加复杂度
        const nestingKeywords = ['if', 'for', 'while', 'switch', 'try'];
        const lines = content.split('\n');
        let nesting = 0;
        for (const line of lines) {
            const trimmed = line.trim();
            // 增加嵌套
            for (const keyword of nestingKeywords) {
                if (trimmed.includes(keyword + ' (') || trimmed.includes(keyword + '(')) {
                    nesting++;
                    complexity += nesting;
                }
            }
            // 减少嵌套
            if (trimmed === '}') {
                nesting = Math.max(0, nesting - 1);
            }
            // 逻辑运算符
            if (trimmed.includes('&&') || trimmed.includes('||')) {
                complexity += 1;
            }
        }
        return complexity;
    }
    /**
     * 计算可维护性指数
     */
    calculateMaintainabilityIndex(loc, complexity, functions) {
        // 简化的可维护性指数计算
        const halsteadVolume = Math.log(Math.max(1, functions)) * 10;
        const mi = Math.max(0, (171 - 5.2 * Math.log(halsteadVolume) - 0.23 * complexity - 16.2 * Math.log(loc)) * 100 / 171);
        return Math.round(mi);
    }
    /**
     * 计算变更复杂度
     */
    calculateChangeComplexity(changedLines) {
        let complexity = 0;
        for (const line of changedLines) {
            const trimmed = line.trim();
            // 控制结构
            if (/(if|for|while|switch|try|catch)/.test(trimmed)) {
                complexity += 2;
            }
            // 函数定义
            if (/function|=>|\w+\s*\(.*\)\s*{/.test(trimmed)) {
                complexity += 1;
            }
            // 逻辑运算
            if (/&&|\|\||!/.test(trimmed)) {
                complexity += 1;
            }
        }
        return complexity;
    }
    /**
     * 获取依赖类型
     */
    getDependencyType(dep, content) {
        if (content.includes(`import`) && content.includes(dep)) {
            if (content.includes(`import('${dep}')`) || content.includes(`import("${dep}")`)) {
                return 'dynamic';
            }
            return 'import';
        }
        return 'require';
    }
    /**
     * 查找依赖在代码中的行号
     */
    findDependencyLine(dep, content) {
        const lines = content.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(dep)) {
                return i + 1;
            }
        }
        return 0;
    }
    /**
     * 判断是否为本地依赖
     */
    isLocalDependency(dep) {
        return dep.startsWith('./') || dep.startsWith('../') || dep.startsWith('/');
    }
    /**
     * 解析依赖路径
     */
    async resolveDependencyPath(dep, fromFile) {
        try {
            const fromDir = path.dirname(fromFile);
            const resolved = path.resolve(fromDir, dep);
            // 尝试不同的扩展名
            const extensions = ['.js', '.ts', '.jsx', '.tsx', '.vue'];
            for (const ext of extensions) {
                const withExt = resolved + ext;
                if (await fs.pathExists(withExt)) {
                    return withExt;
                }
            }
            // 尝试index文件
            for (const ext of extensions) {
                const indexFile = path.join(resolved, `index${ext}`);
                if (await fs.pathExists(indexFile)) {
                    return indexFile;
                }
            }
            return undefined;
        }
        catch {
            return undefined;
        }
    }
    /**
     * 创建空的上下文信息
     */
    createEmptyContext(file) {
        return {
            file,
            parsedFile: null,
            relatedFiles: [],
            dependencies: [],
            usageInfo: {
                functionsUsed: [],
                variablesUsed: [],
                exportsUsed: [],
                complexity: 0
            },
            complexity: {
                cyclomaticComplexity: 1,
                cognitiveComplexity: 0,
                linesOfCode: 0,
                maintainabilityIndex: 100
            }
        };
    }
    /**
     * 清理缓存
     */
    clearCache() {
        this.parseCache.clear();
    }
    /**
     * 获取缓存的解析结果
     */
    getCachedParsedFile(filePath) {
        return this.parseCache.get(filePath);
    }
}
exports.ContextAnalyzer = ContextAnalyzer;
//# sourceMappingURL=context.js.map