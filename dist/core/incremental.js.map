{"version": 3, "file": "incremental.js", "sourceRoot": "", "sources": ["../../src/core/incremental.ts"], "names": [], "mappings": ";;;AAEA,4CAAyC;AAkBzC,MAAa,mBAAmB;IAI9B,YAAY,YAA0B,EAAE,cAAsB,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC,QAAQ;IAC1C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CACrC,WAA4B;QAE5B,MAAM,OAAO,GAAqB,EAAE,CAAC;QACrC,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,eAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QAEjD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAEhF,IAAI,aAAa,EAAE,CAAC;gBAClB,iBAAiB;gBACjB,SAAS,EAAE,CAAC;gBACZ,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;gBAC9E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,cAAc,IAAI,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YAC5E,CAAC;iBAAM,CAAC;gBACN,eAAe;gBACf,WAAW,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAErB,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YACpD,CAAC;YAED,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAChD,CAAC;QAED,cAAc;QACd,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhG,MAAM,MAAM,GAA8B;YACxC,OAAO;YACP,SAAS;YACT,WAAW;YACX,UAAU;YACV,cAAc;SACf,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,sBAAsB,OAAO,CAAC,MAAM,WAAW,SAAS,gBAAgB,WAAW,eAAe,CAAC,CAAC;QAChH,eAAM,CAAC,IAAI,CAAC,qBAAqB,cAAc,IAAI,UAAU,KAAK,CAAC,CAAC,cAAc,GAAC,UAAU,CAAC,GAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAEpH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,wBAAwB,CACpC,UAAyB,EACzB,aAAsB;QAEtB,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAExD,OAAO;YACL,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,YAAY;YACZ,YAAY;YACZ,YAAY,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa;YAC7E,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,aAAa,CAAC;SAChE,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB,CAAC,UAAyB;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAElE,OAAO;YACL,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,YAAY;YACZ,YAAY,EAAE,EAAE;YAChB,YAAY,EAAE,MAAM;YACpB,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC;SACjD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAoB;QAC9C,MAAM,KAAK,GAAG,IAAI,GAAG,EAAU,CAAC;QAEhC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC1D,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,YAAsB;QAC5C,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QAEvC,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,SAAS;YACT,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;gBACrF,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC9B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACtB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,UAAyB;QACrD,mBAAmB;QACnB,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gBAAgB;QAChB,IAAI,UAAU,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,uBAAuB;QACvB,MAAM,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAC5D,iDAAiD,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAC9E,CAAC;QAEF,IAAI,oBAAoB,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,qBAAqB,CAC3B,UAAyB,EACzB,aAAuB;QAEvB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,SAAS;QACT,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE,CAAC;QAC5D,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK;YAAE,KAAK,IAAI,CAAC,CAAC;QAC9C,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,KAAK;YAAE,KAAK,IAAI,CAAC,CAAC;QAE9C,SAAS;QACT,IAAI,UAAU,CAAC,MAAM,KAAK,OAAO;YAAE,KAAK,IAAI,CAAC,CAAC;QAC9C,IAAI,UAAU,CAAC,MAAM,KAAK,UAAU;YAAE,KAAK,IAAI,CAAC,CAAC;QAEjD,QAAQ;QACR,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;QAC9C,IAAI,WAAW,GAAG,EAAE;YAAE,KAAK,IAAI,CAAC,CAAC;aAC5B,IAAI,WAAW,GAAG,EAAE;YAAE,KAAK,IAAI,CAAC,CAAC;aACjC,IAAI,WAAW,GAAG,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QAErC,gBAAgB;QAChB,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,cAAc,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC;YACpF,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC;YAE5E,KAAK,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,KAAK,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QACjC,CAAC;QAED,kBAAkB;QAClB,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QACvF,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QACzF,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,KAAK,IAAI,CAAC,CAAC;QAE9F,SAAS;QACT,IAAI,KAAK,IAAI,CAAC;YAAE,OAAO,MAAM,CAAC;QAC9B,IAAI,KAAK,IAAI,CAAC;YAAE,OAAO,QAAQ,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,QAAmC;QAC3D,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAyB;QAC9C,mBAAmB;QACnB,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,uBAAuB,CAC5B,OAAyB,EACzB,eAAuB,CAAC;QAExB,MAAM,OAAO,GAAuB,EAAE,CAAC;QACvC,IAAI,YAAY,GAAqB,EAAE,CAAC;QACxC,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAE/B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC;YAE1D,8BAA8B;YAC9B,IAAI,YAAY,CAAC,MAAM,IAAI,YAAY;gBACnC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,IAAI,sBAAsB,GAAG,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC;gBAC3E,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,YAAY,GAAG,CAAC,MAAM,CAAC,CAAC;gBACxB,sBAAsB,GAAG,UAAU,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1B,sBAAsB,IAAI,UAAU,CAAC;YACvC,CAAC;QACH,CAAC;QAED,WAAW;QACX,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7B,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC;QACjE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,MAAsB;QACtD,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,SAAS;QACT,QAAQ,MAAM,CAAC,YAAY,EAAE,CAAC;YAC5B,KAAK,MAAM;gBAAE,UAAU,IAAI,EAAE,CAAC;gBAAC,MAAM;YACrC,KAAK,aAAa;gBAAE,UAAU,IAAI,EAAE,CAAC;gBAAC,MAAM;YAC5C,KAAK,QAAQ;gBAAE,UAAU,IAAI,CAAC,CAAC;gBAAC,MAAM;QACxC,CAAC;QAED,OAAO;QACP,UAAU,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC;QAE9E,QAAQ;QACR,QAAQ,MAAM,CAAC,QAAQ,EAAE,CAAC;YACxB,KAAK,MAAM;gBAAE,UAAU,IAAI,GAAG,CAAC;gBAAC,MAAM;YACtC,KAAK,QAAQ;gBAAE,UAAU,IAAI,GAAG,CAAC;gBAAC,MAAM;YACxC,KAAK,KAAK;gBAAE,UAAU,IAAI,GAAG,CAAC;gBAAC,MAAM;QACvC,CAAC;QAED,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,yBAAyB,CAAC,MAAiC;QAChE,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,cAAc,CAAC,GAAG,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtG,MAAM,YAAY,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAEnG,OAAO;;uBAEY,MAAM,CAAC,OAAO,CAAC,MAAM;qBACvB,YAAY,MAAM,MAAM,CAAC,SAAS,UAAU,MAAM,CAAC,WAAW;0BACzD,UAAU,gBAAgB,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,UAAU;0BACpE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;0BACxD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,aAAa,CAAC,CAAC,MAAM;4BACjE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,KAAK,MAAM,CAAC,CAAC,MAAM;KACnF,CAAC,IAAI,EAAE,CAAC;IACX,CAAC;IAED;;OAEG;IACI,sBAAsB,CAC3B,gBAAwB,EACxB,YAAoB,EACpB,aAA8B;QAE9B,MAAM,QAAQ,GAAG,CAAC,CAAC,gBAAgB,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtE,IAAI,OAAO,GAAG,oBAAoB,gBAAgB,IAAI,YAAY,KAAK,QAAQ,IAAI,CAAC;QAEpF,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,IAAI,MAAM,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,YAAY,GAAG,CAAC;QACxE,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;CACF;AA3TD,kDA2TC"}