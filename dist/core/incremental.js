"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IncrementalAnalyzer = void 0;
const logger_1 = require("../utils/logger");
class IncrementalAnalyzer {
    constructor(cacheManager, contextSize = 5) {
        this.cacheManager = cacheManager;
        this.contextSize = contextSize; // 上下文行数
    }
    /**
     * 准备增量分析目标
     */
    async prepareIncrementalAnalysis(fileChanges) {
        const targets = [];
        let cacheHits = 0;
        let cacheMisses = 0;
        let totalLines = 0;
        let linesToAnalyze = 0;
        logger_1.logger.info('Preparing incremental analysis...');
        for (const fileChange of fileChanges) {
            const cachedResults = await this.cacheManager.getCachedResults(fileChange.file);
            if (cachedResults) {
                // 缓存命中，但仍需要分析变更行
                cacheHits++;
                const target = await this.prepareIncrementalTarget(fileChange, cachedResults);
                targets.push(target);
                linesToAnalyze += target.changedLines.length + target.contextLines.length;
            }
            else {
                // 缓存未命中，需要完整分析
                cacheMisses++;
                const target = await this.prepareFullTarget(fileChange);
                targets.push(target);
                linesToAnalyze += this.countFileLines(fileChange);
            }
            totalLines += this.countFileLines(fileChange);
        }
        // 根据优先级排序分析目标
        targets.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
        const result = {
            targets,
            cacheHits,
            cacheMisses,
            totalLines,
            linesToAnalyze
        };
        logger_1.logger.info(`Analysis prepared: ${targets.length} files, ${cacheHits} cache hits, ${cacheMisses} cache misses`);
        logger_1.logger.info(`Lines to analyze: ${linesToAnalyze}/${totalLines} (${((linesToAnalyze / totalLines) * 100).toFixed(1)}%)`);
        return result;
    }
    /**
     * 准备增量分析目标（有缓存的情况）
     */
    async prepareIncrementalTarget(fileChange, cachedResults) {
        const changedLines = this.extractChangedLines(fileChange.changes);
        const contextLines = this.getContextLines(changedLines);
        return {
            file: fileChange.file,
            changedLines,
            contextLines,
            analysisType: this.shouldUseFullAnalysis(fileChange) ? 'full' : 'incremental',
            priority: this.calculateFilePriority(fileChange, cachedResults)
        };
    }
    /**
     * 准备完整分析目标（无缓存的情况）
     */
    async prepareFullTarget(fileChange) {
        const changedLines = this.extractChangedLines(fileChange.changes);
        return {
            file: fileChange.file,
            changedLines,
            contextLines: [],
            analysisType: 'full',
            priority: this.calculateFilePriority(fileChange)
        };
    }
    /**
     * 提取变更的行号
     */
    extractChangedLines(changes) {
        const lines = new Set();
        for (const change of changes) {
            if (change.type === 'added' || change.type === 'modified') {
                lines.add(change.line);
            }
        }
        return Array.from(lines).sort((a, b) => a - b);
    }
    /**
     * 获取上下文行（变更行周围的行）
     */
    getContextLines(changedLines) {
        const contextLines = new Set();
        for (const line of changedLines) {
            // 添加上下文行
            for (let i = Math.max(1, line - this.contextSize); i <= line + this.contextSize; i++) {
                if (!changedLines.includes(i)) {
                    contextLines.add(i);
                }
            }
        }
        return Array.from(contextLines).sort((a, b) => a - b);
    }
    /**
     * 判断是否应该使用完整分析
     */
    shouldUseFullAnalysis(fileChange) {
        // 如果变更过多，使用完整分析更高效
        const changeRatio = fileChange.changes.length / this.countFileLines(fileChange);
        if (changeRatio > 0.5) {
            return true;
        }
        // 如果是新文件，使用完整分析
        if (fileChange.status === 'added') {
            return true;
        }
        // 如果变更包含结构性改动（函数、类定义等）
        const hasStructuralChanges = fileChange.changes.some(change => /^(class|function|const|let|var|import|export)\s/.test(change.content.trim()));
        if (hasStructuralChanges) {
            return true;
        }
        return false;
    }
    /**
     * 计算文件分析优先级
     */
    calculateFilePriority(fileChange, cachedResults) {
        let score = 0;
        // 基于文件类型
        const ext = fileChange.file.split('.').pop()?.toLowerCase();
        if (ext === 'ts' || ext === 'tsx')
            score += 2;
        if (ext === 'js' || ext === 'jsx')
            score += 1;
        // 基于变更类型
        if (fileChange.status === 'added')
            score += 3;
        if (fileChange.status === 'modified')
            score += 1;
        // 基于变更量
        const changeCount = fileChange.changes.length;
        if (changeCount > 50)
            score += 3;
        else if (changeCount > 20)
            score += 2;
        else if (changeCount > 5)
            score += 1;
        // 基于历史问题（如果有缓存）
        if (cachedResults) {
            const criticalIssues = cachedResults.filter(issue => issue.severity === 'Critical');
            const highIssues = cachedResults.filter(issue => issue.severity === 'High');
            score += criticalIssues.length * 3;
            score += highIssues.length * 2;
        }
        // 基于文件重要性（通过路径判断）
        if (fileChange.file.includes('/src/') || fileChange.file.includes('/lib/'))
            score += 2;
        if (fileChange.file.includes('/test/') || fileChange.file.includes('/spec/'))
            score -= 1;
        if (fileChange.file.includes('/config/') || fileChange.file.includes('/scripts/'))
            score -= 1;
        // 转换为优先级
        if (score >= 8)
            return 'high';
        if (score >= 4)
            return 'normal';
        return 'low';
    }
    /**
     * 获取优先级权重
     */
    getPriorityWeight(priority) {
        switch (priority) {
            case 'high': return 3;
            case 'normal': return 2;
            case 'low': return 1;
        }
    }
    /**
     * 计算文件行数
     */
    countFileLines(fileChange) {
        // 简单估算，实际实现中应该读取文件
        return Math.max(50, fileChange.changes.length * 2);
    }
    /**
     * 优化分析批次
     */
    optimizeAnalysisBatches(targets, maxBatchSize = 5) {
        const batches = [];
        let currentBatch = [];
        let currentBatchComplexity = 0;
        for (const target of targets) {
            const complexity = this.calculateTargetComplexity(target);
            // 如果加入当前target会超过批次大小限制，开始新批次
            if (currentBatch.length >= maxBatchSize ||
                (currentBatch.length > 0 && currentBatchComplexity + complexity > 100)) {
                batches.push(currentBatch);
                currentBatch = [target];
                currentBatchComplexity = complexity;
            }
            else {
                currentBatch.push(target);
                currentBatchComplexity += complexity;
            }
        }
        // 添加最后一个批次
        if (currentBatch.length > 0) {
            batches.push(currentBatch);
        }
        logger_1.logger.info(`Optimized analysis into ${batches.length} batches`);
        return batches;
    }
    /**
     * 计算分析目标的复杂度
     */
    calculateTargetComplexity(target) {
        let complexity = 0;
        // 基于分析类型
        switch (target.analysisType) {
            case 'full':
                complexity += 50;
                break;
            case 'incremental':
                complexity += 20;
                break;
            case 'cached':
                complexity += 5;
                break;
        }
        // 基于行数
        complexity += (target.changedLines.length + target.contextLines.length) * 0.5;
        // 基于优先级
        switch (target.priority) {
            case 'high':
                complexity *= 1.5;
                break;
            case 'normal':
                complexity *= 1.0;
                break;
            case 'low':
                complexity *= 0.5;
                break;
        }
        return Math.ceil(complexity);
    }
    /**
     * 生成增量分析报告
     */
    generateIncrementalReport(result) {
        const efficiency = ((result.totalLines - result.linesToAnalyze) / result.totalLines * 100).toFixed(1);
        const cacheHitRate = (result.cacheHits / (result.cacheHits + result.cacheMisses) * 100).toFixed(1);
        return `
📊 Incremental Analysis Report:
   Files to analyze: ${result.targets.length}
   Cache hit rate: ${cacheHitRate}% (${result.cacheHits} hits, ${result.cacheMisses} misses)
   Analysis efficiency: ${efficiency}% (analyzing ${result.linesToAnalyze}/${result.totalLines} lines)
   High priority files: ${result.targets.filter(t => t.priority === 'high').length}
   Incremental targets: ${result.targets.filter(t => t.analysisType === 'incremental').length}
   Full analysis targets: ${result.targets.filter(t => t.analysisType === 'full').length}
    `.trim();
    }
    /**
     * 更新分析进度
     */
    updateAnalysisProgress(completedTargets, totalTargets, currentTarget) {
        const progress = ((completedTargets / totalTargets) * 100).toFixed(1);
        let message = `Analyzing files: ${completedTargets}/${totalTargets} (${progress}%)`;
        if (currentTarget) {
            message += ` - ${currentTarget.file} (${currentTarget.analysisType})`;
        }
        logger_1.logger.info(message);
    }
}
exports.IncrementalAnalyzer = IncrementalAnalyzer;
//# sourceMappingURL=incremental.js.map