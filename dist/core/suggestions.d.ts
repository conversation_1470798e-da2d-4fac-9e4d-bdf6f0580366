import { Issue, GitFileChange } from '../types';
import { ClassificationResult } from './classifier';
import { GitBlame, BlameInfo } from '../git/blame';
export interface EnhancedIssue extends Issue {
    id: string;
    filePath: string;
    context: {
        surroundingLines: string[];
        functionName?: string;
        className?: string;
        complexity: number;
    };
    blame: BlameInfo | null;
    fixSuggestions: FixSuggestion[];
    relatedIssues: string[];
    priority: number;
}
export interface FixSuggestion {
    type: 'quick-fix' | 'refactor' | 'test-addition' | 'documentation';
    title: string;
    description: string;
    codeExample?: {
        before: string;
        after: string;
    };
    effort: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    automated: boolean;
}
export interface IssueGroup {
    category: string;
    issues: EnhancedIssue[];
    summary: string;
    overallPriority: number;
    estimatedFixTime: string;
}
export declare class FixSuggestionGenerator {
    private gitBlame;
    private issueCounter;
    constructor(gitBlame?: GitBlame);
    /**
     * 为分类结果生成修复建议
     */
    generateFixSuggestions(classificationResults: ClassificationResult[], fileChange: GitFileChange, fileContent: string): Promise<EnhancedIssue[]>;
    /**
     * 增强单个问题
     */
    private enhanceIssue;
    /**
     * 提取代码上下文
     */
    private extractCodeContext;
    /**
     * 获取blame信息
     */
    private getBlameInfo;
    /**
     * 为问题生成修复建议
     */
    private generateFixSuggestionsForIssue;
    /**
     * 生成逻辑错误修复建议
     */
    private generateLogicFixSuggestions;
    /**
     * 生成性能优化建议
     */
    private generatePerformanceFixSuggestions;
    /**
     * 生成安全修复建议
     */
    private generateSecurityFixSuggestions;
    /**
     * 生成代码风格修复建议
     */
    private generateStyleFixSuggestions;
    /**
     * 生成业务逻辑修复建议
     */
    private generateBusinessLogicFixSuggestions;
    /**
     * 生成通用建议
     */
    private generateUniversalSuggestions;
    /**
     * 寻找函数名
     */
    private findFunctionName;
    /**
     * 寻找类名
     */
    private findClassName;
    /**
     * 计算局部复杂度
     */
    private calculateLocalComplexity;
    /**
     * 计算问题优先级
     */
    private calculateIssuePriority;
    /**
     * 链接相关问题
     */
    private linkRelatedIssues;
    /**
     * 判断问题是否相关
     */
    private areIssuesRelated;
    /**
     * 计算所有问题的优先级
     */
    private calculatePriorities;
    /**
     * 创建基本的增强问题
     */
    private createBasicEnhancedIssue;
    /**
     * 生成问题ID
     */
    private generateIssueId;
    /**
     * 将问题分组
     */
    groupIssues(issues: EnhancedIssue[]): IssueGroup[];
    /**
     * 生成分组摘要
     */
    private generateGroupSummary;
    /**
     * 估算分组修复时间
     */
    private estimateGroupFixTime;
    /**
     * 寻找共同模式
     */
    private findCommonPatterns;
}
//# sourceMappingURL=suggestions.d.ts.map