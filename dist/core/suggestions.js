"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixSuggestionGenerator = void 0;
const blame_1 = require("../git/blame");
const logger_1 = require("../utils/logger");
class FixSuggestionGenerator {
    constructor(gitBlame) {
        this.issueCounter = 0;
        this.gitBlame = gitBlame || new blame_1.GitBlame();
    }
    /**
     * 为分类结果生成修复建议
     */
    async generateFixSuggestions(classificationResults, fileChange, fileContent) {
        const enhancedIssues = [];
        logger_1.logger.info(`Generating fix suggestions for ${classificationResults.length} issues in ${fileChange.file}`);
        for (const result of classificationResults) {
            try {
                const enhanced = await this.enhanceIssue(result, fileChange, fileContent);
                enhancedIssues.push(enhanced);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to enhance issue: ${error}`);
                // 创建基本的增强问题
                enhancedIssues.push(this.createBasicEnhancedIssue(result.adjustedIssue, fileChange.file));
            }
        }
        // 设置相关问题引用
        this.linkRelatedIssues(enhancedIssues);
        // 计算优先级
        this.calculatePriorities(enhancedIssues);
        logger_1.logger.info(`Generated enhanced suggestions for ${enhancedIssues.length} issues`);
        return enhancedIssues;
    }
    /**
     * 增强单个问题
     */
    async enhanceIssue(result, fileChange, fileContent) {
        const issue = result.adjustedIssue;
        const issueId = this.generateIssueId();
        // 获取代码上下文
        const context = this.extractCodeContext(issue.line, fileContent);
        // 获取blame信息
        const blame = await this.getBlameInfo(fileChange.file, issue.line);
        // 生成修复建议
        const fixSuggestions = this.generateFixSuggestionsForIssue(issue, context, fileContent);
        const enhancedIssue = {
            ...issue,
            id: issueId,
            filePath: fileChange.file,
            context,
            blame,
            fixSuggestions,
            relatedIssues: [],
            priority: this.calculateIssuePriority(issue, context, blame),
            // 更新author和commitTime
            author: blame?.author || issue.author,
            commitTime: blame?.commitTime || issue.commitTime
        };
        return enhancedIssue;
    }
    /**
     * 提取代码上下文
     */
    extractCodeContext(line, fileContent) {
        const lines = fileContent.split('\n');
        const contextSize = 3;
        const startLine = Math.max(0, line - contextSize - 1);
        const endLine = Math.min(lines.length - 1, line + contextSize - 1);
        const surroundingLines = lines.slice(startLine, endLine + 1)
            .map((content, index) => `${startLine + index + 1}: ${content}`);
        // 尝试找到函数名
        const functionName = this.findFunctionName(line, lines);
        // 尝试找到类名
        const className = this.findClassName(line, lines);
        // 计算复杂度（简单实现）
        const complexity = this.calculateLocalComplexity(startLine, endLine, lines);
        const context = {
            surroundingLines,
            functionName: functionName || undefined,
            className: className || undefined,
            complexity
        };
        return context;
    }
    /**
     * 获取blame信息
     */
    async getBlameInfo(filePath, line) {
        try {
            return await this.gitBlame.getLineBlame(filePath, line);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get blame info for ${filePath}:${line}: ${error}`);
            return null;
        }
    }
    /**
     * 为问题生成修复建议
     */
    generateFixSuggestionsForIssue(issue, context, fileContent) {
        const suggestions = [];
        // 基于问题类型生成建议
        switch (issue.type) {
            case 'Logic':
                suggestions.push(...this.generateLogicFixSuggestions(issue, context));
                break;
            case 'Performance':
                suggestions.push(...this.generatePerformanceFixSuggestions(issue, context));
                break;
            case 'Security':
                suggestions.push(...this.generateSecurityFixSuggestions(issue, context));
                break;
            case 'Style':
                suggestions.push(...this.generateStyleFixSuggestions(issue, context));
                break;
            case 'Business':
                suggestions.push(...this.generateBusinessLogicFixSuggestions(issue, context));
                break;
        }
        // 添加通用建议
        suggestions.push(...this.generateUniversalSuggestions(issue, context));
        // 按优先级排序
        return suggestions.sort((a, b) => {
            const impactOrder = { high: 3, medium: 2, low: 1 };
            const effortOrder = { low: 3, medium: 2, high: 1 };
            const aScore = impactOrder[a.impact] + effortOrder[a.effort];
            const bScore = impactOrder[b.impact] + effortOrder[b.effort];
            return bScore - aScore;
        });
    }
    /**
     * 生成逻辑错误修复建议
     */
    generateLogicFixSuggestions(issue, context) {
        const suggestions = [];
        // 空指针检查
        if (issue.message.toLowerCase().includes('null') || issue.message.toLowerCase().includes('undefined')) {
            suggestions.push({
                type: 'quick-fix',
                title: 'Add null/undefined check',
                description: 'Add defensive programming checks to prevent null/undefined errors',
                codeExample: {
                    before: 'const value = data.property;',
                    after: 'const value = data?.property ?? defaultValue;'
                },
                effort: 'low',
                impact: 'high',
                automated: true
            });
        }
        // 条件判断问题
        if (issue.message.toLowerCase().includes('condition')) {
            suggestions.push({
                type: 'refactor',
                title: 'Improve conditional logic',
                description: 'Refactor complex conditions for better readability and correctness',
                effort: 'medium',
                impact: 'medium',
                automated: false
            });
        }
        // 添加单元测试
        suggestions.push({
            type: 'test-addition',
            title: 'Add unit tests',
            description: 'Create unit tests to catch logic errors early and prevent regressions',
            effort: 'medium',
            impact: 'high',
            automated: false
        });
        return suggestions;
    }
    /**
     * 生成性能优化建议
     */
    generatePerformanceFixSuggestions(issue, context) {
        const suggestions = [];
        // 循环优化
        if (issue.message.toLowerCase().includes('loop')) {
            suggestions.push({
                type: 'refactor',
                title: 'Optimize loop performance',
                description: 'Reduce loop iterations or use more efficient data structures',
                codeExample: {
                    before: 'for(let i=0; i<array.length; i++) { /* heavy operation */ }',
                    after: 'const length = array.length; for(let i=0; i<length; i++) { /* operation */ }'
                },
                effort: 'low',
                impact: 'medium',
                automated: false
            });
        }
        // 内存泄漏
        if (issue.message.toLowerCase().includes('memory')) {
            suggestions.push({
                type: 'quick-fix',
                title: 'Fix memory leak',
                description: 'Properly clean up event listeners, timers, and references',
                effort: 'medium',
                impact: 'high',
                automated: false
            });
        }
        return suggestions;
    }
    /**
     * 生成安全修复建议
     */
    generateSecurityFixSuggestions(issue, context) {
        const suggestions = [];
        // XSS防护
        if (issue.message.toLowerCase().includes('xss')) {
            suggestions.push({
                type: 'quick-fix',
                title: 'Sanitize user input',
                description: 'Use proper sanitization to prevent XSS attacks',
                codeExample: {
                    before: 'element.innerHTML = userInput;',
                    after: 'element.textContent = userInput; // or use DOMPurify.sanitize(userInput)'
                },
                effort: 'low',
                impact: 'high',
                automated: true
            });
        }
        // 数据验证
        suggestions.push({
            type: 'refactor',
            title: 'Add input validation',
            description: 'Implement comprehensive input validation and sanitization',
            effort: 'medium',
            impact: 'high',
            automated: false
        });
        return suggestions;
    }
    /**
     * 生成代码风格修复建议
     */
    generateStyleFixSuggestions(issue, context) {
        const suggestions = [];
        // 格式化
        suggestions.push({
            type: 'quick-fix',
            title: 'Auto-format code',
            description: 'Apply consistent formatting using Prettier or similar tools',
            effort: 'low',
            impact: 'low',
            automated: true
        });
        // 命名改进
        if (issue.message.toLowerCase().includes('naming')) {
            suggestions.push({
                type: 'refactor',
                title: 'Improve variable/function naming',
                description: 'Use more descriptive and consistent naming conventions',
                effort: 'low',
                impact: 'medium',
                automated: false
            });
        }
        return suggestions;
    }
    /**
     * 生成业务逻辑修复建议
     */
    generateBusinessLogicFixSuggestions(issue, context) {
        const suggestions = [];
        // 文档化
        suggestions.push({
            type: 'documentation',
            title: 'Document business logic',
            description: 'Add clear documentation explaining the business rules and logic',
            effort: 'medium',
            impact: 'medium',
            automated: false
        });
        // 验证逻辑
        suggestions.push({
            type: 'refactor',
            title: 'Add business rule validation',
            description: 'Implement proper validation for business rules and constraints',
            effort: 'medium',
            impact: 'high',
            automated: false
        });
        return suggestions;
    }
    /**
     * 生成通用建议
     */
    generateUniversalSuggestions(issue, context) {
        const suggestions = [];
        // 高复杂度代码重构
        if (context.complexity > 10) {
            suggestions.push({
                type: 'refactor',
                title: 'Reduce code complexity',
                description: 'Break down complex functions into smaller, more manageable pieces',
                effort: 'high',
                impact: 'high',
                automated: false
            });
        }
        return suggestions;
    }
    /**
     * 寻找函数名
     */
    findFunctionName(targetLine, lines) {
        // 向上搜索函数定义
        for (let i = targetLine - 1; i >= Math.max(0, targetLine - 20); i--) {
            const line = lines[i]?.trim();
            if (!line)
                continue;
            const functionMatch = line.match(/(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:function|\(.*\)\s*=>)|(?:async\s+)?(\w+)\s*\()/);
            if (functionMatch) {
                return functionMatch[1] || functionMatch[2] || functionMatch[3];
            }
        }
        return undefined;
    }
    /**
     * 寻找类名
     */
    findClassName(targetLine, lines) {
        // 向上搜索类定义
        for (let i = targetLine - 1; i >= Math.max(0, targetLine - 50); i--) {
            const line = lines[i]?.trim();
            if (!line)
                continue;
            const classMatch = line.match(/class\s+(\w+)/);
            if (classMatch) {
                return classMatch[1];
            }
        }
        return undefined;
    }
    /**
     * 计算局部复杂度
     */
    calculateLocalComplexity(startLine, endLine, lines) {
        let complexity = 1;
        for (let i = startLine; i <= endLine; i++) {
            const line = lines[i]?.toLowerCase() || '';
            // 计算控制结构
            if (line.includes('if') || line.includes('while') || line.includes('for') ||
                line.includes('switch') || line.includes('case') || line.includes('catch')) {
                complexity++;
            }
            // 计算逻辑运算符
            const logicalOps = (line.match(/&&|\|\|/g) || []).length;
            complexity += logicalOps;
        }
        return complexity;
    }
    /**
     * 计算问题优先级
     */
    calculateIssuePriority(issue, context, blame) {
        let priority = 0;
        // 基于严重程度
        const severityWeights = { Critical: 40, High: 30, Medium: 20, Low: 10 };
        priority += severityWeights[issue.severity];
        // 基于问题类型
        const typeWeights = { Security: 15, Logic: 12, Performance: 10, Business: 8, Style: 5 };
        priority += typeWeights[issue.type];
        // 基于代码复杂度
        if (context.complexity > 15)
            priority += 10;
        else if (context.complexity > 10)
            priority += 5;
        // 基于最近修改（最近修改的代码优先级更高）
        if (blame) {
            const daysSinceModification = (Date.now() - new Date(blame.commitTime).getTime()) / (1000 * 60 * 60 * 24);
            if (daysSinceModification < 7)
                priority += 5;
            else if (daysSinceModification < 30)
                priority += 3;
        }
        return priority;
    }
    /**
     * 链接相关问题
     */
    linkRelatedIssues(issues) {
        for (let i = 0; i < issues.length; i++) {
            for (let j = i + 1; j < issues.length; j++) {
                if (this.areIssuesRelated(issues[i], issues[j])) {
                    issues[i].relatedIssues.push(issues[j].id);
                    issues[j].relatedIssues.push(issues[i].id);
                }
            }
        }
    }
    /**
     * 判断问题是否相关
     */
    areIssuesRelated(issue1, issue2) {
        // 同一函数内的问题
        if (issue1.context.functionName && issue1.context.functionName === issue2.context.functionName) {
            return true;
        }
        // 相近行数的问题
        if (Math.abs(issue1.line - issue2.line) <= 5) {
            return true;
        }
        // 相同类型和严重程度
        if (issue1.type === issue2.type && issue1.severity === issue2.severity) {
            return true;
        }
        return false;
    }
    /**
     * 计算所有问题的优先级
     */
    calculatePriorities(issues) {
        // 优先级已在enhanceIssue中计算，这里可以进行全局调整
        issues.sort((a, b) => b.priority - a.priority);
    }
    /**
     * 创建基本的增强问题
     */
    createBasicEnhancedIssue(issue, filePath) {
        return {
            ...issue,
            id: this.generateIssueId(),
            filePath,
            context: {
                surroundingLines: [],
                complexity: 1
            },
            blame: null,
            fixSuggestions: [],
            relatedIssues: [],
            priority: 50
        };
    }
    /**
     * 生成问题ID
     */
    generateIssueId() {
        return `issue_${Date.now()}_${++this.issueCounter}`;
    }
    /**
     * 将问题分组
     */
    groupIssues(issues) {
        const groups = new Map();
        // 按类型和严重程度分组
        issues.forEach(issue => {
            const key = `${issue.type}_${issue.severity}`;
            if (!groups.has(key)) {
                groups.set(key, []);
            }
            groups.get(key).push(issue);
        });
        const issueGroups = [];
        groups.forEach((groupIssues, key) => {
            const [type, severity] = key.split('_');
            const group = {
                category: `${type} - ${severity}`,
                issues: groupIssues.sort((a, b) => b.priority - a.priority),
                summary: this.generateGroupSummary(groupIssues),
                overallPriority: Math.max(...groupIssues.map(i => i.priority)),
                estimatedFixTime: this.estimateGroupFixTime(groupIssues)
            };
            issueGroups.push(group);
        });
        return issueGroups.sort((a, b) => b.overallPriority - a.overallPriority);
    }
    /**
     * 生成分组摘要
     */
    generateGroupSummary(issues) {
        if (issues.length === 1) {
            return issues[0].message;
        }
        const commonPatterns = this.findCommonPatterns(issues);
        if (commonPatterns.length > 0) {
            return `Multiple issues with common pattern: ${commonPatterns[0]}`;
        }
        return `${issues.length} issues of similar type and severity`;
    }
    /**
     * 估算分组修复时间
     */
    estimateGroupFixTime(issues) {
        const totalEffortMinutes = issues.reduce((total, issue) => {
            const avgMinutes = issue.fixSuggestions.reduce((sum, suggestion) => {
                const effortMinutes = { low: 15, medium: 60, high: 240 };
                return sum + effortMinutes[suggestion.effort];
            }, 0) / (issue.fixSuggestions.length || 1);
            return total + avgMinutes;
        }, 0);
        if (totalEffortMinutes < 60) {
            return `~${Math.round(totalEffortMinutes)} minutes`;
        }
        else if (totalEffortMinutes < 480) {
            return `~${(totalEffortMinutes / 60).toFixed(1)} hours`;
        }
        else {
            return `~${(totalEffortMinutes / 480).toFixed(1)} days`;
        }
    }
    /**
     * 寻找共同模式
     */
    findCommonPatterns(issues) {
        const patterns = [];
        // 共同关键词
        const allMessages = issues.map(i => i.message.toLowerCase()).join(' ');
        const words = allMessages.split(/\s+/).filter(word => word.length > 3);
        const wordCounts = new Map();
        words.forEach(word => {
            wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
        });
        // 找出出现频率高的词
        const commonWords = Array.from(wordCounts.entries())
            .filter(([_, count]) => count >= Math.min(3, Math.ceil(issues.length / 2)))
            .map(([word]) => word);
        if (commonWords.length > 0) {
            patterns.push(commonWords.join(', '));
        }
        return patterns;
    }
}
exports.FixSuggestionGenerator = FixSuggestionGenerator;
//# sourceMappingURL=suggestions.js.map