{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../src/core/parser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0CAAmE;AACnE,6CAA+B;AAC/B,2CAA6B;AAC7B,4CAAyC;AA8BzC,MAAa,UAAU;IAAvB;QACU,wBAAmB,GAAG,IAAI,GAAG,CAAC;YACpC,CAAC,KAAK,EAAE,YAAY,CAAC;YACrB,CAAC,MAAM,EAAE,YAAY,CAAC;YACtB,CAAC,KAAK,EAAE,YAAY,CAAC;YACrB,CAAC,MAAM,EAAE,YAAY,CAAC;YACtB,CAAC,MAAM,EAAE,KAAK,CAAC;YACf,CAAC,MAAM,EAAE,KAAK,CAAC;YACf,CAAC,OAAO,EAAE,KAAK,CAAC;YAChB,CAAC,OAAO,EAAE,KAAK,CAAC;YAChB,CAAC,OAAO,EAAE,KAAK,CAAC;SACjB,CAAC,CAAC;IAsWL,CAAC;IApWC;;OAEG;IACI,KAAK,CAAC,SAAS,CAAC,QAAgB;QACrC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAEnD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,eAAM,CAAC,KAAK,CAAC,0BAA0B,QAAQ,EAAE,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACrD,eAAM,CAAC,KAAK,CAAC,iBAAiB,QAAQ,KAAK,QAAQ,GAAG,CAAC,CAAC;YAExD,QAAQ,QAAQ,EAAE,CAAC;gBACjB,KAAK,YAAY,CAAC;gBAClB,KAAK,YAAY;oBACf,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAuC,CAAC,CAAC;gBAC1G,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAChD,KAAK,KAAK;oBACR,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAChD;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;YAC3D,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB,CACrC,QAAgB,EAChB,OAAe,EACf,QAAqC;QAErC,IAAI,GAAQ,CAAC;QAEb,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,aAAa,GAAkB;gBACnC,UAAU,EAAE,QAAQ;gBACpB,2BAA2B,EAAE,IAAI;gBACjC,0BAA0B,EAAE,IAAI;gBAChC,OAAO,EAAE;oBACP,KAAK;oBACL,YAAY,EAAE,gBAAgB;oBAC9B,iBAAiB;oBACjB,QAAQ;oBACR,iBAAiB;oBACjB,mBAAmB;oBACnB,eAAe;oBACf,eAAe;oBACf,mBAAmB;oBACnB,qBAAqB;oBACrB,cAAc;oBACd,cAAc;oBACd,YAAY;oBACZ,2BAA2B;oBAC3B,kBAAkB;oBAClB,kBAAkB;oBAClB,sBAAsB;oBACtB,kBAAkB;oBAClB,kBAAkB;oBAClB,eAAe;iBAChB;aACF,CAAC;YAEF,GAAG,GAAG,IAAA,cAAU,EAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAEzC,MAAM,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;YACnD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;YAE7C,OAAO;gBACL,QAAQ;gBACR,GAAG;gBACH,OAAO;gBACP,QAAQ;gBACR,YAAY;gBACZ,OAAO;gBACP,SAAS;gBACT,SAAS;aACV,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,mBAAmB,QAAQ,SAAS,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;YAEtE,gBAAgB;YAChB,OAAO;gBACL,QAAQ;gBACR,GAAG,EAAE,IAAI;gBACT,OAAO;gBACP,QAAQ;gBACR,YAAY,EAAE,EAAE;gBAChB,OAAO,EAAE,EAAE;gBACX,SAAS,EAAE,EAAE;gBACb,SAAS,EAAE,EAAE;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,OAAe;QACtD,eAAe;QACf,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvE,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC7E,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAEpE,IAAI,YAAY,GAAa,EAAE,CAAC;QAChC,IAAI,OAAO,GAAa,EAAE,CAAC;QAC3B,IAAI,SAAS,GAAmB,EAAE,CAAC;QACnC,IAAI,SAAS,GAAmB,EAAE,CAAC;QAEnC,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACrC,IAAI,CAAC;gBACH,kBAAkB;gBAClB,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAC5E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,yBAAyB,CACvD,QAAQ,EACR,aAAa,EACb,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CACnC,CAAC;gBAEF,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;gBACzC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;gBAC/B,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;gBACnC,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,GAAG,EAAE;gBACH,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC3C,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;gBACjD,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;aACzC;YACD,OAAO;YACP,QAAQ,EAAE,KAAK;YACf,YAAY;YACZ,OAAO;YACP,SAAS;YACT,SAAS;SACV,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,QAAQ,CAAC,QAAgB,EAAE,OAAe;QACtD,aAAa;QACb,MAAM,WAAW,GAAG,gCAAgC,CAAC;QACrD,MAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QAED,OAAO;YACL,QAAQ;YACR,GAAG,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;YAC7B,OAAO;YACP,QAAQ,EAAE,KAAK;YACf,YAAY;YACZ,OAAO,EAAE,EAAE;YACX,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,EAAE;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,GAAQ;QAClC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO,YAAY,CAAC;QAE3C,MAAM,QAAQ,GAAG,CAAC,IAAS,EAAE,EAAE;YAC7B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;gBAAE,OAAO;YAE9C,WAAW;YACX,IAAI,IAAI,CAAC,IAAI,KAAK,mBAAmB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACrD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;YAED,YAAY;YACZ,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB;gBAC9B,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS;gBAC9B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;gBACzB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACzC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC7C,CAAC;YAED,WAAW;YACX,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ;gBACtB,CAAC,IAAI,CAAC,IAAI,KAAK,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,CAAC,EAAE,CAAC;gBACtE,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBAChF,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;YACH,CAAC;YAED,WAAW;YACX,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACnE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;wBAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK;IAC1C,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ;QAC7B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO,OAAO,CAAC;QAEtC,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;gBAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;wBACpD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;4BAClD,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;gCAC5B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;4BAC7B,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;wBAC3D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;gBAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;wBACpC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;4BACxC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;wBACnC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,0BAA0B,EAAE,CAAC;gBACpD,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,GAAQ;QAC/B,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO,SAAS,CAAC;QAExC,MAAM,QAAQ,GAAG,CAAC,IAAS,EAAE,EAAE;YAC7B,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;gBAAE,OAAO;YAE9C,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB;gBACnC,IAAI,CAAC,IAAI,KAAK,oBAAoB;gBAClC,IAAI,CAAC,IAAI,KAAK,yBAAyB,EAAE,CAAC;gBAE5C,MAAM,QAAQ,GAAiB;oBAC7B,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;oBAC1C,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACxC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC5C,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;oBACxE,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK;oBAC5B,OAAO,EAAE,IAAI,CAAC,IAAI,KAAK,yBAAyB;iBACjD,CAAC;gBAEF,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3B,CAAC;YAED,OAAO;YACP,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACnE,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;wBAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC9B,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,GAAQ;QAC/B,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI;YAAE,OAAO,SAAS,CAAC;QAExC,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,qBAAqB;gBAE/C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;oBACtC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;wBAC5B,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;4BAClB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BACxC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BAC5C,IAAI,EAAE,IAAI,CAAC,IAA+B;4BAC1C,UAAU;yBACX,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,QAAgB;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;CACF;AAjXD,gCAiXC"}