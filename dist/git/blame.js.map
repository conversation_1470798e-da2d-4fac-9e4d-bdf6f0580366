{"version": 3, "file": "blame.js", "sourceRoot": "", "sources": ["../../src/git/blame.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAAkD;AAClD,4CAAyC;AAWzC,MAAa,QAAQ;IAGnB,YAAY,UAAmB;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAA,oBAAS,EAAC,UAAU,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,UAAkB;QAC5D,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBACrC,OAAO;gBACP,kBAAkB;gBAClB,IAAI;gBACJ,GAAG,UAAU,IAAI,UAAU,EAAE;gBAC7B,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,IAAI,UAAU,MAAM,KAAK,EAAE,CAAC,CAAC;YAClF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,SAAiB,EAAE,OAAe;QACjF,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBACrC,OAAO;gBACP,kBAAkB;gBAClB,IAAI;gBACJ,GAAG,SAAS,IAAI,OAAO,EAAE;gBACzB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAEhE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,IAAI,SAAS,IAAI,OAAO,MAAM,KAAK,EAAE,CAAC,CAAC;YAC5F,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBACrC,OAAO;gBACP,kBAAkB;gBAClB,QAAQ;aACT,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAExD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,QAAQ,MAAM,KAAK,EAAE,CAAC,CAAC;YACpE,OAAO,IAAI,GAAG,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gBACnE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aACrE,CAAC,CAAC;YAEH,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,EAAE,CAAC,CAAC;YAC3D,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,iBAAiB;QAC5B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;YACjE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;YAC1D,OAAO,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAmB,EAAE,UAAkB;QAC9D,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAEpC,wBAAwB;QACxB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACnD,IAAI,CAAC,WAAW;YAAE,OAAO,IAAI,CAAC;QAE9B,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;QAClC,IAAI,MAAM,GAAG,SAAS,CAAC;QACvB,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC1C,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC/B,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAClD,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/C,UAAU,GAAG,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO;YACL,MAAM;YACN,KAAK;YACL,UAAU;YACV,UAAU;YACV,IAAI,EAAE,UAAU;YAChB,OAAO;SACR,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,WAAmB,EAAE,SAAiB;QACtE,MAAM,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAC;QAC5C,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE7C,IAAI,iBAAiB,GAAG,SAAS,CAAC;QAClC,IAAI,aAAa,GAAuB,EAAE,CAAC;QAE3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,YAAY;YACZ,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACpE,IAAI,WAAW,EAAE,CAAC;gBAChB,aAAa,GAAG;oBACd,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;oBAC1B,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;iBAC/B,CAAC;gBACF,iBAAiB,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,SAAS;YACX,CAAC;YAED,aAAa;YACb,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC/B,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;gBACtD,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;YAC9B,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC/C,aAAa,CAAC,UAAU,GAAG,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;YACtE,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,WAAW;gBACX,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAE1C,eAAe;gBACf,IAAI,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;oBACrD,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE;wBAC5B,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,SAAS;wBACzC,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,EAAE;wBAChC,UAAU,EAAE,aAAa,CAAC,UAAU;wBACpC,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;wBAChE,IAAI,EAAE,iBAAiB;wBACvB,OAAO,EAAE,aAAa,CAAC,OAAO;qBAC/B,CAAC,CAAC;gBACL,CAAC;gBAED,iBAAiB,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,sBAAsB,CAAC,KAAe;QACjD,MAAM,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAC;QAC5C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE7C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE;gBACf,MAAM,EAAE,QAAQ,CAAC,IAAI;gBACrB,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,UAAU,EAAE,QAAQ;gBACpB,UAAU,EAAE,WAAW;gBACvB,IAAI;gBACJ,OAAO,EAAE,EAAE,CAAC,aAAa;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CAAC,QAAgB;QACzC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAAC,QAAgB;QAO/C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAChC,KAAK;gBACL,IAAI;gBACJ,4BAA4B;gBAC5B,IAAI;gBACJ,QAAQ;aACT,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO;oBACL,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;oBACpB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;oBAChB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACf,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;oBACpB,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;iBAClB,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;YAChE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF;AA1QD,4BA0QC"}