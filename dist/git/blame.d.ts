export interface BlameInfo {
    author: string;
    email: string;
    commitHash: string;
    commitTime: string;
    line: number;
    content: string;
}
export declare class GitBlame {
    private git;
    constructor(workingDir?: string);
    /**
     * 获取文件指定行的blame信息
     */
    getLineBlame(filePath: string, lineNumber: number): Promise<BlameInfo | null>;
    /**
     * 获取文件多行的blame信息
     */
    getMultiLineBlame(filePath: string, startLine: number, endLine: number): Promise<Map<number, BlameInfo>>;
    /**
     * 获取整个文件的blame信息
     */
    getFileBlame(filePath: string): Promise<Map<number, BlameInfo>>;
    /**
     * 获取新增代码的作者信息（对于新增的行，使用当前用户信息）
     */
    getCurrentUserInfo(): Promise<{
        name: string;
        email: string;
    }>;
    /**
     * 获取最后一次提交的时间
     */
    getLastCommitTime(): Promise<string>;
    /**
     * 解析单行blame结果
     */
    private parseBlameResult;
    /**
     * 解析多行blame结果
     */
    private parseMultiLineBlameResult;
    /**
     * 为新增的代码行创建blame信息
     */
    createBlameForNewLines(lines: number[]): Promise<Map<number, BlameInfo>>;
    /**
     * 检查文件是否在Git跟踪中
     */
    isFileTracked(filePath: string): Promise<boolean>;
    /**
     * 获取文件的最后修改信息
     */
    getFileLastModified(filePath: string): Promise<{
        author: string;
        email: string;
        commitHash: string;
        commitTime: string;
        message: string;
    } | null>;
}
//# sourceMappingURL=blame.d.ts.map