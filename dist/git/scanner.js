"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitScanner = void 0;
const simple_git_1 = __importDefault(require("simple-git"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs-extra"));
const logger_1 = require("../utils/logger");
class GitScanner {
    constructor(workingDir) {
        this.supportedExtensions = new Set([
            '.js', '.jsx', '.ts', '.tsx',
            '.vue', '.svelte',
            '.css', '.scss', '.sass', '.less',
            '.html', '.htm', '.json'
        ]);
        this.git = (0, simple_git_1.default)(workingDir || process.cwd());
    }
    /**
     * 获取Git暂存区的变更文件
     */
    async getStagedFiles() {
        try {
            logger_1.logger.debug('Scanning staged files...');
            // 获取暂存区状态
            const status = await this.git.status();
            if (status.staged.length === 0) {
                logger_1.logger.warn('No staged files found. Please stage files using "git add" before running analysis.');
                return [];
            }
            const changes = [];
            for (const file of status.staged) {
                // 检查文件类型
                if (!this.isSupportedFile(file)) {
                    logger_1.logger.debug(`Skipping unsupported file: ${file}`);
                    continue;
                }
                // 检查文件是否存在（排除删除的文件）
                const filePath = path.resolve(file);
                const exists = await fs.pathExists(filePath);
                if (!exists && !status.deleted.includes(file)) {
                    logger_1.logger.debug(`File does not exist: ${file}`);
                    continue;
                }
                const fileChange = {
                    file,
                    status: this.getFileStatus(file, status),
                    changes: []
                };
                // 获取文件的具体变更
                if (exists) {
                    fileChange.changes = await this.getFileChanges(file);
                }
                changes.push(fileChange);
                logger_1.logger.debug(`Added file for analysis: ${file} (${fileChange.status})`);
            }
            logger_1.logger.info(`Found ${changes.length} staged files for analysis`);
            return changes;
        }
        catch (error) {
            logger_1.logger.error(`Failed to scan staged files: ${error}`);
            throw error;
        }
    }
    /**
     * 获取所有修改的文件（包括暂存和未暂存）
     */
    async getAllModifiedFiles() {
        try {
            logger_1.logger.debug('Scanning all modified files...');
            const status = await this.git.status();
            const changes = [];
            // 收集所有变更文件（暂存区 + 工作区）
            const allFiles = new Set([
                ...status.staged,
                ...status.modified,
                ...status.not_added
            ]);
            if (allFiles.size === 0) {
                logger_1.logger.warn('No modified files found.');
                return [];
            }
            for (const file of allFiles) {
                // 检查文件类型
                if (!this.isSupportedFile(file)) {
                    logger_1.logger.debug(`Skipping unsupported file: ${file}`);
                    continue;
                }
                // 检查文件是否存在（排除删除的文件）
                const filePath = path.resolve(file);
                const exists = await fs.pathExists(filePath);
                if (!exists && !status.deleted.includes(file)) {
                    logger_1.logger.debug(`File does not exist: ${file}`);
                    continue;
                }
                const fileChange = {
                    file,
                    status: this.getFileStatus(file, status),
                    changes: []
                };
                // 获取文件的具体变更
                if (exists) {
                    fileChange.changes = await this.getFileChanges(file);
                }
                changes.push(fileChange);
                logger_1.logger.debug(`Added file for analysis: ${file} (${fileChange.status})`);
            }
            logger_1.logger.info(`Found ${changes.length} modified files for analysis`);
            return changes;
        }
        catch (error) {
            logger_1.logger.error(`Failed to scan modified files: ${error}`);
            throw error;
        }
    }
    async getUnstagedFiles() {
        try {
            logger_1.logger.debug('Scanning unstaged files...');
            const status = await this.git.status();
            const changes = [];
            // 合并修改和新增的文件
            const modifiedFiles = [...status.modified, ...status.not_added];
            for (const file of modifiedFiles) {
                if (!this.isSupportedFile(file)) {
                    logger_1.logger.debug(`Skipping unsupported file: ${file}`);
                    continue;
                }
                const filePath = path.resolve(file);
                if (!await fs.pathExists(filePath)) {
                    continue;
                }
                const fileChange = {
                    file,
                    status: status.not_added.includes(file) ? 'added' : 'modified',
                    changes: await this.getFileChanges(file, false) // false = unstaged
                };
                changes.push(fileChange);
            }
            logger_1.logger.info(`Found ${changes.length} unstaged files`);
            return changes;
        }
        catch (error) {
            logger_1.logger.error(`Failed to scan unstaged files: ${error}`);
            throw error;
        }
    }
    /**
     * 获取所有变更文件（暂存区 + 工作区）
     */
    async getAllChangedFiles() {
        const [stagedFiles, unstagedFiles] = await Promise.all([
            this.getStagedFiles(),
            this.getUnstagedFiles()
        ]);
        // 合并结果，暂存区优先
        const fileMap = new Map();
        // 先添加暂存区文件
        stagedFiles.forEach(file => {
            fileMap.set(file.file, file);
        });
        // 添加工作区文件（如果不在暂存区中）
        unstagedFiles.forEach(file => {
            if (!fileMap.has(file.file)) {
                fileMap.set(file.file, file);
            }
        });
        return Array.from(fileMap.values());
    }
    /**
     * 获取文件的具体变更内容
     */
    async getFileChanges(file, staged = true) {
        try {
            // 获取diff信息
            const diffOptions = staged ? ['--cached'] : [];
            const diff = await this.git.diff([...diffOptions, file]);
            return this.parseDiffResult(diff);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get changes for file ${file}: ${error}`);
            return [];
        }
    }
    /**
     * 解析diff结果
     */
    parseDiffResult(diffContent) {
        const changes = [];
        const lines = diffContent.split('\n');
        let currentLine = 0;
        let inHunk = false;
        for (const line of lines) {
            // 跳过文件头信息
            if (line.startsWith('diff --git') ||
                line.startsWith('index ') ||
                line.startsWith('---') ||
                line.startsWith('+++')) {
                continue;
            }
            // 解析hunk头 (@@行)
            const hunkMatch = line.match(/^@@ -(\d+),?\d* \+(\d+),?\d* @@/);
            if (hunkMatch) {
                currentLine = parseInt(hunkMatch[2]) - 1; // 新文件的起始行号
                inHunk = true;
                continue;
            }
            if (!inHunk)
                continue;
            // 解析变更行
            if (line.startsWith('+') && !line.startsWith('+++')) {
                currentLine++;
                changes.push({
                    line: currentLine,
                    type: 'added',
                    content: line.substring(1) // 移除+号
                });
            }
            else if (line.startsWith('-') && !line.startsWith('---')) {
                changes.push({
                    line: currentLine,
                    type: 'removed',
                    content: line.substring(1) // 移除-号
                });
            }
            else if (line.startsWith(' ')) {
                currentLine++;
                // 上下文行，不是变更但需要计算行号
            }
        }
        return changes;
    }
    /**
     * 检查文件是否为支持的类型
     */
    isSupportedFile(filePath) {
        const ext = path.extname(filePath).toLowerCase();
        return this.supportedExtensions.has(ext);
    }
    /**
     * 获取文件的变更状态
     */
    getFileStatus(file, status) {
        if (status.created.includes(file))
            return 'added';
        if (status.deleted.includes(file))
            return 'deleted';
        return 'modified';
    }
    /**
     * 检查当前目录是否为Git仓库
     */
    async isGitRepository() {
        try {
            await this.git.status();
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * 获取当前分支信息
     */
    async getCurrentBranch() {
        try {
            const status = await this.git.status();
            return status.current || 'unknown';
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get current branch: ${error}`);
            return 'unknown';
        }
    }
    /**
     * 获取仓库根目录
     */
    async getRepositoryRoot() {
        try {
            const result = await this.git.revparse(['--show-toplevel']);
            return result.trim();
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get repository root: ${error}`);
            return process.cwd();
        }
    }
    /**
     * 获取文件的完整内容（用于上下文分析）
     */
    async getFileContent(filePath) {
        try {
            const fullPath = path.resolve(filePath);
            return await fs.readFile(fullPath, 'utf-8');
        }
        catch (error) {
            logger_1.logger.debug(`Failed to read file content: ${filePath}`);
            return '';
        }
    }
}
exports.GitScanner = GitScanner;
//# sourceMappingURL=scanner.js.map