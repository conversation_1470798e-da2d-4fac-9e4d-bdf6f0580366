import { GitFileChange } from '../types';
export declare class GitScanner {
    private git;
    private supportedExtensions;
    constructor(workingDir?: string);
    /**
     * 获取Git暂存区的变更文件
     */
    getStagedFiles(): Promise<GitFileChange[]>;
    /**
     * 获取所有修改的文件（包括暂存和未暂存）
     */
    getAllModifiedFiles(): Promise<GitFileChange[]>;
    getUnstagedFiles(): Promise<GitFileChange[]>;
    /**
     * 获取所有变更文件（暂存区 + 工作区）
     */
    getAllChangedFiles(): Promise<GitFileChange[]>;
    /**
     * 获取文件的具体变更内容
     */
    private getFileChanges;
    /**
     * 解析diff结果
     */
    private parseDiffResult;
    /**
     * 检查文件是否为支持的类型
     */
    private isSupportedFile;
    /**
     * 获取文件的变更状态
     */
    private getFileStatus;
    /**
     * 检查当前目录是否为Git仓库
     */
    isGitRepository(): Promise<boolean>;
    /**
     * 获取当前分支信息
     */
    getCurrentBranch(): Promise<string>;
    /**
     * 获取仓库根目录
     */
    getRepositoryRoot(): Promise<string>;
    /**
     * 获取文件的完整内容（用于上下文分析）
     */
    getFileContent(filePath: string): Promise<string>;
}
//# sourceMappingURL=scanner.d.ts.map