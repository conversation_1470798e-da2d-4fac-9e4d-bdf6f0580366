"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitBlame = void 0;
const simple_git_1 = __importDefault(require("simple-git"));
const logger_1 = require("../utils/logger");
class GitBlame {
    constructor(workingDir) {
        this.git = (0, simple_git_1.default)(workingDir || process.cwd());
    }
    /**
     * 获取文件指定行的blame信息
     */
    async getLineBlame(filePath, lineNumber) {
        try {
            const blameResult = await this.git.raw([
                'blame',
                '--line-porcelain',
                '-L',
                `${lineNumber},${lineNumber}`,
                filePath
            ]);
            return this.parseBlameResult(blameResult, lineNumber);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get blame info for ${filePath}:${lineNumber} - ${error}`);
            return null;
        }
    }
    /**
     * 获取文件多行的blame信息
     */
    async getMultiLineBlame(filePath, startLine, endLine) {
        try {
            const blameResult = await this.git.raw([
                'blame',
                '--line-porcelain',
                '-L',
                `${startLine},${endLine}`,
                filePath
            ]);
            return this.parseMultiLineBlameResult(blameResult, startLine);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get blame info for ${filePath}:${startLine}-${endLine} - ${error}`);
            return new Map();
        }
    }
    /**
     * 获取整个文件的blame信息
     */
    async getFileBlame(filePath) {
        try {
            const blameResult = await this.git.raw([
                'blame',
                '--line-porcelain',
                filePath
            ]);
            return this.parseMultiLineBlameResult(blameResult, 1);
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get blame info for ${filePath} - ${error}`);
            return new Map();
        }
    }
    /**
     * 获取新增代码的作者信息（对于新增的行，使用当前用户信息）
     */
    async getCurrentUserInfo() {
        try {
            const [name, email] = await Promise.all([
                this.git.raw(['config', 'user.name']).then(result => result.trim()),
                this.git.raw(['config', 'user.email']).then(result => result.trim())
            ]);
            return { name, email };
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get current user info - ${error}`);
            return { name: 'Unknown', email: '<EMAIL>' };
        }
    }
    /**
     * 获取最后一次提交的时间
     */
    async getLastCommitTime() {
        try {
            const result = await this.git.raw(['log', '-1', '--format=%cI']);
            return result.trim();
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get last commit time - ${error}`);
            return new Date().toISOString();
        }
    }
    /**
     * 解析单行blame结果
     */
    parseBlameResult(blameOutput, lineNumber) {
        const lines = blameOutput.trim().split('\n');
        if (lines.length === 0)
            return null;
        // 解析porcelain格式的blame输出
        const commitMatch = lines[0].match(/^([a-f0-9]+)/);
        if (!commitMatch)
            return null;
        const commitHash = commitMatch[1];
        let author = 'Unknown';
        let email = '';
        let commitTime = new Date().toISOString();
        let content = '';
        for (const line of lines) {
            if (line.startsWith('author ')) {
                author = line.substring(7);
            }
            else if (line.startsWith('author-mail ')) {
                email = line.substring(12).replace(/[<>]/g, '');
            }
            else if (line.startsWith('author-time ')) {
                const timestamp = parseInt(line.substring(12));
                commitTime = new Date(timestamp * 1000).toISOString();
            }
            else if (line.startsWith('\t')) {
                content = line.substring(1);
            }
        }
        return {
            author,
            email,
            commitHash,
            commitTime,
            line: lineNumber,
            content
        };
    }
    /**
     * 解析多行blame结果
     */
    parseMultiLineBlameResult(blameOutput, startLine) {
        const result = new Map();
        const lines = blameOutput.trim().split('\n');
        let currentLineNumber = startLine;
        let currentCommit = {};
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            // 新的commit行
            const commitMatch = line.match(/^([a-f0-9]+) (\d+) (\d+)( (\d+))?/);
            if (commitMatch) {
                currentCommit = {
                    commitHash: commitMatch[1],
                    line: parseInt(commitMatch[2])
                };
                currentLineNumber = parseInt(commitMatch[2]);
                continue;
            }
            // 解析commit信息
            if (line.startsWith('author ')) {
                currentCommit.author = line.substring(7);
            }
            else if (line.startsWith('author-mail ')) {
                const email = line.substring(12).replace(/[<>]/g, '');
                currentCommit.email = email;
            }
            else if (line.startsWith('author-time ')) {
                const timestamp = parseInt(line.substring(12));
                currentCommit.commitTime = new Date(timestamp * 1000).toISOString();
            }
            else if (line.startsWith('\t')) {
                // 这是实际的代码行
                currentCommit.content = line.substring(1);
                // 保存完整的blame信息
                if (currentCommit.commitHash && currentCommit.author) {
                    result.set(currentLineNumber, {
                        author: currentCommit.author || 'Unknown',
                        email: currentCommit.email || '',
                        commitHash: currentCommit.commitHash,
                        commitTime: currentCommit.commitTime || new Date().toISOString(),
                        line: currentLineNumber,
                        content: currentCommit.content
                    });
                }
                currentLineNumber++;
            }
        }
        return result;
    }
    /**
     * 为新增的代码行创建blame信息
     */
    async createBlameForNewLines(lines) {
        const result = new Map();
        const userInfo = await this.getCurrentUserInfo();
        const currentTime = new Date().toISOString();
        for (const line of lines) {
            result.set(line, {
                author: userInfo.name,
                email: userInfo.email,
                commitHash: 'staged',
                commitTime: currentTime,
                line,
                content: '' // 将在上层填充实际内容
            });
        }
        return result;
    }
    /**
     * 检查文件是否在Git跟踪中
     */
    async isFileTracked(filePath) {
        try {
            await this.git.raw(['ls-files', '--error-unmatch', filePath]);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * 获取文件的最后修改信息
     */
    async getFileLastModified(filePath) {
        try {
            const result = await this.git.raw([
                'log',
                '-1',
                '--format=%H|%an|%ae|%cI|%s',
                '--',
                filePath
            ]);
            const parts = result.trim().split('|');
            if (parts.length >= 5) {
                return {
                    commitHash: parts[0],
                    author: parts[1],
                    email: parts[2],
                    commitTime: parts[3],
                    message: parts[4]
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.debug(`Failed to get file last modified info: ${error}`);
            return null;
        }
    }
}
exports.GitBlame = GitBlame;
//# sourceMappingURL=blame.js.map