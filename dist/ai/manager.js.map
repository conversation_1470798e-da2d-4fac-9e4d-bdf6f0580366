{"version": 3, "file": "manager.js", "sourceRoot": "", "sources": ["../../src/ai/manager.ts"], "names": [], "mappings": ";;;AACA,+CAA2F;AAC3F,+CAAoD;AACpD,6CAAkD;AAClD,4CAAyC;AAWzC,MAAa,SAAS;IAOpB,YAAY,MAAsB;QAN1B,cAAS,GAAqB,IAAI,GAAG,EAAE,CAAC;QACxC,UAAK,GAAiC,IAAI,GAAG,EAAE,CAAC;QAGhD,mBAAc,GAAyD,IAAI,GAAG,EAAE,CAAC;QAGvF,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAsB;QAChD,IAAI,CAAC;YACH,eAAe;YACf,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;gBAChD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,cAAe,CAAC;gBAC5D,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;gBACtE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChE,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9C,CAAC;YAED,eAAe;YACf,IAAI,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC;gBACnD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAkB,CAAC;gBAC/D,MAAM,KAAK,GAAG,MAAM,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B,CAAC;gBACzF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChE,eAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;YAC9C,CAAC;YAED,aAAa;YACb,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC;YAC9E,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,qBAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;YACtE,eAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAE7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,KAAK,EAAE,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;gBACvB,aAAa,EAAE,CAAC;gBAChB,kBAAkB,EAAE,CAAC;gBACrB,cAAc,EAAE,CAAC;gBACjB,eAAe,EAAE,CAAC;gBAClB,mBAAmB,EAAE,CAAC;gBACtB,QAAQ,EAAE,IAAI;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,OAA0B;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,SAAS,GAAiB,IAAI,CAAC;QAEnC,YAAY;QACZ,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;gBACtE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;gBACxF,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,GAAG,KAAc,CAAC;YAC3B,eAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,eAAe,YAAY,KAAK,EAAE,CAAC,CAAC;YACzE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAC5D,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAClD,CAAC;QAED,YAAY;QACZ,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAChD,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,gBAAgB,EAAE,CAAC,CAAC;gBAC7D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;gBAClE,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,WAAW,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;gBACpF,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAc,CAAC;gBAC3B,eAAM,CAAC,IAAI,CAAC,qBAAqB,gBAAgB,YAAY,KAAK,EAAE,CAAC,CAAC;gBACtE,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;gBACxD,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,MAAM,IAAI,KAAK,CAAC,wCAAwC,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,YAAoB,EAAE,OAA0B;QACzE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,YAAY,YAAY,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,YAAoB;QAC9C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,UAAU;QACV,MAAM,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAI,YAAY,IAAI,YAAY,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;YAC/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC7E,SAAS;YACT,IAAI,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,QAAQ;gBACR,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,YAAoB;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;QAClG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE/C,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,CAAC;YAC1B,eAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,2BAA2B,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,WAAW,CACjB,YAAoB,EACpB,SAAiB,EACjB,UAAkB,EAClB,OAAgB;QAEhB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE5C,KAAK,CAAC,aAAa,EAAE,CAAC;QACtB,KAAK,CAAC,eAAe,IAAI,UAAU,CAAC;QACpC,KAAK,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;QAE5B,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,CAAC,kBAAkB,EAAE,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,cAAc,EAAE,CAAC;QACzB,CAAC;QAED,WAAW;QACX,KAAK,CAAC,mBAAmB;YACvB,CAAC,KAAK,CAAC,mBAAmB,GAAG,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;gBAC3F,KAAK,CAAC,kBAAkB,CAAC;QAE3B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,eAAuB;QACpD,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;QACnD,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,eAAe,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAmB,CAAC;QAE3C,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,cAAc,EAAE,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAE/B,IAAI,WAAW,EAAE,CAAC;oBAChB,eAAM,CAAC,IAAI,CAAC,KAAK,IAAI,iCAAiC,CAAC,CAAC;gBAC1D,CAAC;qBAAM,CAAC;oBACN,eAAM,CAAC,IAAI,CAAC,KAAK,IAAI,6BAA6B,CAAC,CAAC;gBACtD,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBACzB,eAAM,CAAC,IAAI,CAAC,KAAK,IAAI,+BAA+B,KAAK,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,gBAAgB;QACrB,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC;QACxC,IAAI,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAElE,KAAK,MAAM,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC;gBAAE,SAAS;YAEtD,MAAM,KAAK,GAAG,IAAI,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC;YACxD,IAAI,KAAK,GAAG,SAAS,EAAE,CAAC;gBACtB,YAAY,GAAG,YAAY,CAAC;gBAC5B,SAAS,GAAG,KAAK,CAAC;YACpB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,YAAoB;QACjD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,aAAa,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,CAAC,CAAC;QACX,CAAC;QAED,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,aAAa,CAAC;QACnE,MAAM,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU;QAExF,OAAO,WAAW,GAAG,mBAAmB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,MAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;QAE3C,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;oBAC3D,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,4BAA4B,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;gBAC1D,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA0B,EAAE,YAAqB;QACzE,MAAM,cAAc,GAAG,YAAY,IAAI,IAAI,CAAC,eAAe,CAAC;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,CAAC,OAAO;QACtB,CAAC;QAED,IAAI,CAAC;YACH,OAAO,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,cAAc,KAAK,KAAK,EAAE,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,cAAc,CAAC,WAAmB;QACvC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,eAAM,CAAC,KAAK,CAAC,YAAY,WAAW,gBAAgB,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3C,eAAM,CAAC,KAAK,CAAC,YAAY,WAAW,2BAA2B,CAAC,CAAC;YACjE,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAElE,eAAM,CAAC,IAAI,CAAC,6BAA6B,WAAW,OAAO,WAAW,EAAE,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACI,oBAAoB;QACzB,MAAM,KAAK,GAAG,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;QAEpD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACvC,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;YAE3D,KAAK,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,aAAa,KAAK,KAAK,CAAC,kBAAkB,aAAa,KAAK,CAAC,cAAc,UAAU,CAAC,CAAC;YACxH,KAAK,CAAC,IAAI,CAAC,oBAAoB,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACrI,KAAK,CAAC,IAAI,CAAC,oBAAoB,KAAK,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YACzE,KAAK,CAAC,IAAI,CAAC,mBAAmB,KAAK,CAAC,eAAe,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;YACxE,KAAK,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1F,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,UAAU;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,eAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;IAC9C,CAAC;CACF;AApWD,8BAoWC"}