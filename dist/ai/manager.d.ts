import { AIReviewConfig } from '../types';
import { AIAnalysisRequest, AIAnalysisResponse } from './providers/openai';
export interface AIProviderStats {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalTokensUsed: number;
    averageResponseTime: number;
    lastUsed: Date | null;
}
export declare class AIManager {
    private providers;
    private stats;
    private currentProvider;
    private fallbackProviders;
    private circuitBreaker;
    constructor(config: AIReviewConfig);
    /**
     * 初始化AI提供商
     */
    private initializeProviders;
    /**
     * 初始化统计信息
     */
    private initializeStats;
    /**
     * 分析代码
     */
    analyzeCode(request: AIAnalysisRequest): Promise<AIAnalysisResponse>;
    /**
     * 调用指定的AI提供商
     */
    private callProvider;
    /**
     * 检查提供商是否可用
     */
    private isProviderAvailable;
    /**
     * 更新熔断器状态
     */
    private updateCircuitBreaker;
    /**
     * 更新统计信息
     */
    private updateStats;
    /**
     * 确定后备提供商顺序
     */
    private determineFallbackOrder;
    /**
     * 测试所有提供商连接
     */
    testAllConnections(): Promise<Map<string, boolean>>;
    /**
     * 获取提供商统计信息
     */
    getProviderStats(): Map<string, AIProviderStats>;
    /**
     * 获取当前最佳提供商
     */
    getBestProvider(): string;
    /**
     * 计算提供商评分
     */
    private calculateProviderScore;
    /**
     * 获取所有可用模型
     */
    getAvailableModels(): Promise<Map<string, string[]>>;
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request: AIAnalysisRequest, providerName?: string): number;
    /**
     * 切换主要提供商
     */
    switchProvider(newProvider: string): boolean;
    /**
     * 生成提供商状态报告
     */
    generateStatusReport(): string;
    /**
     * 重置统计信息
     */
    resetStats(): void;
}
//# sourceMappingURL=manager.d.ts.map