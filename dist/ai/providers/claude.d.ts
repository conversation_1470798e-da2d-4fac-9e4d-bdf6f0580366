import { AIAnalysisRequest, AIAnalysisResponse } from './openai';
export declare class ClaudeProvider {
    private apiKey;
    private model;
    private maxRetries;
    private retryDelay;
    private endpoint;
    constructor(apiKey: string, model?: string, maxRetries?: number, retryDelay?: number);
    /**
     * 分析代码并返回问题列表
     */
    analyzeCode(request: AIAnalysisRequest): Promise<AIAnalysisResponse>;
    /**
     * 调用Claude API
     */
    private callClaudeAPI;
    /**
     * 构建分析提示词
     */
    private buildAnalysisPrompt;
    /**
     * 获取系统提示词
     */
    private getSystemPrompt;
    /**
     * 解析AI分析结果
     */
    private parseAnalysisResult;
    /**
     * 验证问题类型
     */
    private validateIssueType;
    /**
     * 验证严重程度
     */
    private validateSeverity;
    /**
     * 延迟函数
     */
    private delay;
    /**
     * 测试API连接
     */
    testConnection(): Promise<boolean>;
    /**
     * 获取可用模型列表
     */
    getAvailableModels(): Promise<string[]>;
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request: AIAnalysisRequest): number;
}
//# sourceMappingURL=claude.d.ts.map