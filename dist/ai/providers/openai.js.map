{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/openai.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAE5B,+CAA4C;AA8B5C,MAAa,cAAc;IAMzB,YACE,MAAc,EACd,QAAgB,OAAO,EACvB,aAAqB,CAAC,EACtB,aAAqB,IAAI;QAEzB,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM;SACP,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,OAA0B;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEjD,eAAM,CAAC,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAExE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;oBACzD,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC;yBAC7C;wBACD;4BACE,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,MAAM;yBAChB;qBACF;oBACD,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,IAAI;oBAChB,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;iBACzC,CAAC,CAAC;gBAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;gBACtD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;gBAChD,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAElE,OAAO;oBACL,MAAM;oBACN,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;wBAC1C,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,iBAAiB;wBAClD,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;qBACzC,CAAC,CAAC,CAAC,SAAS;iBACd,CAAC;YAEJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,YAAY,KAAK,EAAE,CAAC,CAAC;gBAEnE,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;oBAC5C,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,CAAC,UAAU,GAAG,CAAC,cAAc,KAAK,EAAE,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA0B;QACpD,IAAI,MAAM,GAAG,gCAAgC,OAAO,CAAC,QAAQ,iCAAiC,CAAC;QAE/F,SAAS;QACT,MAAM,IAAI,SAAS,OAAO,CAAC,QAAQ,MAAM,CAAC;QAE1C,OAAO;QACP,MAAM,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC1C,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,WAAW,CAAC;QAEtB,UAAU;QACV,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,iBAAiB,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACzE,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAI,mBAAmB,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3E,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,SAAS;QACT,MAAM,IAAI,8CAA8C,CAAC;QACzD,MAAM,IAAI,mBAAmB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC;QAC5D,MAAM,IAAI,2BAA2B,OAAO,CAAC,KAAK,CAAC,WAAW,WAAW,CAAC;QAC1E,MAAM,IAAI,+BAA+B,OAAO,CAAC,KAAK,CAAC,QAAQ,WAAW,CAAC;QAC3E,MAAM,IAAI,wBAAwB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC;QACjE,MAAM,IAAI,+BAA+B,CAAC;QAE1C,MAAM,IAAI,+DAA+D,CAAC;QAC1E,MAAM,IAAI,KAAK,CAAC;QAChB,MAAM,IAAI,mBAAmB,CAAC;QAC9B,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,kEAAkE,CAAC;QAC7E,MAAM,IAAI,qDAAqD,CAAC;QAChE,MAAM,IAAI,2BAA2B,CAAC;QACtC,MAAM,IAAI,wCAAwC,CAAC;QACnD,MAAM,IAAI,6CAA6C,CAAC;QACxD,MAAM,IAAI,+DAA+D,CAAC;QAC1E,MAAM,IAAI,oDAAoD,CAAC;QAC/D,MAAM,IAAI,6DAA6D,CAAC;QACxE,MAAM,IAAI,sEAAsE,CAAC;QACjF,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,OAAO,CAAC;QAClB,MAAM,IAAI,GAAG,CAAC;QAEd,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAiC;QACvD,OAAO;;;;;;;;;;;;;;;;;WAiBA,KAAK,CAAC,KAAK;iBACL,KAAK,CAAC,WAAW;cACpB,KAAK,CAAC,QAAQ;WACjB,KAAK,CAAC,KAAK;;+BAES,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,MAAW,EAAE,QAAgB;QACvD,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpD,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;YAC1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,MAAM,WAAW,GAAU;oBACzB,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC;oBACxC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC;oBAC/C,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC/B,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;oBACzD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,eAAe;oBACzC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,IAAI,yBAAyB;oBAC5E,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,wBAAwB;oBACxD,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;oBACnE,MAAM,EAAE,SAAS,EAAE,mCAAmC;oBACtD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,oCAAoC;iBAC1E,CAAC;gBAEF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,MAAM,oBAAoB,QAAQ,EAAE,CAAC,CAAC;QACvF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,UAAU,GAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC9F,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAqB,CAAC,CAAC,CAAC,CAAC,IAAqB,CAAC,CAAC,CAAC,OAAO,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,eAAe,GAAwB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,eAAe,CAAC,QAAQ,CAAC,QAA6B,CAAC,CAAC,CAAC,CAAC,QAA6B,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5G,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,4CAA4C;qBACtD;iBACF;gBACD,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5E,OAAO,OAAO,KAAK,IAAI,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAC/C,OAAO,MAAM,CAAC,IAAI;iBACf,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;iBACzC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC;iBACtB,IAAI,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA0B;QAClD,6BAA6B;QAC7B,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACrF,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjF,MAAM,uBAAuB,GAAG,IAAI,CAAC,CAAC,cAAc;QAEpD,OAAO,kBAAkB,GAAG,gBAAgB,GAAG,uBAAuB,CAAC;IACzE,CAAC;CACF;AApRD,wCAoRC"}