"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalProvider = void 0;
const logger_1 = require("../../utils/logger");
class LocalProvider {
    constructor(endpoint = 'http://localhost:11434', model = 'codellama', maxRetries = 2, retryDelay = 1000, timeout = 60000) {
        this.endpoint = endpoint;
        this.model = model;
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
        this.timeout = timeout;
    }
    /**
     * 分析代码并返回问题列表
     */
    async analyzeCode(request) {
        const prompt = this.buildAnalysisPrompt(request);
        logger_1.logger.debug(`Analyzing ${request.filePath} with local model ${this.model}`);
        for (let attempt = 1; attempt <= this.maxRetries + 1; attempt++) {
            try {
                const response = await this.callLocalModel(prompt);
                const issues = this.parseAnalysisResult(response, request.filePath);
                return {
                    issues,
                    model: this.model,
                    usage: {
                        promptTokens: Math.ceil(prompt.length / 4),
                        completionTokens: Math.ceil(response.length / 4),
                        totalTokens: Math.ceil((prompt.length + response.length) / 4)
                    }
                };
            }
            catch (error) {
                logger_1.logger.warn(`Local model analysis attempt ${attempt} failed: ${error}`);
                if (attempt <= this.maxRetries) {
                    await this.delay(this.retryDelay * attempt);
                    continue;
                }
                throw new Error(`Local model analysis failed after ${this.maxRetries + 1} attempts: ${error}`);
            }
        }
        throw new Error('Unexpected error in local model analysis');
    }
    /**
     * 调用本地模型API（支持Ollama格式）
     */
    async callLocalModel(prompt) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        try {
            const response = await fetch(`${this.endpoint}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: this.model,
                    prompt: prompt,
                    stream: false,
                    options: {
                        temperature: 0.1,
                        top_p: 0.9,
                        max_tokens: 4000
                    }
                }),
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`Local model API error: ${response.status} ${response.statusText}`);
            }
            const result = await response.json();
            if (!result.response) {
                throw new Error('Empty response from local model');
            }
            return result.response;
        }
        catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof Error && error.name === 'AbortError') {
                throw new Error(`Request timeout after ${this.timeout}ms`);
            }
            throw error;
        }
    }
    /**
     * 构建分析提示词
     */
    buildAnalysisPrompt(request) {
        let prompt = `You are an expert code reviewer. Analyze the following ${request.language} code and identify potential issues.\n\n`;
        // 添加文件信息
        prompt += `File: ${request.filePath}\n\n`;
        // 添加代码
        prompt += `Code:\n\`\`\`${request.language}\n`;
        prompt += request.code;
        prompt += '\n```\n\n';
        // 添加上下文
        if (request.context) {
            if (request.context.dependencies.length > 0) {
                prompt += `Dependencies: ${request.context.dependencies.join(', ')}\n`;
            }
            if (request.context.changedLines && request.context.changedLines.length > 0) {
                prompt += `Focus on lines: ${request.context.changedLines.join(', ')}\n`;
            }
            prompt += '\n';
        }
        // 添加分析要求
        prompt += 'Find issues in these categories:\n';
        prompt += `1. Logic errors (${request.rules.logic} level)\n`;
        prompt += `2. Performance problems (${request.rules.performance} level)\n`;
        prompt += `3. Security vulnerabilities (${request.rules.security} level)\n`;
        prompt += `4. Code style issues (${request.rules.style} level)\n`;
        prompt += '5. Business logic problems\n\n';
        prompt += 'Strictness levels:\n';
        prompt += '- strict: Find all issues including minor ones\n';
        prompt += '- normal: Focus on significant issues\n';
        prompt += '- loose: Only critical issues\n\n';
        // 输出格式
        prompt += 'Return ONLY a JSON object in this exact format:\n';
        prompt += '{\n';
        prompt += '  "issues": [\n';
        prompt += '    {\n';
        prompt += '      "type": "Logic|Performance|Security|Style|Business",\n';
        prompt += '      "severity": "Critical|High|Medium|Low",\n';
        prompt += '      "line": number,\n';
        prompt += '      "message": "Brief description",\n';
        prompt += '      "description": "Detailed explanation",\n';
        prompt += '      "suggestion": "How to fix"\n';
        prompt += '    }\n';
        prompt += '  ]\n';
        prompt += '}\n\n';
        prompt += 'Be precise with line numbers. Only report genuine issues.';
        return prompt;
    }
    /**
     * 解析本地模型分析结果
     */
    parseAnalysisResult(response, filePath) {
        const issues = [];
        try {
            // 尝试提取JSON
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                logger_1.logger.warn('No JSON found in local model response');
                return this.createFallbackIssues(response);
            }
            const result = JSON.parse(jsonMatch[0]);
            if (!result.issues || !Array.isArray(result.issues)) {
                logger_1.logger.warn('Invalid analysis result format from local model');
                return this.createFallbackIssues(response);
            }
            for (const issue of result.issues) {
                try {
                    const parsedIssue = {
                        type: this.validateIssueType(issue.type),
                        severity: this.validateSeverity(issue.severity),
                        line: parseInt(issue.line) || 1,
                        column: issue.column ? parseInt(issue.column) : undefined,
                        message: issue.message || 'Unknown issue',
                        description: issue.description || issue.message || 'No description provided',
                        suggestion: issue.suggestion || 'No suggestion provided',
                        codeExample: issue.codeExample,
                        references: Array.isArray(issue.references) ? issue.references : [],
                        author: 'Unknown',
                        commitTime: new Date().toISOString()
                    };
                    issues.push(parsedIssue);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to parse issue: ${error}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse local model response: ${error}`);
            return this.createFallbackIssues(response);
        }
        logger_1.logger.info(`Local model analysis completed: ${issues.length} issues found in ${filePath}`);
        return issues;
    }
    /**
     * 创建后备问题（当解析失败时）
     */
    createFallbackIssues(response) {
        // 如果无法解析JSON，尝试从文本中提取信息
        const lines = response.split('\n');
        const issues = [];
        // 简单的文本解析逻辑
        for (const line of lines) {
            if (line.toLowerCase().includes('error') ||
                line.toLowerCase().includes('issue') ||
                line.toLowerCase().includes('problem')) {
                issues.push({
                    type: 'Logic',
                    severity: 'Medium',
                    line: 1,
                    message: 'Local model analysis issue',
                    description: line.trim(),
                    suggestion: 'Please review the code manually',
                    author: 'Unknown',
                    commitTime: new Date().toISOString()
                });
            }
        }
        return issues;
    }
    /**
     * 验证问题类型
     */
    validateIssueType(type) {
        const validTypes = ['Logic', 'Performance', 'Security', 'Style', 'Business'];
        return validTypes.includes(type) ? type : 'Logic';
    }
    /**
     * 验证严重程度
     */
    validateSeverity(severity) {
        const validSeverities = ['Critical', 'High', 'Medium', 'Low'];
        return validSeverities.includes(severity) ? severity : 'Medium';
    }
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 测试本地模型连接
     */
    async testConnection() {
        try {
            const response = await fetch(`${this.endpoint}/api/tags`, {
                method: 'GET'
            });
            if (!response.ok) {
                return false;
            }
            const data = await response.json();
            return Array.isArray(data.models) && data.models.length > 0;
        }
        catch (error) {
            logger_1.logger.error(`Local model connection test failed: ${error}`);
            return false;
        }
    }
    /**
     * 获取可用模型列表
     */
    async getAvailableModels() {
        try {
            const response = await fetch(`${this.endpoint}/api/tags`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            const data = await response.json();
            return data.models?.map((model) => model.name) || [this.model];
        }
        catch (error) {
            logger_1.logger.error(`Failed to get available models: ${error}`);
            return [this.model];
        }
    }
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request) {
        const prompt = this.buildAnalysisPrompt(request);
        return Math.ceil((prompt.length + 1000) / 4); // 估算包含响应的token数
    }
    /**
     * 检查模型是否可用
     */
    async isModelAvailable(modelName) {
        try {
            const availableModels = await this.getAvailableModels();
            return availableModels.includes(modelName);
        }
        catch {
            return false;
        }
    }
}
exports.LocalProvider = LocalProvider;
//# sourceMappingURL=local.js.map