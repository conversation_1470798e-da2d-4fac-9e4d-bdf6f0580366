import { AIAnalysisRequest, AIAnalysisResponse } from './openai';
export declare class LocalProvider {
    private endpoint;
    private model;
    private maxRetries;
    private retryDelay;
    private timeout;
    constructor(endpoint?: string, model?: string, maxRetries?: number, retryDelay?: number, timeout?: number);
    /**
     * 分析代码并返回问题列表
     */
    analyzeCode(request: AIAnalysisRequest): Promise<AIAnalysisResponse>;
    /**
     * 调用本地模型API（支持Ollama格式）
     */
    private callLocalModel;
    /**
     * 构建分析提示词
     */
    private buildAnalysisPrompt;
    /**
     * 解析本地模型分析结果
     */
    private parseAnalysisResult;
    /**
     * 创建后备问题（当解析失败时）
     */
    private createFallbackIssues;
    /**
     * 验证问题类型
     */
    private validateIssueType;
    /**
     * 验证严重程度
     */
    private validateSeverity;
    /**
     * 延迟函数
     */
    private delay;
    /**
     * 测试本地模型连接
     */
    testConnection(): Promise<boolean>;
    /**
     * 获取可用模型列表
     */
    getAvailableModels(): Promise<string[]>;
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request: AIAnalysisRequest): number;
    /**
     * 检查模型是否可用
     */
    isModelAvailable(modelName: string): Promise<boolean>;
}
//# sourceMappingURL=local.d.ts.map