{"version": 3, "file": "claude.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/claude.ts"], "names": [], "mappings": ";;;AAEA,+CAA4C;AAE5C,MAAa,cAAc;IAOzB,YACE,MAAc,EACd,QAAgB,0BAA0B,EAC1C,aAAqB,CAAC,EACtB,aAAqB,IAAI;QANnB,aAAQ,GAAW,uCAAuC,CAAC;QAQjE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,OAA0B;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEjD,eAAM,CAAC,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ,gBAAgB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAExE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAE5E,OAAO;oBACL,MAAM;oBACN,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;wBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;wBACzC,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;wBAC9C,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa;qBACxE,CAAC,CAAC,CAAC,SAAS;iBACd,CAAC;YAEJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,YAAY,KAAK,EAAE,CAAC,CAAC;gBAEnE,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;oBAC5C,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,KAAK,CAAC,gCAAgC,IAAI,CAAC,UAAU,GAAG,CAAC,cAAc,KAAK,EAAE,CAAC,CAAC;YAC5F,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,MAAc,EAAE,OAA0B;QACpE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC1C,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,WAAW,EAAE,IAAI,CAAC,MAAM;gBACxB,mBAAmB,EAAE,YAAY;aAClC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,GAAG;gBAChB,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC3C,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;aACF,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YACxC,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA0B;QACpD,IAAI,MAAM,GAAG,uCAAuC,OAAO,CAAC,QAAQ,iCAAiC,CAAC;QAEtG,SAAS;QACT,MAAM,IAAI,aAAa,OAAO,CAAC,QAAQ,MAAM,CAAC;QAE9C,OAAO;QACP,MAAM,IAAI,+BAA+B,OAAO,CAAC,QAAQ,IAAI,CAAC;QAC9D,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,WAAW,CAAC;QAEtB,UAAU;QACV,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,IAAI,4BAA4B,CAAC;YAEvC,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,mBAAmB,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3E,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAI,+BAA+B,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACvF,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,wBAAwB,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC7E,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,SAAS;QACT,MAAM,IAAI,8BAA8B,CAAC;QACzC,MAAM,IAAI,4FAA4F,CAAC;QACvG,MAAM,IAAI,wBAAwB,OAAO,CAAC,KAAK,CAAC,KAAK,qEAAqE,CAAC;QAC3H,MAAM,IAAI,gCAAgC,OAAO,CAAC,KAAK,CAAC,WAAW,yEAAyE,CAAC;QAC7I,MAAM,IAAI,oCAAoC,OAAO,CAAC,KAAK,CAAC,QAAQ,oDAAoD,CAAC;QACzH,MAAM,IAAI,6BAA6B,OAAO,CAAC,KAAK,CAAC,KAAK,qDAAqD,CAAC;QAChH,MAAM,IAAI,0FAA0F,CAAC;QAErG,SAAS;QACT,MAAM,IAAI,0BAA0B,CAAC;QACrC,MAAM,IAAI,+DAA+D,CAAC;QAC1E,MAAM,IAAI,4EAA4E,CAAC;QACvF,MAAM,IAAI,0DAA0D,CAAC;QAErE,SAAS;QACT,MAAM,IAAI,+BAA+B,CAAC;QAC1C,MAAM,IAAI,uEAAuE,CAAC;QAClF,MAAM,IAAI,WAAW,CAAC;QACtB,MAAM,IAAI,KAAK,CAAC;QAChB,MAAM,IAAI,iBAAiB,CAAC;QAC5B,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,8DAA8D,CAAC;QACzE,MAAM,IAAI,iDAAiD,CAAC;QAC5D,MAAM,IAAI,yBAAyB,CAAC;QACpC,MAAM,IAAI,2BAA2B,CAAC;QACtC,MAAM,IAAI,6DAA6D,CAAC;QACxE,MAAM,IAAI,8FAA8F,CAAC;QACzG,MAAM,IAAI,sEAAsE,CAAC;QACjF,MAAM,IAAI,qFAAqF,CAAC;QAChG,MAAM,IAAI,4EAA4E,CAAC;QACvF,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,OAAO,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC;QAChB,MAAM,IAAI,SAAS,CAAC;QAEpB,MAAM,IAAI,yGAAyG,CAAC;QAEpH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,KAAiC;QACvD,OAAO;;;;;;;;;;;;;;;;;WAiBA,KAAK,CAAC,KAAK;iBACL,KAAK,CAAC,WAAW;cACpB,KAAK,CAAC,QAAQ;WACjB,KAAK,CAAC,KAAK;;4CAEsB,CAAC;IAC3C,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAY,EAAE,QAAgB;QACxD,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,IAAI,CAAC;YACH,0BAA0B;YAC1B,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC/E,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAChC,CAAC;iBAAM,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBACvC,WAAW,GAAG,OAAO,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,WAAW;YACX,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;gBAC1D,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAU;wBACzB,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC;wBACxC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC;wBAC/C,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC/B,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;wBACzD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,eAAe;wBACzC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,IAAI,yBAAyB;wBAC5E,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,wBAAwB;wBACxD,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;wBACnE,MAAM,EAAE,SAAS,EAAE,mCAAmC;wBACtD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,oCAAoC;qBAC1E,CAAC;oBAEF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAC3D,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,8BAA8B,MAAM,CAAC,MAAM,oBAAoB,QAAQ,EAAE,CAAC,CAAC;QACvF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,UAAU,GAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC9F,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAqB,CAAC,CAAC,CAAC,CAAC,IAAqB,CAAC,CAAC,CAAC,OAAO,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,eAAe,GAAwB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,eAAe,CAAC,QAAQ,CAAC,QAA6B,CAAC,CAAC,CAAC,CAAC,QAA6B,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5G,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC1C,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,WAAW,EAAE,IAAI,CAAC,MAAM;oBACxB,mBAAmB,EAAE,YAAY;iBAClC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,UAAU,EAAE,EAAE;oBACd,QAAQ,EAAE;wBACR;4BACE,IAAI,EAAE,MAAM;4BACZ,OAAO,EAAE,iDAAiD;yBAC3D;qBACF;iBACF,CAAC;aACH,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;YAE7B,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC/E,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC;YAED,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,kCAAkC;QAClC,OAAO;YACL,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;YACzB,YAAY;YACZ,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA0B;QAClD,mCAAmC;QACnC,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QACvF,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;QACnF,MAAM,uBAAuB,GAAG,IAAI,CAAC,CAAC,cAAc;QAEpD,OAAO,kBAAkB,GAAG,gBAAgB,GAAG,uBAAuB,CAAC;IACzE,CAAC;CACF;AAxVD,wCAwVC"}