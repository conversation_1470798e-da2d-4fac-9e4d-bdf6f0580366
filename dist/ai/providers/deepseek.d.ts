export interface DeepSeekConfig {
    apiKey: string;
    model: string;
    baseUrl?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
}
export interface DeepSeekMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}
export interface DeepSeekResponse {
    choices: Array<{
        message: {
            content: string;
            role: string;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
    model: string;
}
export declare class DeepSeekProvider {
    private client;
    private config;
    constructor(config: DeepSeekConfig);
    /**
     * 发送聊天完成请求
     */
    chatCompletion(messages: DeepSeekMessage[]): Promise<string>;
    /**
     * 分析代码并返回问题
     */
    analyzeCode(code: string, filePath: string, contextPrompt?: string): Promise<string>;
    /**
     * 测试API连接
     */
    testConnection(): Promise<boolean>;
    /**
     * 获取可用模型列表
     */
    getAvailableModels(): string[];
    /**
     * 获取推荐模型配置
     */
    getRecommendedModels(): Record<string, {
        description: string;
        bestFor: string[];
    }>;
    /**
     * 更新配置
     */
    updateConfig(newConfig: Partial<DeepSeekConfig>): void;
    /**
     * 获取当前配置
     */
    getConfig(): DeepSeekConfig;
}
//# sourceMappingURL=deepseek.d.ts.map