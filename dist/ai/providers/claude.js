"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClaudeProvider = void 0;
const logger_1 = require("../../utils/logger");
class ClaudeProvider {
    constructor(apiKey, model = 'claude-3-sonnet-20240229', maxRetries = 2, retryDelay = 1000) {
        this.endpoint = 'https://api.anthropic.com/v1/messages';
        this.apiKey = apiKey;
        this.model = model;
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
    }
    /**
     * 分析代码并返回问题列表
     */
    async analyzeCode(request) {
        const prompt = this.buildAnalysisPrompt(request);
        logger_1.logger.debug(`Analyzing ${request.filePath} with Claude ${this.model}`);
        for (let attempt = 1; attempt <= this.maxRetries + 1; attempt++) {
            try {
                const response = await this.callClaudeAPI(prompt, request);
                const issues = this.parseAnalysisResult(response.content, request.filePath);
                return {
                    issues,
                    model: this.model,
                    usage: response.usage ? {
                        promptTokens: response.usage.input_tokens,
                        completionTokens: response.usage.output_tokens,
                        totalTokens: response.usage.input_tokens + response.usage.output_tokens
                    } : undefined
                };
            }
            catch (error) {
                logger_1.logger.warn(`Claude analysis attempt ${attempt} failed: ${error}`);
                if (attempt <= this.maxRetries) {
                    await this.delay(this.retryDelay * attempt);
                    continue;
                }
                throw new Error(`Claude analysis failed after ${this.maxRetries + 1} attempts: ${error}`);
            }
        }
        throw new Error('Unexpected error in Claude analysis');
    }
    /**
     * 调用Claude API
     */
    async callClaudeAPI(prompt, request) {
        const response = await fetch(this.endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': this.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: this.model,
                max_tokens: 4000,
                temperature: 0.1,
                system: this.getSystemPrompt(request.rules),
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            })
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Claude API error: ${response.status} ${errorText}`);
        }
        return await response.json();
    }
    /**
     * 构建分析提示词
     */
    buildAnalysisPrompt(request) {
        let prompt = `I need you to analyze the following ${request.language} code for potential issues.\n\n`;
        // 添加文件信息
        prompt += `**File:** ${request.filePath}\n\n`;
        // 添加代码
        prompt += `**Code to analyze:**\n\`\`\`${request.language}\n`;
        prompt += request.code;
        prompt += '\n```\n\n';
        // 添加上下文信息
        if (request.context) {
            prompt += '**Context Information:**\n';
            if (request.context.dependencies.length > 0) {
                prompt += `- Dependencies: ${request.context.dependencies.join(', ')}\n`;
            }
            if (request.context.changedLines && request.context.changedLines.length > 0) {
                prompt += `- Focus primarily on lines: ${request.context.changedLines.join(', ')}\n`;
            }
            if (request.context.functions.length > 0) {
                prompt += `- Functions in file: ${request.context.functions.join(', ')}\n`;
            }
            prompt += '\n';
        }
        // 添加分析要求
        prompt += '**Analysis Requirements:**\n';
        prompt += `Please analyze for the following types of issues with the specified strictness levels:\n\n`;
        prompt += `1. **Logic errors** (${request.rules.logic} level): null pointer access, condition errors, loop issues, etc.\n`;
        prompt += `2. **Performance problems** (${request.rules.performance} level): unnecessary re-renders, memory leaks, inefficient algorithms\n`;
        prompt += `3. **Security vulnerabilities** (${request.rules.security} level): XSS risks, CSRF, data validation issues\n`;
        prompt += `4. **Code style issues** (${request.rules.style} level): naming, formatting, complexity, comments\n`;
        prompt += `5. **Business logic problems**: inconsistencies, edge cases, state management issues\n\n`;
        // 严格程度说明
        prompt += '**Strictness Levels:**\n';
        prompt += '- **strict**: Find all possible issues including minor ones\n';
        prompt += '- **normal**: Focus on significant issues that could cause real problems\n';
        prompt += '- **loose**: Only report obvious and critical issues\n\n';
        // 输出格式要求
        prompt += '**Required Output Format:**\n';
        prompt += 'Please return your analysis results in the following JSON format:\n\n';
        prompt += '```json\n';
        prompt += '{\n';
        prompt += '  "issues": [\n';
        prompt += '    {\n';
        prompt += '      "type": "Logic|Performance|Security|Style|Business",\n';
        prompt += '      "severity": "Critical|High|Medium|Low",\n';
        prompt += '      "line": number,\n';
        prompt += '      "column": number,\n';
        prompt += '      "message": "Brief, clear description of the issue",\n';
        prompt += '      "description": "Detailed explanation of why this is a problem and potential impact",\n';
        prompt += '      "suggestion": "Specific, actionable steps to fix the issue",\n';
        prompt += '      "codeExample": "Example of how the code should be written (if applicable)",\n';
        prompt += '      "references": ["URLs to relevant documentation or best practices"]\n';
        prompt += '    }\n';
        prompt += '  ]\n';
        prompt += '}\n';
        prompt += '```\n\n';
        prompt += 'Important: Only report genuine issues. Be precise with line numbers and provide actionable suggestions.';
        return prompt;
    }
    /**
     * 获取系统提示词
     */
    getSystemPrompt(rules) {
        return `You are a senior code reviewer specializing in frontend development with expertise in JavaScript, TypeScript, Vue.js, and CSS. Your role is to analyze code and identify potential issues with high precision and provide actionable feedback.

Key responsibilities:
1. Identify genuine issues that could impact functionality, security, performance, or maintainability
2. Provide precise line numbers and clear descriptions
3. Offer specific, actionable suggestions for improvements
4. Include relevant documentation links when helpful
5. Avoid false positives - only report real issues

Your analysis should consider:
- Modern JavaScript/TypeScript best practices
- Framework-specific patterns and anti-patterns
- Security considerations for web applications
- Performance optimization opportunities
- Code maintainability and readability

Current analysis strictness:
- Logic: ${rules.logic}
- Performance: ${rules.performance}  
- Security: ${rules.security}
- Style: ${rules.style}

Always respond with valid JSON format only.`;
    }
    /**
     * 解析AI分析结果
     */
    parseAnalysisResult(content, filePath) {
        const issues = [];
        try {
            // Claude响应格式中content是一个数组
            let textContent = '';
            if (Array.isArray(content) && content.length > 0 && content[0].type === 'text') {
                textContent = content[0].text;
            }
            else if (typeof content === 'string') {
                textContent = content;
            }
            else {
                throw new Error('Invalid response format');
            }
            // 提取JSON部分
            const jsonMatch = textContent.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in Claude response');
            }
            const result = JSON.parse(jsonMatch[0]);
            if (!result.issues || !Array.isArray(result.issues)) {
                logger_1.logger.warn('Invalid analysis result format from Claude');
                return issues;
            }
            for (const issue of result.issues) {
                try {
                    const parsedIssue = {
                        type: this.validateIssueType(issue.type),
                        severity: this.validateSeverity(issue.severity),
                        line: parseInt(issue.line) || 1,
                        column: issue.column ? parseInt(issue.column) : undefined,
                        message: issue.message || 'Unknown issue',
                        description: issue.description || issue.message || 'No description provided',
                        suggestion: issue.suggestion || 'No suggestion provided',
                        codeExample: issue.codeExample,
                        references: Array.isArray(issue.references) ? issue.references : [],
                        author: 'Unknown', // Will be filled by blame analysis
                        commitTime: new Date().toISOString() // Will be updated by blame analysis
                    };
                    issues.push(parsedIssue);
                }
                catch (error) {
                    logger_1.logger.warn(`Failed to parse issue: ${error}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to parse Claude response: ${error}`);
        }
        logger_1.logger.info(`Claude analysis completed: ${issues.length} issues found in ${filePath}`);
        return issues;
    }
    /**
     * 验证问题类型
     */
    validateIssueType(type) {
        const validTypes = ['Logic', 'Performance', 'Security', 'Style', 'Business'];
        return validTypes.includes(type) ? type : 'Logic';
    }
    /**
     * 验证严重程度
     */
    validateSeverity(severity) {
        const validSeverities = ['Critical', 'High', 'Medium', 'Low'];
        return validSeverities.includes(severity) ? severity : 'Medium';
    }
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-api-key': this.apiKey,
                    'anthropic-version': '2023-06-01'
                },
                body: JSON.stringify({
                    model: this.model,
                    max_tokens: 10,
                    messages: [
                        {
                            role: 'user',
                            content: 'Test connection. Please respond with just "OK".'
                        }
                    ]
                })
            });
            if (!response.ok) {
                return false;
            }
            const data = await response.json();
            const content = data.content;
            if (Array.isArray(content) && content.length > 0 && content[0].type === 'text') {
                return content[0].text.trim().toLowerCase().includes('ok');
            }
            return false;
        }
        catch (error) {
            logger_1.logger.error(`Claude connection test failed: ${error}`);
            return false;
        }
    }
    /**
     * 获取可用模型列表
     */
    async getAvailableModels() {
        // Claude模型列表（手动维护，因为API不提供模型列表端点）
        return [
            'claude-3-opus-20240229',
            'claude-3-sonnet-20240229',
            'claude-3-haiku-20240307',
            'claude-2.1',
            'claude-2.0'
        ];
    }
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request) {
        // Claude的token估算：大约每3.5个字符为1个token
        const systemPromptTokens = Math.ceil(this.getSystemPrompt(request.rules).length / 3.5);
        const userPromptTokens = Math.ceil(this.buildAnalysisPrompt(request).length / 3.5);
        const estimatedResponseTokens = 1200; // 预估响应token数量
        return systemPromptTokens + userPromptTokens + estimatedResponseTokens;
    }
}
exports.ClaudeProvider = ClaudeProvider;
//# sourceMappingURL=claude.js.map