"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const openai_1 = __importDefault(require("openai"));
const logger_1 = require("../../utils/logger");
class OpenAIProvider {
    constructor(apiKey, model = 'gpt-4', maxRetries = 2, retryDelay = 1000) {
        this.client = new openai_1.default({
            apiKey
        });
        this.model = model;
        this.maxRetries = maxRetries;
        this.retryDelay = retryDelay;
    }
    /**
     * 分析代码并返回问题列表
     */
    async analyzeCode(request) {
        const prompt = this.buildAnalysisPrompt(request);
        logger_1.logger.debug(`Analyzing ${request.filePath} with OpenAI ${this.model}`);
        for (let attempt = 1; attempt <= this.maxRetries + 1; attempt++) {
            try {
                const response = await this.client.chat.completions.create({
                    model: this.model,
                    messages: [
                        {
                            role: 'system',
                            content: this.getSystemPrompt(request.rules)
                        },
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    temperature: 0.1,
                    max_tokens: 4000,
                    response_format: { type: 'json_object' }
                });
                const content = response.choices[0]?.message?.content;
                if (!content) {
                    throw new Error('Empty response from OpenAI');
                }
                const result = JSON.parse(content);
                const issues = this.parseAnalysisResult(result, request.filePath);
                return {
                    issues,
                    model: this.model,
                    usage: response.usage ? {
                        promptTokens: response.usage.prompt_tokens,
                        completionTokens: response.usage.completion_tokens,
                        totalTokens: response.usage.total_tokens
                    } : undefined
                };
            }
            catch (error) {
                logger_1.logger.warn(`OpenAI analysis attempt ${attempt} failed: ${error}`);
                if (attempt <= this.maxRetries) {
                    await this.delay(this.retryDelay * attempt);
                    continue;
                }
                throw new Error(`OpenAI analysis failed after ${this.maxRetries + 1} attempts: ${error}`);
            }
        }
        throw new Error('Unexpected error in OpenAI analysis');
    }
    /**
     * 构建分析提示词
     */
    buildAnalysisPrompt(request) {
        let prompt = `Please analyze the following ${request.language} code for potential issues:\n\n`;
        // 添加文件路径
        prompt += `File: ${request.filePath}\n\n`;
        // 添加代码
        prompt += '```' + request.language + '\n';
        prompt += request.code;
        prompt += '\n```\n\n';
        // 添加上下文信息
        if (request.context) {
            if (request.context.dependencies.length > 0) {
                prompt += `Dependencies: ${request.context.dependencies.join(', ')}\n`;
            }
            if (request.context.changedLines && request.context.changedLines.length > 0) {
                prompt += `Focus on lines: ${request.context.changedLines.join(', ')}\n`;
            }
            prompt += '\n';
        }
        // 添加分析要求
        prompt += 'Analyze for the following types of issues:\n';
        prompt += `- Logic errors (${request.rules.logic} level)\n`;
        prompt += `- Performance problems (${request.rules.performance} level)\n`;
        prompt += `- Security vulnerabilities (${request.rules.security} level)\n`;
        prompt += `- Code style issues (${request.rules.style} level)\n`;
        prompt += '- Business logic problems\n\n';
        prompt += 'Return results in JSON format with the following structure:\n';
        prompt += '{\n';
        prompt += '  \"issues\": [\n';
        prompt += '    {\n';
        prompt += '      \"type\": \"Logic|Performance|Security|Style|Business\",\n';
        prompt += '      \"severity\": \"Critical|High|Medium|Low\",\n';
        prompt += '      \"line\": number,\n';
        prompt += '      \"column\": number (optional),\n';
        prompt += '      \"message\": \"Brief description\",\n';
        prompt += '      \"description\": \"Detailed explanation and impact\",\n';
        prompt += '      \"suggestion\": \"How to fix this issue\",\n';
        prompt += '      \"codeExample\": \"Fixed code example (optional)\",\n';
        prompt += '      \"references\": [\"URL to documentation or best practices\"]\n';
        prompt += '    }\n';
        prompt += '  ]\n';
        prompt += '}';
        return prompt;
    }
    /**
     * 获取系统提示词
     */
    getSystemPrompt(rules) {
        return `You are an expert code reviewer specializing in frontend development. 
Your task is to analyze code and identify potential issues with high accuracy.

Analysis strictness levels:
- strict: Find all possible issues including minor ones
- normal: Focus on significant issues that could cause problems
- loose: Only report obvious and critical issues

Guidelines:
1. Be precise with line numbers and descriptions
2. Provide actionable suggestions for fixes
3. Include relevant documentation links when possible
4. Consider the specific language features and best practices
5. Focus on issues that could impact functionality, security, or maintainability
6. Avoid false positives - only report genuine issues

Current strictness settings:
- Logic: ${rules.logic}
- Performance: ${rules.performance}
- Security: ${rules.security}
- Style: ${rules.style}

Return only valid JSON format.`;
    }
    /**
     * 解析AI分析结果
     */
    parseAnalysisResult(result, filePath) {
        const issues = [];
        if (!result.issues || !Array.isArray(result.issues)) {
            logger_1.logger.warn('Invalid analysis result format from OpenAI');
            return issues;
        }
        for (const issue of result.issues) {
            try {
                const parsedIssue = {
                    type: this.validateIssueType(issue.type),
                    severity: this.validateSeverity(issue.severity),
                    line: parseInt(issue.line) || 1,
                    column: issue.column ? parseInt(issue.column) : undefined,
                    message: issue.message || 'Unknown issue',
                    description: issue.description || issue.message || 'No description provided',
                    suggestion: issue.suggestion || 'No suggestion provided',
                    codeExample: issue.codeExample,
                    references: Array.isArray(issue.references) ? issue.references : [],
                    author: 'Unknown', // Will be filled by blame analysis
                    commitTime: new Date().toISOString() // Will be updated by blame analysis
                };
                issues.push(parsedIssue);
            }
            catch (error) {
                logger_1.logger.warn(`Failed to parse issue: ${error}`);
            }
        }
        logger_1.logger.info(`OpenAI analysis completed: ${issues.length} issues found in ${filePath}`);
        return issues;
    }
    /**
     * 验证问题类型
     */
    validateIssueType(type) {
        const validTypes = ['Logic', 'Performance', 'Security', 'Style', 'Business'];
        return validTypes.includes(type) ? type : 'Logic';
    }
    /**
     * 验证严重程度
     */
    validateSeverity(severity) {
        const validSeverities = ['Critical', 'High', 'Medium', 'Low'];
        return validSeverities.includes(severity) ? severity : 'Medium';
    }
    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            const response = await this.client.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'user',
                        content: 'Test connection. Please respond with "OK".'
                    }
                ],
                max_tokens: 10
            });
            const content = response.choices[0]?.message?.content?.trim().toLowerCase();
            return content === 'ok';
        }
        catch (error) {
            logger_1.logger.error(`OpenAI connection test failed: ${error}`);
            return false;
        }
    }
    /**
     * 获取可用模型列表
     */
    async getAvailableModels() {
        try {
            const models = await this.client.models.list();
            return models.data
                .filter(model => model.id.includes('gpt'))
                .map(model => model.id)
                .sort();
        }
        catch (error) {
            logger_1.logger.error(`Failed to get available models: ${error}`);
            return [this.model];
        }
    }
    /**
     * 估算token使用量
     */
    estimateTokenUsage(request) {
        // 简单的token估算：大约每4个字符为1个token
        const systemPromptTokens = Math.ceil(this.getSystemPrompt(request.rules).length / 4);
        const userPromptTokens = Math.ceil(this.buildAnalysisPrompt(request).length / 4);
        const estimatedResponseTokens = 1000; // 预估响应token数量
        return systemPromptTokens + userPromptTokens + estimatedResponseTokens;
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=openai.js.map