"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepSeekProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const logger_1 = require("../../utils/logger");
class DeepSeekProvider {
    constructor(config) {
        this.config = {
            baseUrl: 'https://api.deepseek.com/v1',
            maxTokens: 2048,
            temperature: 0.1,
            timeout: 30000,
            ...config
        };
        this.client = axios_1.default.create({
            baseURL: this.config.baseUrl,
            timeout: this.config.timeout,
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json',
                'User-Agent': 'AI-Code-Review/1.0'
            }
        });
        logger_1.logger.debug(`DeepSeek provider initialized with model: ${this.config.model}`);
    }
    /**
     * 发送聊天完成请求
     */
    async chatCompletion(messages) {
        try {
            const requestData = {
                model: this.config.model,
                messages,
                max_tokens: this.config.maxTokens,
                temperature: this.config.temperature,
                stream: false
            };
            logger_1.logger.debug(`DeepSeek API request: ${JSON.stringify({ model: requestData.model, messageCount: messages.length })}`);
            const response = await this.client.post('/chat/completions', requestData);
            if (!response.data?.choices?.[0]?.message?.content) {
                throw new Error('Invalid response format from DeepSeek API');
            }
            const content = response.data.choices[0].message.content;
            logger_1.logger.debug(`DeepSeek API response: ${response.data.usage?.total_tokens || 0} tokens used`);
            return content;
        }
        catch (error) {
            logger_1.logger.error(`DeepSeek API error: ${error}`);
            if (axios_1.default.isAxiosError(error)) {
                const status = error.response?.status;
                const message = error.response?.data?.error?.message || error.message;
                switch (status) {
                    case 401:
                        throw new Error('Invalid API key for DeepSeek');
                    case 403:
                        throw new Error('Access forbidden - check your DeepSeek API permissions');
                    case 429:
                        throw new Error('Rate limit exceeded for DeepSeek API');
                    case 500:
                    case 502:
                    case 503:
                        throw new Error('DeepSeek API server error - please try again later');
                    default:
                        throw new Error(`DeepSeek API error: ${message}`);
                }
            }
            throw error;
        }
    }
    /**
     * 分析代码并返回问题
     */
    async analyzeCode(code, filePath, contextPrompt) {
        const systemPrompt = `You are an expert code reviewer. Analyze the provided code for:
1. Logic errors and bugs
2. Performance issues
3. Security vulnerabilities
4. Code quality and best practices
5. Business logic problems

Return your analysis in JSON format with an array of issues, each containing:
- type: "Logic" | "Performance" | "Security" | "Style" | "Business"
- severity: "Critical" | "High" | "Medium" | "Low"
- line: number (if applicable)
- message: brief description
- description: detailed explanation
- suggestion: how to fix the issue

Focus on actionable, specific feedback that helps improve code quality.`;
        const userPrompt = `File: ${filePath}

${contextPrompt ? `Context: ${contextPrompt}\n\n` : ''}Code to analyze:
\`\`\`
${code}
\`\`\`

Please analyze this code and provide feedback in JSON format.`;
        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];
        return await this.chatCompletion(messages);
    }
    /**
     * 测试API连接
     */
    async testConnection() {
        try {
            const testMessages = [
                { role: 'user', content: 'Hello, please respond with "Connected" to test the connection.' }
            ];
            const response = await this.chatCompletion(testMessages);
            logger_1.logger.info('DeepSeek connection test successful');
            return response.toLowerCase().includes('connected');
        }
        catch (error) {
            logger_1.logger.error(`DeepSeek connection test failed: ${error}`);
            return false;
        }
    }
    /**
     * 获取可用模型列表
     */
    getAvailableModels() {
        return [
            'deepseek-coder',
            'deepseek-chat',
            'deepseek-code-6b-instruct',
            'deepseek-code-33b-instruct',
            'deepseek-coder-6.7b-instruct',
            'deepseek-coder-33b-instruct'
        ];
    }
    /**
     * 获取推荐模型配置
     */
    getRecommendedModels() {
        return {
            'deepseek-coder': {
                description: 'Latest DeepSeek Coder model, best overall performance',
                bestFor: ['code-review', 'bug-detection', 'optimization']
            },
            'deepseek-chat': {
                description: 'General chat model, good for explanations',
                bestFor: ['code-explanation', 'documentation']
            },
            'deepseek-coder-33b-instruct': {
                description: 'Large model for complex code analysis',
                bestFor: ['complex-logic', 'architecture-review']
            },
            'deepseek-coder-6.7b-instruct': {
                description: 'Smaller, faster model for quick reviews',
                bestFor: ['quick-checks', 'style-review']
            }
        };
    }
    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        // 更新客户端配置
        this.client.defaults.baseURL = this.config.baseUrl;
        this.client.defaults.timeout = this.config.timeout;
        this.client.defaults.headers['Authorization'] = `Bearer ${this.config.apiKey}`;
        logger_1.logger.debug('DeepSeek provider configuration updated');
    }
    /**
     * 获取当前配置
     */
    getConfig() {
        return { ...this.config };
    }
}
exports.DeepSeekProvider = DeepSeekProvider;
//# sourceMappingURL=deepseek.js.map