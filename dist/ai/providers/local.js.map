{"version": 3, "file": "local.js", "sourceRoot": "", "sources": ["../../../src/ai/providers/local.ts"], "names": [], "mappings": ";;;AAEA,+CAA4C;AAE5C,MAAa,aAAa;IAOxB,YACE,WAAmB,wBAAwB,EAC3C,QAAgB,WAAW,EAC3B,aAAqB,CAAC,EACtB,aAAqB,IAAI,EACzB,UAAkB,KAAK;QAEvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,OAA0B;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAEjD,eAAM,CAAC,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ,qBAAqB,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;QAE7E,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;YAChE,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;gBACnD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEpE,OAAO;oBACL,MAAM;oBACN,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,KAAK,EAAE;wBACL,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;wBAC1C,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;wBAChD,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;qBAC9D;iBACF,CAAC;YAEJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,gCAAgC,OAAO,YAAY,KAAK,EAAE,CAAC,CAAC;gBAExE,IAAI,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC;oBAC5C,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,UAAU,GAAG,CAAC,cAAc,KAAK,EAAE,CAAC,CAAC;YACjG,CAAC;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,MAAc;QACzC,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAErE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,eAAe,EAAE;gBAC5D,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,MAAM,EAAE,MAAM;oBACd,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE;wBACP,WAAW,EAAE,GAAG;wBAChB,KAAK,EAAE,GAAG;wBACV,UAAU,EAAE,IAAI;qBACjB;iBACF,CAAC;gBACF,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B,CAAC,CAAC;YAEH,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,0BAA0B,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;YACtF,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAE5C,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;YAED,OAAO,MAAM,CAAC,QAAQ,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC1D,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;YAC7D,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAA0B;QACpD,IAAI,MAAM,GAAG,0DAA0D,OAAO,CAAC,QAAQ,0CAA0C,CAAC;QAElI,SAAS;QACT,MAAM,IAAI,SAAS,OAAO,CAAC,QAAQ,MAAM,CAAC;QAE1C,OAAO;QACP,MAAM,IAAI,gBAAgB,OAAO,CAAC,QAAQ,IAAI,CAAC;QAC/C,MAAM,IAAI,OAAO,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,WAAW,CAAC;QAEtB,QAAQ;QACR,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5C,MAAM,IAAI,iBAAiB,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACzE,CAAC;YAED,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5E,MAAM,IAAI,mBAAmB,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAC3E,CAAC;YAED,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,SAAS;QACT,MAAM,IAAI,oCAAoC,CAAC;QAC/C,MAAM,IAAI,oBAAoB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC;QAC7D,MAAM,IAAI,4BAA4B,OAAO,CAAC,KAAK,CAAC,WAAW,WAAW,CAAC;QAC3E,MAAM,IAAI,gCAAgC,OAAO,CAAC,KAAK,CAAC,QAAQ,WAAW,CAAC;QAC5E,MAAM,IAAI,yBAAyB,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC;QAClE,MAAM,IAAI,gCAAgC,CAAC;QAE3C,MAAM,IAAI,sBAAsB,CAAC;QACjC,MAAM,IAAI,kDAAkD,CAAC;QAC7D,MAAM,IAAI,yCAAyC,CAAC;QACpD,MAAM,IAAI,mCAAmC,CAAC;QAE9C,OAAO;QACP,MAAM,IAAI,mDAAmD,CAAC;QAC9D,MAAM,IAAI,KAAK,CAAC;QAChB,MAAM,IAAI,iBAAiB,CAAC;QAC5B,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,8DAA8D,CAAC;QACzE,MAAM,IAAI,iDAAiD,CAAC;QAC5D,MAAM,IAAI,yBAAyB,CAAC;QACpC,MAAM,IAAI,yCAAyC,CAAC;QACpD,MAAM,IAAI,gDAAgD,CAAC;QAC3D,MAAM,IAAI,oCAAoC,CAAC;QAC/C,MAAM,IAAI,SAAS,CAAC;QACpB,MAAM,IAAI,OAAO,CAAC;QAClB,MAAM,IAAI,OAAO,CAAC;QAElB,MAAM,IAAI,2DAA2D,CAAC;QAEtE,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAgB,EAAE,QAAgB;QAC5D,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,IAAI,CAAC;YACH,WAAW;YACX,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,eAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACrD,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,eAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC;YAED,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC;oBACH,MAAM,WAAW,GAAU;wBACzB,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC;wBACxC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC;wBAC/C,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;wBAC/B,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;wBACzD,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,eAAe;wBACzC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,OAAO,IAAI,yBAAyB;wBAC5E,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,wBAAwB;wBACxD,WAAW,EAAE,KAAK,CAAC,WAAW;wBAC9B,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;wBACnE,MAAM,EAAE,SAAS;wBACjB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACrC,CAAC;oBAEF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC3B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,mCAAmC,MAAM,CAAC,MAAM,oBAAoB,QAAQ,EAAE,CAAC,CAAC;QAC5F,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAgB;QAC3C,wBAAwB;QACxB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACnC,MAAM,MAAM,GAAY,EAAE,CAAC;QAE3B,YAAY;QACZ,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACpC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACpC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAE3C,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,CAAC;oBACP,OAAO,EAAE,4BAA4B;oBACrC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE;oBACxB,UAAU,EAAE,iCAAiC;oBAC7C,MAAM,EAAE,SAAS;oBACjB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACrC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY;QACpC,MAAM,UAAU,GAAoB,CAAC,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAC9F,OAAO,UAAU,CAAC,QAAQ,CAAC,IAAqB,CAAC,CAAC,CAAC,CAAC,IAAqB,CAAC,CAAC,CAAC,OAAO,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAgB;QACvC,MAAM,eAAe,GAAwB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,eAAe,CAAC,QAAQ,CAAC,QAA6B,CAAC,CAAC,CAAC,CAAC,QAA6B,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC5G,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,WAAW,EAAE;gBACxD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;QAE9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,WAAW,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7C,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEtE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACzD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA0B;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,gBAAgB;IAChE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,SAAiB;QAC7C,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxD,OAAO,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AA/UD,sCA+UC"}