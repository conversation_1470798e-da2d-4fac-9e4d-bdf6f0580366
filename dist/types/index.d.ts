export interface AIReviewConfig {
    aiProvider: 'openai' | 'claude' | 'deepseek' | 'local';
    model: string;
    apiKey?: string;
    baseUrl?: string;
    ignore: string[];
    rules: {
        logic: 'strict' | 'normal' | 'loose';
        performance: 'strict' | 'normal' | 'loose';
        security: 'strict' | 'normal' | 'loose';
        style: 'strict' | 'normal' | 'loose';
        business: 'strict' | 'normal' | 'loose';
    };
    output: {
        format: 'terminal' | 'json' | 'html';
        detailed: boolean;
        verbose?: boolean | undefined;
    };
    cache: {
        enabled: boolean;
        ttl: number;
    };
}
export interface AnalysisResult {
    file: string;
    issues: Issue[];
    statistics: {
        critical: number;
        high: number;
        medium: number;
        low: number;
    };
    metadata: {
        analyzedAt: string;
        aiModel: string;
        fromCache: boolean;
    };
}
export interface Issue {
    type: 'Logic' | 'Performance' | 'Security' | 'Style' | 'Business';
    severity: 'Critical' | 'High' | 'Medium' | 'Low';
    line: number;
    column?: number | undefined;
    message: string;
    description: string;
    suggestion: string;
    codeExample?: string;
    references?: string[];
    author: string;
    commitTime: string;
}
export interface CLIOptions {
    mode?: 'quick' | 'full';
    output?: 'terminal' | 'json' | 'html';
    config?: string;
    verbose?: boolean;
    cache?: boolean;
    all?: boolean;
    staged?: boolean;
}
export interface GitFileChange {
    file: string;
    status: 'added' | 'modified' | 'deleted';
    changes: GitChange[];
}
export interface GitChange {
    line: number;
    type: 'added' | 'removed' | 'modified';
    content: string;
}
export interface CacheEntry {
    fileHash: string;
    lastModified: string;
    results: Issue[];
}
export interface ErrorRecovery {
    aiServiceFallback: 'cache' | 'static' | 'skip';
    maxRetries: number;
    retryDelay: number;
    timeoutMs: number;
}
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export interface ProgressOptions {
    total: number;
    current: number;
    message: string;
}
//# sourceMappingURL=index.d.ts.map