"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = exports.ErrorHandler = exports.AIReviewError = exports.ErrorCode = void 0;
exports.createConfigError = createConfigError;
exports.createGitError = createGitError;
exports.createAIServiceError = createAIServiceError;
exports.createFileError = createFileError;
const logger_1 = require("./logger");
var ErrorCode;
(function (ErrorCode) {
    // Configuration errors
    ErrorCode["CONFIG_INVALID"] = "CONFIG_INVALID";
    ErrorCode["CONFIG_NOT_FOUND"] = "CONFIG_NOT_FOUND";
    // Git errors  
    ErrorCode["GIT_NOT_REPO"] = "GIT_NOT_REPO";
    ErrorCode["GIT_NO_CHANGES"] = "GIT_NO_CHANGES";
    ErrorCode["GIT_OPERATION_FAILED"] = "GIT_OPERATION_FAILED";
    // File system errors
    ErrorCode["FILE_NOT_FOUND"] = "FILE_NOT_FOUND";
    ErrorCode["FILE_READ_ERROR"] = "FILE_READ_ERROR";
    ErrorCode["FILE_WRITE_ERROR"] = "FILE_WRITE_ERROR";
    // AI service errors
    ErrorCode["AI_API_ERROR"] = "AI_API_ERROR";
    ErrorCode["AI_QUOTA_EXCEEDED"] = "AI_QUOTA_EXCEEDED";
    ErrorCode["AI_TIMEOUT"] = "AI_TIMEOUT";
    ErrorCode["AI_INVALID_RESPONSE"] = "AI_INVALID_RESPONSE";
    // Cache errors
    ErrorCode["CACHE_READ_ERROR"] = "CACHE_READ_ERROR";
    ErrorCode["CACHE_WRITE_ERROR"] = "CACHE_WRITE_ERROR";
    // Analysis errors
    ErrorCode["PARSE_ERROR"] = "PARSE_ERROR";
    ErrorCode["ANALYSIS_FAILED"] = "ANALYSIS_FAILED";
    // Report generation errors
    ErrorCode["REPORT_GENERATION_FAILED"] = "REPORT_GENERATION_FAILED";
    // Generic errors
    ErrorCode["UNKNOWN_ERROR"] = "UNKNOWN_ERROR";
    ErrorCode["NETWORK_ERROR"] = "NETWORK_ERROR";
})(ErrorCode || (exports.ErrorCode = ErrorCode = {}));
class AIReviewError extends Error {
    constructor(code, message, userMessage, recoverable = false, context = {}) {
        super(message);
        this.name = 'AIReviewError';
        this.code = code;
        this.recoverable = recoverable;
        this.userMessage = userMessage || this.getDefaultUserMessage(code);
        this.context = {
            timestamp: new Date().toISOString(),
            stackTrace: this.stack,
            ...context
        };
        // Ensure the stack trace shows the correct error class
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, AIReviewError);
        }
    }
    getDefaultUserMessage(code) {
        const messages = {
            [ErrorCode.CONFIG_INVALID]: 'Configuration file is invalid. Please check your .ai-review.json file.',
            [ErrorCode.CONFIG_NOT_FOUND]: 'Configuration file not found. Run with --config to specify a path or create .ai-review.json.',
            [ErrorCode.GIT_NOT_REPO]: 'Not a git repository. Please run this tool in a git project.',
            [ErrorCode.GIT_NO_CHANGES]: 'No staged changes found. Please stage your changes with git add first.',
            [ErrorCode.GIT_OPERATION_FAILED]: 'Git operation failed. Please check your repository status.',
            [ErrorCode.FILE_NOT_FOUND]: 'File not found. The file may have been deleted or moved.',
            [ErrorCode.FILE_READ_ERROR]: 'Failed to read file. Please check file permissions.',
            [ErrorCode.FILE_WRITE_ERROR]: 'Failed to write file. Please check file permissions and disk space.',
            [ErrorCode.AI_API_ERROR]: 'AI service is temporarily unavailable. Please try again later.',
            [ErrorCode.AI_QUOTA_EXCEEDED]: 'AI service quota exceeded. Please check your API limits.',
            [ErrorCode.AI_TIMEOUT]: 'AI service request timed out. Please try again.',
            [ErrorCode.AI_INVALID_RESPONSE]: 'Received invalid response from AI service.',
            [ErrorCode.CACHE_READ_ERROR]: 'Failed to read from cache. Cache will be disabled.',
            [ErrorCode.CACHE_WRITE_ERROR]: 'Failed to write to cache. Cache will be disabled.',
            [ErrorCode.PARSE_ERROR]: 'Failed to parse code file. The file may contain syntax errors.',
            [ErrorCode.ANALYSIS_FAILED]: 'Code analysis failed. Please check the file and try again.',
            [ErrorCode.REPORT_GENERATION_FAILED]: 'Failed to generate report. Please check output permissions.',
            [ErrorCode.NETWORK_ERROR]: 'Network connection failed. Please check your internet connection.',
            [ErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.'
        };
        return messages[code] || messages[ErrorCode.UNKNOWN_ERROR];
    }
    toJSON() {
        return {
            name: this.name,
            code: this.code,
            message: this.message,
            userMessage: this.userMessage,
            recoverable: this.recoverable,
            context: this.context
        };
    }
}
exports.AIReviewError = AIReviewError;
class ErrorHandler {
    constructor() {
        this.retryOptions = {
            maxRetries: 2,
            baseDelay: 1000,
            maxDelay: 10000,
            backoffFactor: 2,
            retryableErrors: [
                ErrorCode.AI_API_ERROR,
                ErrorCode.AI_TIMEOUT,
                ErrorCode.NETWORK_ERROR,
                ErrorCode.CACHE_READ_ERROR
            ]
        };
    }
    static getInstance() {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }
    /**
     * Handle error with appropriate logging and user feedback
     */
    handleError(error) {
        if (error instanceof AIReviewError) {
            this.handleAIReviewError(error);
        }
        else {
            this.handleGenericError(error);
        }
    }
    /**
     * Execute operation with automatic retry logic
     */
    async withRetry(operation, context, customRetryOptions) {
        const options = { ...this.retryOptions, ...customRetryOptions };
        let lastError;
        for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
            try {
                if (attempt > 0) {
                    const delay = Math.min(options.baseDelay * Math.pow(options.backoffFactor, attempt - 1), options.maxDelay);
                    logger_1.logger.info(`Retrying ${context} (attempt ${attempt + 1}/${options.maxRetries + 1}) after ${delay}ms`);
                    await this.sleep(delay);
                }
                return await operation();
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt === options.maxRetries) {
                    break;
                }
                if (error instanceof AIReviewError) {
                    if (!options.retryableErrors?.includes(error.code)) {
                        logger_1.logger.warn(`${context} failed with non-retryable error: ${error.code}`);
                        break;
                    }
                }
                logger_1.logger.warn(`${context} failed (attempt ${attempt + 1}): ${lastError.message}`);
            }
        }
        // All retries exhausted
        if (!lastError) {
            lastError = new Error(`${context} failed for unknown reason`);
        }
        const finalError = lastError instanceof AIReviewError
            ? lastError
            : new AIReviewError(ErrorCode.UNKNOWN_ERROR, `${context} failed after ${options.maxRetries + 1} attempts: ${lastError.message}`, `Operation failed after multiple attempts. Please try again later.`, false, { operation: context, originalError: lastError.message });
        throw finalError;
    }
    /**
     * Graceful degradation - continue operation with fallback
     */
    async withFallback(primaryOperation, fallbackOperation, context) {
        try {
            return await primaryOperation();
        }
        catch (error) {
            logger_1.logger.warn(`${context} primary operation failed, using fallback: ${error instanceof Error ? error.message : String(error)}`);
            try {
                return await fallbackOperation();
            }
            catch (fallbackError) {
                const combinedError = new AIReviewError(ErrorCode.ANALYSIS_FAILED, `Both primary and fallback operations failed for ${context}`, `Operation failed completely. Please check your configuration and try again.`, false, {
                    operation: context,
                    primaryError: error instanceof Error ? error.message : String(error),
                    fallbackError: fallbackError instanceof Error ? fallbackError.message : String(fallbackError)
                });
                throw combinedError;
            }
        }
    }
    /**
     * Validate operation preconditions and throw descriptive error
     */
    validatePrecondition(condition, errorCode, message, userMessage, context) {
        if (!condition) {
            throw new AIReviewError(errorCode, message, userMessage, false, context);
        }
    }
    /**
     * Create error from HTTP response
     */
    createHttpError(status, statusText, responseBody, context) {
        let code;
        let userMessage;
        if (status === 429) {
            code = ErrorCode.AI_QUOTA_EXCEEDED;
            userMessage = 'AI service quota exceeded. Please wait and try again or check your API limits.';
        }
        else if (status === 408 || status === 504) {
            code = ErrorCode.AI_TIMEOUT;
            userMessage = 'AI service request timed out. Please try again.';
        }
        else if (status >= 500) {
            code = ErrorCode.AI_API_ERROR;
            userMessage = 'AI service is experiencing issues. Please try again later.';
        }
        else if (status >= 400) {
            code = ErrorCode.AI_INVALID_RESPONSE;
            userMessage = 'Invalid request to AI service. Please check your configuration.';
        }
        else {
            code = ErrorCode.NETWORK_ERROR;
            userMessage = 'Network error occurred. Please check your connection.';
        }
        return new AIReviewError(code, `HTTP ${status}: ${statusText}`, userMessage, status >= 500 || status === 408 || status === 429, // Recoverable errors
        {
            ...context,
            details: {
                status,
                statusText,
                responseBody: responseBody ? String(responseBody).substring(0, 1000) : undefined
            }
        });
    }
    /**
     * Update retry options
     */
    updateRetryOptions(options) {
        this.retryOptions = { ...this.retryOptions, ...options };
    }
    handleAIReviewError(error) {
        // Log detailed error for debugging
        logger_1.logger.error(`[${error.code}] ${error.message}`, {
            context: error.context,
            recoverable: error.recoverable
        });
        // Show user-friendly message
        if (error.userMessage) {
            console.error(`\n❌ ${error.userMessage}\n`);
        }
        // Add recovery suggestions for common errors
        this.showRecoverySuggestions(error.code);
    }
    handleGenericError(error) {
        logger_1.logger.error(`Unexpected error: ${error.message}`, { stack: error.stack });
        const aiReviewError = new AIReviewError(ErrorCode.UNKNOWN_ERROR, error.message, 'An unexpected error occurred. Please try again.', true, { originalError: error.name });
        this.handleAIReviewError(aiReviewError);
    }
    showRecoverySuggestions(code) {
        const suggestions = {
            [ErrorCode.CONFIG_INVALID]: [
                'Check your .ai-review.json file syntax',
                'Validate required fields are present',
                'Run with --config to specify a different config file'
            ],
            [ErrorCode.AI_API_ERROR]: [
                'Check your API key is valid',
                'Verify your internet connection',
                'Try again in a few minutes'
            ],
            [ErrorCode.AI_QUOTA_EXCEEDED]: [
                'Check your API usage limits',
                'Wait for quota reset',
                'Consider upgrading your plan'
            ],
            [ErrorCode.GIT_NO_CHANGES]: [
                'Stage your changes with: git add <files>',
                'Check if files are already committed',
                'Use git status to see repository state'
            ]
        };
        const suggestion = suggestions[code];
        if (suggestion) {
            console.log('💡 Suggestions:');
            suggestion.forEach(s => console.log(`   • ${s}`));
            console.log();
        }
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.ErrorHandler = ErrorHandler;
// Convenience functions for common error scenarios
exports.errorHandler = ErrorHandler.getInstance();
function createConfigError(message, file) {
    return new AIReviewError(ErrorCode.CONFIG_INVALID, message, undefined, false, { file });
}
function createGitError(message, operation) {
    return new AIReviewError(ErrorCode.GIT_OPERATION_FAILED, message, undefined, false, { operation });
}
function createAIServiceError(message, provider) {
    return new AIReviewError(ErrorCode.AI_API_ERROR, message, undefined, true, { details: { provider } });
}
function createFileError(message, file, operation) {
    const code = operation === 'read' ? ErrorCode.FILE_READ_ERROR : ErrorCode.FILE_WRITE_ERROR;
    return new AIReviewError(code, message, undefined, false, { file, operation });
}
//# sourceMappingURL=errorHandler.js.map