"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Spinner = exports.ProgressBar = void 0;
const chalk_1 = __importDefault(require("chalk"));
class ProgressBar {
    constructor(options) {
        this.total = options.total;
        this.current = options.current;
        this.message = options.message;
        this.startTime = Date.now();
    }
    update(current, message) {
        this.current = current;
        if (message) {
            this.message = message;
        }
        this.render();
    }
    increment(message) {
        this.update(this.current + 1, message);
    }
    complete(message) {
        this.current = this.total;
        this.render();
        if (message) {
            console.log('\n' + chalk_1.default.green(message));
        }
        else {
            console.log('\n' + chalk_1.default.green('✓ Complete!'));
        }
    }
    render() {
        const percentage = Math.round((this.current / this.total) * 100);
        const barLength = 30;
        const filledLength = Math.round((barLength * this.current) / this.total);
        const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
        const elapsed = (Date.now() - this.startTime) / 1000;
        const eta = this.current > 0 ? ((elapsed * this.total) / this.current - elapsed) : 0;
        const progressLine = [
            chalk_1.default.cyan(`[${bar}]`),
            chalk_1.default.bold(`${percentage}%`),
            chalk_1.default.gray(`(${this.current}/${this.total})`),
            chalk_1.default.yellow(this.message),
            chalk_1.default.gray(`ETA: ${eta.toFixed(1)}s`)
        ].join(' ');
        // Clear current line and write progress
        process.stdout.write('\r' + ' '.repeat(process.stdout.columns || 80));
        process.stdout.write('\r' + progressLine);
    }
}
exports.ProgressBar = ProgressBar;
class Spinner {
    constructor(message = 'Loading...') {
        this.frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
        this.current = 0;
        this.interval = null;
        this.message = message;
    }
    start() {
        this.interval = setInterval(() => {
            process.stdout.write('\r' + chalk_1.default.cyan(this.frames[this.current]) + ' ' + this.message);
            this.current = (this.current + 1) % this.frames.length;
        }, 100);
    }
    stop(successMessage) {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        process.stdout.write('\r' + ' '.repeat(this.message.length + 10));
        if (successMessage) {
            console.log('\r' + chalk_1.default.green('✓ ' + successMessage));
        }
        else {
            process.stdout.write('\r');
        }
    }
    updateMessage(message) {
        this.message = message;
    }
}
exports.Spinner = Spinner;
//# sourceMappingURL=progress.js.map