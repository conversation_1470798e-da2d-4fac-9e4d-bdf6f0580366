{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/utils/errorHandler.ts"], "names": [], "mappings": ";;;AAmYA,8CAQC;AAED,wCAQC;AAED,oDAQC;AAED,0CASC;AA1aD,qCAAkC;AAElC,IAAY,SAmCX;AAnCD,WAAY,SAAS;IACnB,uBAAuB;IACvB,8CAAiC,CAAA;IACjC,kDAAqC,CAAA;IAErC,eAAe;IACf,0CAA6B,CAAA;IAC7B,8CAAiC,CAAA;IACjC,0DAA6C,CAAA;IAE7C,qBAAqB;IACrB,8CAAiC,CAAA;IACjC,gDAAmC,CAAA;IACnC,kDAAqC,CAAA;IAErC,oBAAoB;IACpB,0CAA6B,CAAA;IAC7B,oDAAuC,CAAA;IACvC,sCAAyB,CAAA;IACzB,wDAA2C,CAAA;IAE3C,eAAe;IACf,kDAAqC,CAAA;IACrC,oDAAuC,CAAA;IAEvC,kBAAkB;IAClB,wCAA2B,CAAA;IAC3B,gDAAmC,CAAA;IAEnC,2BAA2B;IAC3B,kEAAqD,CAAA;IAErD,iBAAiB;IACjB,4CAA+B,CAAA;IAC/B,4CAA+B,CAAA;AACjC,CAAC,EAnCW,SAAS,yBAAT,SAAS,QAmCpB;AAaD,MAAa,aAAc,SAAQ,KAAK;IAMtC,YACE,IAAe,EACf,OAAe,EACf,WAAoB,EACpB,cAAuB,KAAK,EAC5B,UAAiC,EAAE;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,GAAG;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,UAAU,EAAE,IAAI,CAAC,KAAK;YACtB,GAAG,OAAO;SACX,CAAC;QAEF,uDAAuD;QACvD,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,IAAe;QAC3C,MAAM,QAAQ,GAA8B;YAC1C,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,wEAAwE;YACpG,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,8FAA8F;YAC5H,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,8DAA8D;YACxF,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,wEAAwE;YACpG,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,4DAA4D;YAC9F,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,0DAA0D;YACtF,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,qDAAqD;YAClF,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,qEAAqE;YACnG,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,gEAAgE;YAC1F,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,0DAA0D;YACzF,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,iDAAiD;YACzE,CAAC,SAAS,CAAC,mBAAmB,CAAC,EAAE,4CAA4C;YAC7E,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,oDAAoD;YAClF,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,mDAAmD;YAClF,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,gEAAgE;YACzF,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,4DAA4D;YACzF,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAAE,6DAA6D;YACnG,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,mEAAmE;YAC9F,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,iDAAiD;SAC7E,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;IAC7D,CAAC;IAEM,MAAM;QACX,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;IACJ,CAAC;CACF;AAlED,sCAkEC;AAUD,MAAa,YAAY;IAAzB;QAEU,iBAAY,GAAiB;YACnC,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,KAAK;YACf,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE;gBACf,SAAS,CAAC,YAAY;gBACtB,SAAS,CAAC,UAAU;gBACpB,SAAS,CAAC,aAAa;gBACvB,SAAS,CAAC,gBAAgB;aAC3B;SACF,CAAC;IAmPJ,CAAC;IAjPQ,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;YAC3B,YAAY,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;QAC7C,CAAC;QACD,OAAO,YAAY,CAAC,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAA4B;QAC7C,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;YACnC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,SAAS,CACpB,SAA2B,EAC3B,OAAe,EACf,kBAA0C;QAE1C,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,kBAAkB,EAAE,CAAC;QAChE,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC/D,IAAI,CAAC;gBACH,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;oBAChB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,CAAC,EAChE,OAAO,CAAC,QAAQ,CACjB,CAAC;oBACF,eAAM,CAAC,IAAI,CAAC,YAAY,OAAO,aAAa,OAAO,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,GAAG,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC;oBACvG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC1B,CAAC;gBAED,OAAO,MAAM,SAAS,EAAE,CAAC;YAC3B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEtE,IAAI,OAAO,KAAK,OAAO,CAAC,UAAU,EAAE,CAAC;oBACnC,MAAM;gBACR,CAAC;gBAED,IAAI,KAAK,YAAY,aAAa,EAAE,CAAC;oBACnC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;wBACnD,eAAM,CAAC,IAAI,CAAC,GAAG,OAAO,qCAAqC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;wBACzE,MAAM;oBACR,CAAC;gBACH,CAAC;gBAED,eAAM,CAAC,IAAI,CAAC,GAAG,OAAO,oBAAoB,OAAO,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;YAClF,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,SAAS,GAAG,IAAI,KAAK,CAAC,GAAG,OAAO,4BAA4B,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,YAAY,aAAa;YACnD,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,IAAI,aAAa,CACf,SAAS,CAAC,aAAa,EACvB,GAAG,OAAO,iBAAiB,OAAO,CAAC,UAAU,GAAG,CAAC,cAAc,SAAS,CAAC,OAAO,EAAE,EAClF,mEAAmE,EACnE,KAAK,EACL,EAAE,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,OAAO,EAAE,CACzD,CAAC;QAEN,MAAM,UAAU,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,YAAY,CACvB,gBAAkC,EAClC,iBAAmC,EACnC,OAAe;QAEf,IAAI,CAAC;YACH,OAAO,MAAM,gBAAgB,EAAE,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,GAAG,OAAO,8CAA8C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAE9H,IAAI,CAAC;gBACH,OAAO,MAAM,iBAAiB,EAAE,CAAC;YACnC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC;gBACvB,MAAM,aAAa,GAAG,IAAI,aAAa,CACrC,SAAS,CAAC,eAAe,EACzB,mDAAmD,OAAO,EAAE,EAC5D,6EAA6E,EAC7E,KAAK,EACL;oBACE,SAAS,EAAE,OAAO;oBAClB,YAAY,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBACpE,aAAa,EAAE,aAAa,YAAY,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;iBAC9F,CACF,CAAC;gBACF,MAAM,aAAa,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,oBAAoB,CACzB,SAAkB,EAClB,SAAoB,EACpB,OAAe,EACf,WAAoB,EACpB,OAA+B;QAE/B,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe,CACpB,MAAc,EACd,UAAkB,EAClB,YAAkB,EAClB,OAA+B;QAE/B,IAAI,IAAe,CAAC;QACpB,IAAI,WAAmB,CAAC;QAExB,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YACnB,IAAI,GAAG,SAAS,CAAC,iBAAiB,CAAC;YACnC,WAAW,GAAG,gFAAgF,CAAC;QACjG,CAAC;aAAM,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5C,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC;YAC5B,WAAW,GAAG,iDAAiD,CAAC;QAClE,CAAC;aAAM,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;YACzB,IAAI,GAAG,SAAS,CAAC,YAAY,CAAC;YAC9B,WAAW,GAAG,4DAA4D,CAAC;QAC7E,CAAC;aAAM,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;YACzB,IAAI,GAAG,SAAS,CAAC,mBAAmB,CAAC;YACrC,WAAW,GAAG,iEAAiE,CAAC;QAClF,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC;YAC/B,WAAW,GAAG,uDAAuD,CAAC;QACxE,CAAC;QAED,OAAO,IAAI,aAAa,CACtB,IAAI,EACJ,QAAQ,MAAM,KAAK,UAAU,EAAE,EAC/B,WAAW,EACX,MAAM,IAAI,GAAG,IAAI,MAAM,KAAK,GAAG,IAAI,MAAM,KAAK,GAAG,EAAE,qBAAqB;QACxE;YACE,GAAG,OAAO;YACV,OAAO,EAAE;gBACP,MAAM;gBACN,UAAU;gBACV,YAAY,EAAE,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;aACjF;SACF,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,OAA8B;QACtD,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,OAAO,EAAE,CAAC;IAC3D,CAAC;IAEO,mBAAmB,CAAC,KAAoB;QAC9C,mCAAmC;QACnC,eAAM,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,EAAE;YAC/C,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,WAAW,EAAE,KAAK,CAAC,WAAW;SAC/B,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,CAAC,KAAK,CAAC,OAAO,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC;QAC9C,CAAC;QAED,6CAA6C;QAC7C,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAEO,kBAAkB,CAAC,KAAY;QACrC,eAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAE3E,MAAM,aAAa,GAAG,IAAI,aAAa,CACrC,SAAS,CAAC,aAAa,EACvB,KAAK,CAAC,OAAO,EACb,iDAAiD,EACjD,IAAI,EACJ,EAAE,aAAa,EAAE,KAAK,CAAC,IAAI,EAAE,CAC9B,CAAC;QAEF,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAEO,uBAAuB,CAAC,IAAe;QAC7C,MAAM,WAAW,GAAyC;YACxD,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC1B,wCAAwC;gBACxC,sCAAsC;gBACtC,sDAAsD;aACvD;YACD,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;gBACxB,6BAA6B;gBAC7B,iCAAiC;gBACjC,4BAA4B;aAC7B;YACD,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE;gBAC7B,6BAA6B;gBAC7B,sBAAsB;gBACtB,8BAA8B;aAC/B;YACD,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;gBAC1B,0CAA0C;gBAC1C,sCAAsC;gBACtC,wCAAwC;aACzC;SACF,CAAC;QAEF,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC/B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;CACF;AAhQD,oCAgQC;AAED,mDAAmD;AACtC,QAAA,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;AAEvD,SAAgB,iBAAiB,CAAC,OAAe,EAAE,IAAa;IAC9D,OAAO,IAAI,aAAa,CACtB,SAAS,CAAC,cAAc,EACxB,OAAO,EACP,SAAS,EACT,KAAK,EACL,EAAE,IAAI,EAAE,CACT,CAAC;AACJ,CAAC;AAED,SAAgB,cAAc,CAAC,OAAe,EAAE,SAAkB;IAChE,OAAO,IAAI,aAAa,CACtB,SAAS,CAAC,oBAAoB,EAC9B,OAAO,EACP,SAAS,EACT,KAAK,EACL,EAAE,SAAS,EAAE,CACd,CAAC;AACJ,CAAC;AAED,SAAgB,oBAAoB,CAAC,OAAe,EAAE,QAAiB;IACrE,OAAO,IAAI,aAAa,CACtB,SAAS,CAAC,YAAY,EACtB,OAAO,EACP,SAAS,EACT,IAAI,EACJ,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,EAAE,CAC1B,CAAC;AACJ,CAAC;AAED,SAAgB,eAAe,CAAC,OAAe,EAAE,IAAa,EAAE,SAAkB;IAChF,MAAM,IAAI,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAC3F,OAAO,IAAI,aAAa,CACtB,IAAI,EACJ,OAAO,EACP,SAAS,EACT,KAAK,EACL,EAAE,IAAI,EAAE,SAAS,EAAE,CACpB,CAAC;AACJ,CAAC"}