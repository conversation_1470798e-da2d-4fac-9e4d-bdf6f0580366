"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryMonitor = exports.performanceMonitor = exports.PerformanceMonitor = void 0;
exports.monitored = monitored;
const logger_1 = require("./logger");
class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.activeOperations = new Map();
        this.enabled = true;
    }
    static getInstance() {
        if (!PerformanceMonitor.instance) {
            PerformanceMonitor.instance = new PerformanceMonitor();
        }
        return PerformanceMonitor.instance;
    }
    /**
     * Start timing an operation
     */
    startOperation(operationName, metadata) {
        if (!this.enabled)
            return '';
        const operationId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const startMetrics = {
            operationName,
            startTime: performance.now(),
            memoryUsage: this.getCurrentMemoryUsage(),
            metadata
        };
        this.activeOperations.set(operationId, startMetrics);
        return operationId;
    }
    /**
     * End timing an operation
     */
    endOperation(operationId, success = true, error) {
        if (!this.enabled || !operationId)
            return null;
        const activeMetric = this.activeOperations.get(operationId);
        if (!activeMetric) {
            logger_1.logger.warn(`No active operation found for ID: ${operationId}`);
            return null;
        }
        const endTime = performance.now();
        const duration = endTime - activeMetric.startTime;
        const endMemoryUsage = this.getCurrentMemoryUsage();
        const completedMetric = {
            ...activeMetric,
            endTime,
            duration,
            metadata: {
                ...activeMetric.metadata,
                success,
                error: error ? error.message : undefined,
                memoryDelta: this.calculateMemoryDelta(activeMetric.memoryUsage, endMemoryUsage)
            }
        };
        // Store the completed metric
        const operationMetrics = this.metrics.get(activeMetric.operationName) || [];
        operationMetrics.push(completedMetric);
        this.metrics.set(activeMetric.operationName, operationMetrics);
        // Clean up active operation
        this.activeOperations.delete(operationId);
        // Log performance for slow operations
        if (duration > 1000) { // Log operations taking more than 1 second
            logger_1.logger.info(`Slow operation detected: ${activeMetric.operationName} took ${duration.toFixed(2)}ms`);
        }
        return completedMetric;
    }
    /**
     * Time an async operation automatically
     */
    async timeOperation(operationName, operation, metadata) {
        const operationId = this.startOperation(operationName, metadata);
        try {
            const result = await operation();
            this.endOperation(operationId, true);
            return result;
        }
        catch (error) {
            this.endOperation(operationId, false, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    /**
     * Time a synchronous operation automatically
     */
    timeSync(operationName, operation, metadata) {
        const operationId = this.startOperation(operationName, metadata);
        try {
            const result = operation();
            this.endOperation(operationId, true);
            return result;
        }
        catch (error) {
            this.endOperation(operationId, false, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
    /**
     * Get aggregated metrics for an operation
     */
    getAggregatedMetrics(operationName) {
        const operationMetrics = this.metrics.get(operationName);
        if (!operationMetrics || operationMetrics.length === 0) {
            return null;
        }
        const durations = operationMetrics.map(m => m.duration || 0);
        const successCount = operationMetrics.filter(m => m.metadata?.success !== false).length;
        const errorCount = operationMetrics.length - successCount;
        return {
            operationName,
            count: operationMetrics.length,
            totalDuration: durations.reduce((sum, d) => sum + d, 0),
            averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
            minDuration: Math.min(...durations),
            maxDuration: Math.max(...durations),
            successCount,
            errorCount
        };
    }
    /**
     * Get all performance metrics
     */
    getAllMetrics() {
        const result = new Map();
        for (const operationName of this.metrics.keys()) {
            const aggregated = this.getAggregatedMetrics(operationName);
            if (aggregated) {
                result.set(operationName, aggregated);
            }
        }
        return result;
    }
    /**
     * Generate performance report
     */
    generateReport() {
        const allMetrics = this.getAllMetrics();
        if (allMetrics.size === 0) {
            return 'No performance metrics collected';
        }
        const lines = [];
        lines.push('📊 Performance Report');
        lines.push('='.repeat(50));
        lines.push('');
        // Sort by total duration descending
        const sortedMetrics = Array.from(allMetrics.entries()).sort(([, a], [, b]) => b.totalDuration - a.totalDuration);
        for (const [operationName, metrics] of sortedMetrics) {
            lines.push(`🔧 ${operationName}`);
            lines.push(`   Count: ${metrics.count}`);
            lines.push(`   Total Time: ${metrics.totalDuration.toFixed(2)}ms`);
            lines.push(`   Average Time: ${metrics.averageDuration.toFixed(2)}ms`);
            lines.push(`   Min/Max: ${metrics.minDuration.toFixed(2)}ms / ${metrics.maxDuration.toFixed(2)}ms`);
            lines.push(`   Success Rate: ${((metrics.successCount / metrics.count) * 100).toFixed(1)}%`);
            lines.push('');
        }
        // Add memory usage summary
        const currentMemory = this.getCurrentMemoryUsage();
        lines.push('💾 Current Memory Usage');
        lines.push(`   Heap Used: ${(currentMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        lines.push(`   Heap Total: ${(currentMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);
        lines.push(`   RSS: ${(currentMemory.rss / 1024 / 1024).toFixed(2)} MB`);
        lines.push('');
        // Add bottleneck analysis
        const bottlenecks = this.identifyBottlenecks();
        if (bottlenecks.length > 0) {
            lines.push('⚠️  Performance Bottlenecks Detected');
            bottlenecks.forEach(bottleneck => {
                lines.push(`   • ${bottleneck}`);
            });
            lines.push('');
        }
        return lines.join('\n');
    }
    /**
     * Clear all metrics
     */
    clearMetrics() {
        this.metrics.clear();
        this.activeOperations.clear();
    }
    /**
     * Enable or disable performance monitoring
     */
    setEnabled(enabled) {
        this.enabled = enabled;
        if (!enabled) {
            this.clearMetrics();
        }
    }
    /**
     * Check if monitoring is enabled
     */
    isEnabled() {
        return this.enabled;
    }
    /**
     * Export metrics to JSON format
     */
    exportMetrics() {
        const data = {
            timestamp: new Date().toISOString(),
            aggregatedMetrics: Object.fromEntries(this.getAllMetrics()),
            rawMetrics: Object.fromEntries(this.metrics),
            currentMemory: this.getCurrentMemoryUsage()
        };
        return JSON.stringify(data, null, 2);
    }
    /**
     * Get current memory usage
     */
    getCurrentMemoryUsage() {
        const memUsage = process.memoryUsage();
        return {
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external,
            rss: memUsage.rss
        };
    }
    /**
     * Calculate memory usage delta
     */
    calculateMemoryDelta(start, end) {
        return {
            heapUsedDelta: end.heapUsed - start.heapUsed,
            heapTotalDelta: end.heapTotal - start.heapTotal,
            rssDelta: end.rss - start.rss
        };
    }
    /**
     * Identify performance bottlenecks
     */
    identifyBottlenecks() {
        const bottlenecks = [];
        const allMetrics = this.getAllMetrics();
        for (const [operationName, metrics] of allMetrics) {
            // Check for slow operations (> 2 seconds average)
            if (metrics.averageDuration > 2000) {
                bottlenecks.push(`${operationName}: Slow average duration (${metrics.averageDuration.toFixed(2)}ms)`);
            }
            // Check for high error rates (> 10%)
            const errorRate = (metrics.errorCount / metrics.count) * 100;
            if (errorRate > 10) {
                bottlenecks.push(`${operationName}: High error rate (${errorRate.toFixed(1)}%)`);
            }
            // Check for operations with high variance (max > 5x average)
            if (metrics.maxDuration > metrics.averageDuration * 5) {
                bottlenecks.push(`${operationName}: High performance variance (max ${metrics.maxDuration.toFixed(2)}ms vs avg ${metrics.averageDuration.toFixed(2)}ms)`);
            }
        }
        return bottlenecks;
    }
}
exports.PerformanceMonitor = PerformanceMonitor;
// Global performance monitor instance
exports.performanceMonitor = PerformanceMonitor.getInstance();
// Decorator for automatic performance monitoring
function monitored(operationName) {
    return function (target, propertyName, descriptor) {
        const method = descriptor.value;
        const finalOperationName = operationName || `${target.constructor.name}.${propertyName}`;
        descriptor.value = async function (...args) {
            if (method.constructor.name === 'AsyncFunction') {
                return exports.performanceMonitor.timeOperation(finalOperationName, () => method.apply(this, args));
            }
            else {
                return exports.performanceMonitor.timeSync(finalOperationName, () => method.apply(this, args));
            }
        };
        return descriptor;
    };
}
// Memory monitoring utilities
class MemoryMonitor {
    /**
     * Start monitoring memory usage
     */
    static startMonitoring(intervalMs = 30000) {
        this.stopMonitoring(); // Ensure no duplicate intervals
        this.memoryCheckInterval = setInterval(() => {
            const usage = process.memoryUsage();
            if (usage.heapUsed > this.memoryThreshold) {
                logger_1.logger.warn(`High memory usage detected: ${(usage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
                // Suggest garbage collection if available
                if (global.gc) {
                    logger_1.logger.info('Running garbage collection...');
                    global.gc();
                }
            }
        }, intervalMs);
    }
    /**
     * Stop memory monitoring
     */
    static stopMonitoring() {
        if (this.memoryCheckInterval) {
            clearInterval(this.memoryCheckInterval);
            this.memoryCheckInterval = null;
        }
    }
    /**
     * Force garbage collection if available
     */
    static forceGC() {
        if (global.gc) {
            const beforeGC = process.memoryUsage().heapUsed;
            global.gc();
            const afterGC = process.memoryUsage().heapUsed;
            const recovered = beforeGC - afterGC;
            logger_1.logger.info(`Garbage collection completed. Recovered ${(recovered / 1024 / 1024).toFixed(2)} MB`);
        }
        else {
            logger_1.logger.warn('Garbage collection not available. Run with --expose-gc flag to enable.');
        }
    }
    /**
     * Set memory usage threshold for warnings
     */
    static setMemoryThreshold(thresholdMB) {
        this.memoryThreshold = thresholdMB * 1024 * 1024;
    }
}
exports.MemoryMonitor = MemoryMonitor;
MemoryMonitor.memoryCheckInterval = null;
MemoryMonitor.memoryThreshold = 500 * 1024 * 1024; // 500MB threshold
//# sourceMappingURL=performance.js.map