import { ProgressOptions } from '../types';
export declare class ProgressBar {
    private total;
    private current;
    private message;
    private startTime;
    constructor(options: ProgressOptions);
    update(current: number, message?: string): void;
    increment(message?: string): void;
    complete(message?: string): void;
    private render;
}
export declare class Spinner {
    private frames;
    private current;
    private interval;
    private message;
    constructor(message?: string);
    start(): void;
    stop(successMessage?: string): void;
    updateMessage(message: string): void;
}
//# sourceMappingURL=progress.d.ts.map