{"version": 3, "file": "performance.d.ts", "sourceRoot": "", "sources": ["../../src/utils/performance.ts"], "names": [], "mappings": "AAEA,MAAM,WAAW,kBAAkB;IACjC,aAAa,EAAE,MAAM,CAAC;IACtB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,WAAW,CAAC,EAAE;QACZ,QAAQ,EAAE,MAAM,CAAC;QACjB,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,GAAG,EAAE,MAAM,CAAC;KACb,CAAC;IACF,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,iBAAiB;IAChC,aAAa,EAAE,MAAM,CAAC;IACtB,KAAK,EAAE,MAAM,CAAC;IACd,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;IACrB,UAAU,EAAE,MAAM,CAAC;CACpB;AAED,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAqB;IAC5C,OAAO,CAAC,OAAO,CAAgD;IAC/D,OAAO,CAAC,gBAAgB,CAA8C;IACtE,OAAO,CAAC,OAAO,CAAiB;IAChC,OAAO,CAAC,sBAAsB,CAAgB;IAC9C,OAAO,CAAC,eAAe,CAA+B;IAEtD,OAAO;WAKO,WAAW,IAAI,kBAAkB;IAO/C;;OAEG;IACI,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM;IAepF;;OAEG;IACI,YAAY,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,GAAE,OAAc,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,kBAAkB,GAAG,IAAI;IA+C3G;;OAEG;IACU,aAAa,CAAC,CAAC,EAC1B,aAAa,EAAE,MAAM,EACrB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,OAAO,CAAC,CAAC,CAAC;IAab;;OAEG;IACI,QAAQ,CAAC,CAAC,EACf,aAAa,EAAE,MAAM,EACrB,SAAS,EAAE,MAAM,CAAC,EAClB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAC7B,CAAC;IAaJ;;OAEG;IACI,oBAAoB,CAAC,aAAa,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI;IAsB5E;;OAEG;IACI,aAAa,IAAI,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC;IAatD;;OAEG;IACI,cAAc,IAAI,MAAM;IAgD/B;;OAEG;IACI,YAAY,IAAI,IAAI;IAK3B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAOxB;;OAEG;IACH,OAAO,CAAC,iBAAiB;IAqCzB;;OAEG;IACH,OAAO,CAAC,WAAW;IAUnB;;OAEG;IACI,OAAO,IAAI,IAAI;IAQtB;;OAEG;IACI,UAAU,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAOzC;;OAEG;IACI,SAAS,IAAI,OAAO;IAI3B;;OAEG;IACI,aAAa,IAAI,MAAM;IAW9B;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAU7B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAQ5B;;OAEG;IACH,OAAO,CAAC,mBAAmB;CAwB5B;AAGD,eAAO,MAAM,kBAAkB,oBAAmC,CAAC;AAGnE,wBAAgB,SAAS,CAAC,aAAa,CAAC,EAAE,MAAM,IAC7B,QAAQ,GAAG,EAAE,cAAc,MAAM,EAAE,YAAY,kBAAkB,wBAcnF;AAGD,qBAAa,aAAa;IACxB,OAAO,CAAC,MAAM,CAAC,mBAAmB,CAA+B;IACjE,OAAO,CAAC,MAAM,CAAC,eAAe,CAAqB;IAEnD;;OAEG;WACW,eAAe,CAAC,UAAU,GAAE,MAAc,GAAG,IAAI;IAkB/D;;OAEG;WACW,cAAc,IAAI,IAAI;IAOpC;;OAEG;WACW,OAAO,IAAI,IAAI;IAa7B;;OAEG;WACW,kBAAkB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;CAG5D"}