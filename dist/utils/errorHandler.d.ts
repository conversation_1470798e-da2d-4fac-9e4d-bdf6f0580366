export declare enum ErrorCode {
    CONFIG_INVALID = "CONFIG_INVALID",
    CONFIG_NOT_FOUND = "CONFIG_NOT_FOUND",
    GIT_NOT_REPO = "GIT_NOT_REPO",
    GIT_NO_CHANGES = "GIT_NO_CHANGES",
    GIT_OPERATION_FAILED = "GIT_OPERATION_FAILED",
    FILE_NOT_FOUND = "FILE_NOT_FOUND",
    FILE_READ_ERROR = "FILE_READ_ERROR",
    FILE_WRITE_ERROR = "FILE_WRITE_ERROR",
    AI_API_ERROR = "AI_API_ERROR",
    AI_QUOTA_EXCEEDED = "AI_QUOTA_EXCEEDED",
    AI_TIMEOUT = "AI_TIMEOUT",
    AI_INVALID_RESPONSE = "AI_INVALID_RESPONSE",
    CACHE_READ_ERROR = "CACHE_READ_ERROR",
    CACHE_WRITE_ERROR = "CACHE_WRITE_ERROR",
    PARSE_ERROR = "PARSE_ERROR",
    ANALYSIS_FAILED = "ANALYSIS_FAILED",
    REPORT_GENERATION_FAILED = "REPORT_GENERATION_FAILED",
    UNKNOWN_ERROR = "UNKNOWN_ERROR",
    NETWORK_ERROR = "NETWORK_ERROR"
}
export interface ErrorContext {
    file?: string;
    operation?: string;
    details?: Record<string, any>;
    timestamp: string;
    stackTrace?: string;
    originalError?: string;
    primaryError?: string;
    fallbackError?: string;
}
export declare class AIReviewError extends Error {
    readonly code: ErrorCode;
    readonly context: ErrorContext;
    readonly recoverable: boolean;
    readonly userMessage: string;
    constructor(code: ErrorCode, message: string, userMessage?: string, recoverable?: boolean, context?: Partial<ErrorContext>);
    private getDefaultUserMessage;
    toJSON(): object;
}
export interface RetryOptions {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffFactor: number;
    retryableErrors?: ErrorCode[];
}
export declare class ErrorHandler {
    private static instance;
    private retryOptions;
    static getInstance(): ErrorHandler;
    /**
     * Handle error with appropriate logging and user feedback
     */
    handleError(error: Error | AIReviewError): void;
    /**
     * Execute operation with automatic retry logic
     */
    withRetry<T>(operation: () => Promise<T>, context: string, customRetryOptions?: Partial<RetryOptions>): Promise<T>;
    /**
     * Graceful degradation - continue operation with fallback
     */
    withFallback<T>(primaryOperation: () => Promise<T>, fallbackOperation: () => Promise<T>, context: string): Promise<T>;
    /**
     * Validate operation preconditions and throw descriptive error
     */
    validatePrecondition(condition: boolean, errorCode: ErrorCode, message: string, userMessage?: string, context?: Partial<ErrorContext>): void;
    /**
     * Create error from HTTP response
     */
    createHttpError(status: number, statusText: string, responseBody?: any, context?: Partial<ErrorContext>): AIReviewError;
    /**
     * Update retry options
     */
    updateRetryOptions(options: Partial<RetryOptions>): void;
    private handleAIReviewError;
    private handleGenericError;
    private showRecoverySuggestions;
    private sleep;
}
export declare const errorHandler: ErrorHandler;
export declare function createConfigError(message: string, file?: string): AIReviewError;
export declare function createGitError(message: string, operation?: string): AIReviewError;
export declare function createAIServiceError(message: string, provider?: string): AIReviewError;
export declare function createFileError(message: string, file?: string, operation?: string): AIReviewError;
//# sourceMappingURL=errorHandler.d.ts.map