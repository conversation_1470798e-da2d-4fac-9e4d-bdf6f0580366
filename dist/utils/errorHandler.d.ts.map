{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errorHandler.ts"], "names": [], "mappings": "AAEA,oBAAY,SAAS;IAEnB,cAAc,mBAAmB;IACjC,gBAAgB,qBAAqB;IAGrC,YAAY,iBAAiB;IAC7B,cAAc,mBAAmB;IACjC,oBAAoB,yBAAyB;IAG7C,cAAc,mBAAmB;IACjC,eAAe,oBAAoB;IACnC,gBAAgB,qBAAqB;IAGrC,YAAY,iBAAiB;IAC7B,iBAAiB,sBAAsB;IACvC,UAAU,eAAe;IACzB,mBAAmB,wBAAwB;IAG3C,gBAAgB,qBAAqB;IACrC,iBAAiB,sBAAsB;IAGvC,WAAW,gBAAgB;IAC3B,eAAe,oBAAoB;IAGnC,wBAAwB,6BAA6B;IAGrD,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;CAChC;AAED,MAAM,WAAW,YAAY;IAC3B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9B,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,qBAAa,aAAc,SAAQ,KAAK;IACtC,SAAgB,IAAI,EAAE,SAAS,CAAC;IAChC,SAAgB,OAAO,EAAE,YAAY,CAAC;IACtC,SAAgB,WAAW,EAAE,OAAO,CAAC;IACrC,SAAgB,WAAW,EAAE,MAAM,CAAC;gBAGlC,IAAI,EAAE,SAAS,EACf,OAAO,EAAE,MAAM,EACf,WAAW,CAAC,EAAE,MAAM,EACpB,WAAW,GAAE,OAAe,EAC5B,OAAO,GAAE,OAAO,CAAC,YAAY,CAAM;IAmBrC,OAAO,CAAC,qBAAqB;IA0BtB,MAAM,IAAI,MAAM;CAUxB;AAED,MAAM,WAAW,YAAY;IAC3B,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;IACtB,eAAe,CAAC,EAAE,SAAS,EAAE,CAAC;CAC/B;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAe;IACtC,OAAO,CAAC,YAAY,CAWlB;WAEY,WAAW,IAAI,YAAY;IAOzC;;OAEG;IACI,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,aAAa,GAAG,IAAI;IAQtD;;OAEG;IACU,SAAS,CAAC,CAAC,EACtB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,EAAE,MAAM,EACf,kBAAkB,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,GACzC,OAAO,CAAC,CAAC,CAAC;IAoDb;;OAEG;IACU,YAAY,CAAC,CAAC,EACzB,gBAAgB,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAClC,iBAAiB,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EACnC,OAAO,EAAE,MAAM,GACd,OAAO,CAAC,CAAC,CAAC;IAyBb;;OAEG;IACI,oBAAoB,CACzB,SAAS,EAAE,OAAO,EAClB,SAAS,EAAE,SAAS,EACpB,OAAO,EAAE,MAAM,EACf,WAAW,CAAC,EAAE,MAAM,EACpB,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,GAC9B,IAAI;IAMP;;OAEG;IACI,eAAe,CACpB,MAAM,EAAE,MAAM,EACd,UAAU,EAAE,MAAM,EAClB,YAAY,CAAC,EAAE,GAAG,EAClB,OAAO,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC,GAC9B,aAAa;IAqChB;;OAEG;IACI,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;IAI/D,OAAO,CAAC,mBAAmB;IAgB3B,OAAO,CAAC,kBAAkB;IAc1B,OAAO,CAAC,uBAAuB;IAgC/B,OAAO,CAAC,KAAK;CAGd;AAGD,eAAO,MAAM,YAAY,cAA6B,CAAC;AAEvD,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,aAAa,CAQ/E;AAED,wBAAgB,cAAc,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,aAAa,CAQjF;AAED,wBAAgB,oBAAoB,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,aAAa,CAQtF;AAED,wBAAgB,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,aAAa,CASjG"}