import { LogLevel } from '../types';
export declare class Logger {
    private static instance;
    private logLevel;
    private verbose;
    private constructor();
    static getInstance(): Logger;
    setLogLevel(level: LogLevel): void;
    setVerbose(verbose: boolean): void;
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    success(message: string, ...args: any[]): void;
    log(message: string, ...args: any[]): void;
    private shouldLog;
}
export declare const logger: Logger;
//# sourceMappingURL=logger.d.ts.map