export interface PerformanceMetrics {
    operationName: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    memoryUsage?: {
        heapUsed: number;
        heapTotal: number;
        external: number;
        rss: number;
    };
    metadata?: Record<string, any>;
}
export interface AggregatedMetrics {
    operationName: string;
    count: number;
    totalDuration: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    successCount: number;
    errorCount: number;
}
export declare class PerformanceMonitor {
    private static instance;
    private metrics;
    private activeOperations;
    private enabled;
    static getInstance(): PerformanceMonitor;
    /**
     * Start timing an operation
     */
    startOperation(operationName: string, metadata?: Record<string, any>): string;
    /**
     * End timing an operation
     */
    endOperation(operationId: string, success?: boolean, error?: Error): PerformanceMetrics | null;
    /**
     * Time an async operation automatically
     */
    timeOperation<T>(operationName: string, operation: () => Promise<T>, metadata?: Record<string, any>): Promise<T>;
    /**
     * Time a synchronous operation automatically
     */
    timeSync<T>(operationName: string, operation: () => T, metadata?: Record<string, any>): T;
    /**
     * Get aggregated metrics for an operation
     */
    getAggregatedMetrics(operationName: string): AggregatedMetrics | null;
    /**
     * Get all performance metrics
     */
    getAllMetrics(): Map<string, AggregatedMetrics>;
    /**
     * Generate performance report
     */
    generateReport(): string;
    /**
     * Clear all metrics
     */
    clearMetrics(): void;
    /**
     * Enable or disable performance monitoring
     */
    setEnabled(enabled: boolean): void;
    /**
     * Check if monitoring is enabled
     */
    isEnabled(): boolean;
    /**
     * Export metrics to JSON format
     */
    exportMetrics(): string;
    /**
     * Get current memory usage
     */
    private getCurrentMemoryUsage;
    /**
     * Calculate memory usage delta
     */
    private calculateMemoryDelta;
    /**
     * Identify performance bottlenecks
     */
    private identifyBottlenecks;
}
export declare const performanceMonitor: PerformanceMonitor;
export declare function monitored(operationName?: string): (target: any, propertyName: string, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare class MemoryMonitor {
    private static memoryCheckInterval;
    private static memoryThreshold;
    /**
     * Start monitoring memory usage
     */
    static startMonitoring(intervalMs?: number): void;
    /**
     * Stop memory monitoring
     */
    static stopMonitoring(): void;
    /**
     * Force garbage collection if available
     */
    static forceGC(): void;
    /**
     * Set memory usage threshold for warnings
     */
    static setMemoryThreshold(thresholdMB: number): void;
}
//# sourceMappingURL=performance.d.ts.map