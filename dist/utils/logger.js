"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
const chalk_1 = __importDefault(require("chalk"));
class Logger {
    constructor() {
        this.logLevel = 'info';
        this.verbose = false;
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    setLogLevel(level) {
        this.logLevel = level;
    }
    setVerbose(verbose) {
        this.verbose = verbose;
    }
    debug(message, ...args) {
        if (this.shouldLog('debug')) {
            console.log(chalk_1.default.gray(`[DEBUG] ${message}`), ...args);
        }
    }
    info(message, ...args) {
        if (this.shouldLog('info')) {
            console.log(chalk_1.default.blue(`[INFO] ${message}`), ...args);
        }
    }
    warn(message, ...args) {
        if (this.shouldLog('warn')) {
            console.warn(chalk_1.default.yellow(`[WARN] ${message}`), ...args);
        }
    }
    error(message, ...args) {
        if (this.shouldLog('error')) {
            console.error(chalk_1.default.red(`[ERROR] ${message}`), ...args);
        }
    }
    success(message, ...args) {
        console.log(chalk_1.default.green(`[SUCCESS] ${message}`), ...args);
    }
    log(message, ...args) {
        console.log(message, ...args);
    }
    shouldLog(level) {
        const levels = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3
        };
        const currentLevelValue = levels[this.logLevel];
        const messageLevelValue = levels[level];
        return messageLevelValue >= currentLevelValue || (level === 'debug' && this.verbose);
    }
}
exports.Logger = Logger;
exports.logger = Logger.getInstance();
//# sourceMappingURL=logger.js.map