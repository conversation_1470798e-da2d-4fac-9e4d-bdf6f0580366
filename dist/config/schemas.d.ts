import { AIReviewConfig } from '../types';
export declare const configSchema: {
    type: string;
    properties: {
        aiProvider: {
            type: string;
            enum: string[];
            description: string;
        };
        model: {
            type: string;
            description: string;
        };
        apiKey: {
            type: string;
            description: string;
        };
        baseUrl: {
            type: string;
            description: string;
        };
        ignore: {
            type: string;
            items: {
                type: string;
            };
            description: string;
        };
        rules: {
            type: string;
            properties: {
                logic: {
                    type: string;
                    enum: string[];
                    description: string;
                };
                performance: {
                    type: string;
                    enum: string[];
                    description: string;
                };
                security: {
                    type: string;
                    enum: string[];
                    description: string;
                };
                style: {
                    type: string;
                    enum: string[];
                    description: string;
                };
            };
            required: string[];
        };
        output: {
            type: string;
            properties: {
                format: {
                    type: string;
                    enum: string[];
                    description: string;
                };
                detailed: {
                    type: string;
                    description: string;
                };
            };
            required: string[];
        };
        cache: {
            type: string;
            properties: {
                enabled: {
                    type: string;
                    description: string;
                };
                ttl: {
                    type: string;
                    minimum: number;
                    maximum: number;
                    description: string;
                };
            };
            required: string[];
        };
    };
    required: string[];
};
export declare const defaultConfig: AIReviewConfig;
export declare function validateConfig(config: any): {
    valid: boolean;
    errors: string[];
};
//# sourceMappingURL=schemas.d.ts.map