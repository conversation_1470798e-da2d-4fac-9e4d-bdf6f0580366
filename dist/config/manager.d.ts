import { AIReviewConfig } from '../types';
export declare class ConfigManager {
    private static instance;
    private config;
    private configPath;
    private constructor();
    static getInstance(): ConfigManager;
    loadConfig(customConfigPath?: string): Promise<AIReviewConfig>;
    getConfig(): AIReviewConfig;
    saveConfig(config: AIReviewConfig, filePath?: string): Promise<void>;
    private findConfigFile;
    private createDefaultConfigFile;
    private mergeConfig;
    private loadFromEnvironment;
    generateConfigTemplate(filePath?: string): Promise<void>;
    /**
     * 验证配置完整性和合理性
     */
    validateConfigurationAdvanced(config?: AIReviewConfig): Promise<{
        valid: boolean;
        errors: string[];
        warnings: string[];
        suggestions: string[];
    }>;
    /**
     * 自动修复配置问题
     */
    autoFixConfiguration(config: AIReviewConfig): Promise<{
        fixed: AIReviewConfig;
        changes: string[];
    }>;
    /**
     * 检测项目类型并生成推荐配置
     */
    generateProjectSpecificConfig(): Promise<{
        config: Partial<AIReviewConfig>;
        detectedFeatures: string[];
        recommendations: string[];
    }>;
    private getEnvironmentAPIKeyName;
    private isModelCompatible;
    private getCompatibleModel;
}
//# sourceMappingURL=manager.d.ts.map