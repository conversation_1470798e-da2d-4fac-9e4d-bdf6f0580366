"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const schemas_1 = require("./schemas");
const logger_1 = require("../utils/logger");
class ConfigManager {
    constructor() {
        this.config = null;
        this.configPath = null;
    }
    static getInstance() {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }
    async loadConfig(customConfigPath) {
        try {
            // 确定配置文件路径
            this.configPath = customConfigPath || this.findConfigFile();
            if (this.configPath && await fs.pathExists(this.configPath)) {
                logger_1.logger.debug(`Loading config from: ${this.configPath}`);
                const configData = await fs.readJSON(this.configPath);
                // 验证配置
                const validation = (0, schemas_1.validateConfig)(configData);
                if (!validation.valid) {
                    logger_1.logger.error('Invalid configuration:');
                    validation.errors.forEach(error => logger_1.logger.error(`  - ${error}`));
                    throw new Error('Invalid configuration file');
                }
                // 合并默认配置
                this.config = this.mergeConfig(schemas_1.defaultConfig, configData);
                logger_1.logger.info('Configuration loaded successfully');
            }
            else {
                logger_1.logger.warn('No configuration file found, using default configuration');
                this.config = { ...schemas_1.defaultConfig };
                // 创建默认配置文件
                await this.createDefaultConfigFile();
            }
            // 从环境变量读取敏感信息
            this.loadFromEnvironment();
            return this.config;
        }
        catch (error) {
            logger_1.logger.error(`Failed to load configuration: ${error}`);
            throw error;
        }
    }
    getConfig() {
        if (!this.config) {
            throw new Error('Configuration not loaded. Call loadConfig() first.');
        }
        return this.config;
    }
    async saveConfig(config, filePath) {
        const targetPath = filePath || this.configPath || path.join(process.cwd(), '.ai-review.json');
        const validation = (0, schemas_1.validateConfig)(config);
        if (!validation.valid) {
            throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
        }
        await fs.writeJSON(targetPath, config, { spaces: 2 });
        logger_1.logger.info(`Configuration saved to: ${targetPath}`);
    }
    findConfigFile() {
        const possiblePaths = [
            path.join(process.cwd(), '.ai-review.json'),
            path.join(process.cwd(), '.ai-review.jsonc'),
            path.join(process.cwd(), 'ai-review.config.json'),
            path.join(process.env.HOME || '', '.ai-review.json')
        ];
        for (const configPath of possiblePaths) {
            if (fs.existsSync(configPath)) {
                return configPath;
            }
        }
        return null;
    }
    async createDefaultConfigFile() {
        const defaultPath = path.join(process.cwd(), '.ai-review.json');
        if (!await fs.pathExists(defaultPath)) {
            await fs.writeJSON(defaultPath, schemas_1.defaultConfig, { spaces: 2 });
            logger_1.logger.info(`Created default configuration file: ${defaultPath}`);
        }
    }
    mergeConfig(base, override) {
        const merged = { ...base };
        // 深度合并规则
        if (override.rules) {
            merged.rules = { ...base.rules, ...override.rules };
        }
        // 深度合并输出配置
        if (override.output) {
            merged.output = { ...base.output, ...override.output };
        }
        // 深度合并缓存配置
        if (override.cache) {
            merged.cache = { ...base.cache, ...override.cache };
        }
        // 其他属性直接覆盖
        Object.keys(override).forEach(key => {
            if (key !== 'rules' && key !== 'output' && key !== 'cache') {
                merged[key] = override[key];
            }
        });
        return merged;
    }
    loadFromEnvironment() {
        if (!this.config)
            return;
        // 从环境变量读取API密钥
        if (!this.config.apiKey) {
            const envKey = process.env.OPENAI_API_KEY ||
                process.env.ANTHROPIC_API_KEY ||
                process.env.DEEPSEEK_API_KEY ||
                process.env.AI_REVIEW_API_KEY;
            if (envKey) {
                this.config.apiKey = envKey;
                logger_1.logger.debug('API key loaded from environment variables');
            }
        }
        // 从环境变量读取其他配置
        if (process.env.AI_REVIEW_PROVIDER) {
            const provider = process.env.AI_REVIEW_PROVIDER;
            if (['openai', 'claude', 'local', 'deepseek'].includes(provider)) {
                this.config.aiProvider = provider;
            }
        }
        if (process.env.AI_REVIEW_MODEL) {
            this.config.model = process.env.AI_REVIEW_MODEL;
        }
        // 从环境变量读取自定义基础URL
        if (process.env.AI_REVIEW_BASE_URL) {
            this.config.baseUrl = process.env.AI_REVIEW_BASE_URL;
        }
    }
    async generateConfigTemplate(filePath) {
        const templatePath = filePath || path.join(process.cwd(), '.ai-review.template.json');
        const template = {
            "// AI Provider Configuration": "",
            "aiProvider": "openai",
            "model": "gpt-4",
            "// apiKey": "your-api-key-here (or set environment variable OPENAI_API_KEY)",
            "// baseUrl": "https://api.deepseek.com/v1 (optional, for custom endpoints)",
            "// Analysis Rules - strictness levels": "",
            "rules": {
                "logic": "strict", // strict|normal|loose - Logic errors and bugs
                "performance": "normal", // Performance issues and optimizations  
                "security": "strict", // Security vulnerabilities
                "style": "normal", // Code style and conventions
                "business": "normal" // Business logic and domain rules
            },
            "// Output Configuration": "",
            "output": {
                "format": "terminal", // terminal|html|json
                "detailed": true, // Include detailed explanations
                "verbose": false // Show debug information
            },
            "// Cache Settings for performance": "",
            "cache": {
                "enabled": true, // Enable caching for faster analysis
                "ttl": 24 // Cache TTL in hours
            },
            "// Files and patterns to ignore": "",
            "ignore": [
                "node_modules/**",
                "*.min.js",
                "*.min.css",
                "dist/**",
                "build/**",
                ".git/**"
            ]
        };
        await fs.writeJSON(templatePath, template, { spaces: 2 });
        logger_1.logger.info(`Configuration template generated: ${templatePath}`);
    }
    /**
     * 验证配置完整性和合理性
     */
    async validateConfigurationAdvanced(config) {
        const targetConfig = config || this.config;
        if (!targetConfig) {
            return {
                valid: false,
                errors: ['No configuration available'],
                warnings: [],
                suggestions: ['Run "ai-review config --wizard" to create configuration']
            };
        }
        const errors = [];
        const warnings = [];
        const suggestions = [];
        // 基础验证
        const basicValidation = (0, schemas_1.validateConfig)(targetConfig);
        if (!basicValidation.valid) {
            errors.push(...basicValidation.errors);
        }
        // API密钥验证
        if (targetConfig.aiProvider !== 'local') {
            const envVar = this.getEnvironmentAPIKeyName(targetConfig.aiProvider);
            const hasApiKey = targetConfig.apiKey || process.env[envVar];
            if (!hasApiKey) {
                errors.push(`API key required for ${targetConfig.aiProvider}. Set ${envVar} environment variable or add to config.`);
            }
        }
        // 模型兼容性检查
        if (!this.isModelCompatible(targetConfig.aiProvider, targetConfig.model)) {
            warnings.push(`Model "${targetConfig.model}" may not be compatible with provider "${targetConfig.aiProvider}"`);
            suggestions.push('Run "ai-review config --wizard" to select compatible model');
        }
        // 输出目录权限检查
        if (targetConfig.output?.format === 'html' || targetConfig.output?.format === 'json') {
            const outputDir = path.join(process.cwd(), 'reports');
            try {
                await fs.ensureDir(outputDir);
                await fs.access(outputDir, fs.constants.W_OK);
            }
            catch (error) {
                warnings.push(`Output directory may not be writable: ${outputDir}`);
            }
        }
        // 缓存目录检查
        if (targetConfig.cache?.enabled) {
            const cacheDir = path.join(process.cwd(), '.ai-review-cache');
            try {
                await fs.ensureDir(cacheDir);
            }
            catch (error) {
                warnings.push('Cache directory cannot be created, cache will be disabled');
            }
        }
        // 性能建议
        if (targetConfig.rules?.logic === 'loose' && targetConfig.rules?.security === 'loose') {
            suggestions.push('Consider using stricter rules for better code quality');
        }
        if (!targetConfig.cache?.enabled) {
            suggestions.push('Enable caching to improve performance and reduce API costs');
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings,
            suggestions
        };
    }
    /**
     * 自动修复配置问题
     */
    async autoFixConfiguration(config) {
        const fixed = { ...config };
        const changes = [];
        // 修复缺失的规则
        if (!fixed.rules) {
            fixed.rules = { ...schemas_1.defaultConfig.rules };
            changes.push('Added missing analysis rules');
        }
        // 修复缺失的输出配置
        if (!fixed.output) {
            fixed.output = { ...schemas_1.defaultConfig.output };
            changes.push('Added missing output configuration');
        }
        // 修复缺失的缓存配置
        if (!fixed.cache) {
            fixed.cache = { ...schemas_1.defaultConfig.cache };
            changes.push('Added missing cache configuration');
        }
        // 修复不兼容的模型
        if (!this.isModelCompatible(fixed.aiProvider, fixed.model)) {
            const compatibleModel = this.getCompatibleModel(fixed.aiProvider);
            if (compatibleModel) {
                fixed.model = compatibleModel;
                changes.push(`Changed model to compatible "${compatibleModel}" for provider "${fixed.aiProvider}"`);
            }
        }
        // 确保ignore数组存在
        if (!fixed.ignore || !Array.isArray(fixed.ignore)) {
            fixed.ignore = [...schemas_1.defaultConfig.ignore];
            changes.push('Added default ignore patterns');
        }
        return { fixed, changes };
    }
    /**
     * 检测项目类型并生成推荐配置
     */
    async generateProjectSpecificConfig() {
        const detectedFeatures = [];
        const recommendations = [];
        const config = {};
        // 检测项目类型
        const packageJsonPath = path.join(process.cwd(), 'package.json');
        if (await fs.pathExists(packageJsonPath)) {
            const packageJson = await fs.readJSON(packageJsonPath);
            // React项目检测
            if (packageJson.dependencies?.react || packageJson.devDependencies?.react) {
                detectedFeatures.push('React');
                recommendations.push('Enable JSX/TSX analysis');
                if (!config.ignore)
                    config.ignore = [];
                config.ignore.push('build/**', 'public/**');
            }
            // Vue项目检测
            if (packageJson.dependencies?.vue || packageJson.devDependencies?.vue) {
                detectedFeatures.push('Vue.js');
                recommendations.push('Enable Vue single-file component analysis');
            }
            // TypeScript项目检测
            if (packageJson.devDependencies?.typescript || await fs.pathExists(path.join(process.cwd(), 'tsconfig.json'))) {
                detectedFeatures.push('TypeScript');
                recommendations.push('Use strict rules for TypeScript analysis');
                if (!config.rules)
                    config.rules = {};
                config.rules.logic = 'strict';
            }
            // Next.js项目检测
            if (packageJson.dependencies?.next || packageJson.devDependencies?.next) {
                detectedFeatures.push('Next.js');
                if (!config.ignore)
                    config.ignore = [];
                config.ignore.push('.next/**', 'out/**');
            }
            // Node.js项目检测
            if (packageJson.engines?.node || packageJson.dependencies?.express) {
                detectedFeatures.push('Node.js');
                if (!config.rules)
                    config.rules = {};
                config.rules.security = 'strict';
                recommendations.push('Use strict security rules for Node.js');
            }
        }
        // Git hooks检测
        const hooksDir = path.join(process.cwd(), '.git', 'hooks');
        if (await fs.pathExists(hooksDir)) {
            detectedFeatures.push('Git hooks');
            recommendations.push('Consider integrating AI review in pre-commit hooks');
        }
        // CI/CD检测
        const ciFiles = ['.github/workflows', '.gitlab-ci.yml', 'Jenkinsfile', '.travis.yml'];
        for (const ciFile of ciFiles) {
            if (await fs.pathExists(path.join(process.cwd(), ciFile))) {
                detectedFeatures.push('CI/CD');
                recommendations.push('Consider adding AI review to CI/CD pipeline');
                break;
            }
        }
        return {
            config,
            detectedFeatures,
            recommendations
        };
    }
    getEnvironmentAPIKeyName(provider) {
        switch (provider) {
            case 'openai': return 'OPENAI_API_KEY';
            case 'claude': return 'ANTHROPIC_API_KEY';
            case 'deepseek': return 'DEEPSEEK_API_KEY';
            default: return 'AI_REVIEW_API_KEY';
        }
    }
    isModelCompatible(provider, model) {
        const compatibility = {
            'openai': ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            'claude': ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'],
            'deepseek': ['deepseek-coder', 'deepseek-chat', 'deepseek-code-6b-instruct', 'deepseek-code-33b-instruct', 'deepseek-coder-6.7b-instruct', 'deepseek-coder-33b-instruct'],
            'local': ['codellama', 'deepseek-coder', 'starcoder']
        };
        return compatibility[provider]?.includes(model) ?? false;
    }
    getCompatibleModel(provider) {
        const defaults = {
            'openai': 'gpt-4',
            'claude': 'claude-3-sonnet-20240229',
            'deepseek': 'deepseek-coder',
            'local': 'codellama'
        };
        return defaults[provider] || null;
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=manager.js.map