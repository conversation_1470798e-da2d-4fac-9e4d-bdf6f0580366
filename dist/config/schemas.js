"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.defaultConfig = exports.configSchema = void 0;
exports.validateConfig = validateConfig;
exports.configSchema = {
    type: 'object',
    properties: {
        aiProvider: {
            type: 'string',
            enum: ['openai', 'claude', 'deepseek', 'local'],
            description: 'AI provider to use for code analysis'
        },
        model: {
            type: 'string',
            description: 'Specific model to use (e.g., gpt-4, claude-3-sonnet, deepseek-coder)'
        },
        apiKey: {
            type: 'string',
            description: 'API key for the AI provider (optional if using local)'
        },
        baseUrl: {
            type: 'string',
            description: 'Custom API base URL (optional, for custom endpoints)'
        },
        ignore: {
            type: 'array',
            items: {
                type: 'string'
            },
            description: 'Glob patterns for files/directories to ignore'
        },
        rules: {
            type: 'object',
            properties: {
                logic: {
                    type: 'string',
                    enum: ['strict', 'normal', 'loose'],
                    description: 'Logic error checking strictness'
                },
                performance: {
                    type: 'string',
                    enum: ['strict', 'normal', 'loose'],
                    description: 'Performance issue checking strictness'
                },
                security: {
                    type: 'string',
                    enum: ['strict', 'normal', 'loose'],
                    description: 'Security vulnerability checking strictness'
                },
                style: {
                    type: 'string',
                    enum: ['strict', 'normal', 'loose'],
                    description: 'Code style checking strictness'
                }
            },
            required: ['logic', 'performance', 'security', 'style']
        },
        output: {
            type: 'object',
            properties: {
                format: {
                    type: 'string',
                    enum: ['terminal', 'json', 'html'],
                    description: 'Output format for reports'
                },
                detailed: {
                    type: 'boolean',
                    description: 'Whether to include detailed issue information'
                }
            },
            required: ['format', 'detailed']
        },
        cache: {
            type: 'object',
            properties: {
                enabled: {
                    type: 'boolean',
                    description: 'Whether to enable caching'
                },
                ttl: {
                    type: 'number',
                    minimum: 1,
                    maximum: 168,
                    description: 'Cache time-to-live in hours'
                }
            },
            required: ['enabled', 'ttl']
        }
    },
    required: ['aiProvider', 'model', 'ignore', 'rules', 'output', 'cache']
};
exports.defaultConfig = {
    aiProvider: 'openai',
    model: 'gpt-4',
    ignore: [
        'node_modules/**',
        'dist/**',
        'build/**',
        '*.min.js',
        '*.min.css',
        '**/*.test.*',
        '**/*.spec.*',
        '.git/**',
        '.ai-review-cache/**'
    ],
    rules: {
        logic: 'strict',
        performance: 'normal',
        security: 'strict',
        style: 'normal',
        business: 'normal'
    },
    output: {
        format: 'terminal',
        detailed: true
    },
    cache: {
        enabled: true,
        ttl: 24
    }
};
function validateConfig(config) {
    const errors = [];
    // Basic validation
    if (!config || typeof config !== 'object') {
        return { valid: false, errors: ['Configuration must be an object'] };
    }
    // Validate aiProvider
    if (!['openai', 'claude', 'deepseek', 'local'].includes(config.aiProvider)) {
        errors.push('aiProvider must be one of: openai, claude, deepseek, local');
    }
    // Validate model
    if (!config.model || typeof config.model !== 'string') {
        errors.push('model must be a non-empty string');
    }
    // Validate baseUrl if provided
    if (config.baseUrl !== undefined) {
        if (typeof config.baseUrl !== 'string') {
            errors.push('baseUrl must be a string');
        }
        else if (config.baseUrl && !config.baseUrl.match(/^https?:\/\/.+/)) {
            errors.push('baseUrl must be a valid HTTP/HTTPS URL');
        }
    }
    // Validate ignore array
    if (!Array.isArray(config.ignore)) {
        errors.push('ignore must be an array of strings');
    }
    // Validate rules
    if (!config.rules || typeof config.rules !== 'object') {
        errors.push('rules must be an object');
    }
    else {
        const ruleTypes = ['logic', 'performance', 'security', 'style'];
        const validLevels = ['strict', 'normal', 'loose'];
        for (const ruleType of ruleTypes) {
            if (!validLevels.includes(config.rules[ruleType])) {
                errors.push(`rules.${ruleType} must be one of: ${validLevels.join(', ')}`);
            }
        }
    }
    // Validate output
    if (!config.output || typeof config.output !== 'object') {
        errors.push('output must be an object');
    }
    else {
        if (!['terminal', 'json', 'html'].includes(config.output.format)) {
            errors.push('output.format must be one of: terminal, json, html');
        }
        if (typeof config.output.detailed !== 'boolean') {
            errors.push('output.detailed must be a boolean');
        }
    }
    // Validate cache
    if (!config.cache || typeof config.cache !== 'object') {
        errors.push('cache must be an object');
    }
    else {
        if (typeof config.cache.enabled !== 'boolean') {
            errors.push('cache.enabled must be a boolean');
        }
        if (typeof config.cache.ttl !== 'number' || config.cache.ttl < 1 || config.cache.ttl > 168) {
            errors.push('cache.ttl must be a number between 1 and 168 (hours)');
        }
    }
    return { valid: errors.length === 0, errors };
}
//# sourceMappingURL=schemas.js.map