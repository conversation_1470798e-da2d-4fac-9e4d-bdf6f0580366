#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const logger_1 = require("./utils/logger");
const manager_1 = require("./config/manager");
const wizard_1 = require("./cli/wizard");
const progress_1 = require("./cli/progress");
const analyzer_1 = require("./core/analyzer");
const scanner_1 = require("./git/scanner");
const generator_1 = require("./report/generator");
const chalk_1 = __importDefault(require("chalk"));
const packageJson = require('../package.json');
class AIReviewCLI {
    constructor() {
        this.program = new commander_1.Command();
        this.configManager = manager_1.ConfigManager.getInstance();
        this.setupCommands();
    }
    setupCommands() {
        this.program
            .name('ai-review')
            .description('🤖 AI-powered code review tool for Git changes')
            .version(packageJson.version)
            .addHelpText('after', this.getGlobalHelpText());
        // 主分析命令
        this.program
            .command('analyze')
            .description('Analyze Git staged changes')
            .option('-m, --mode <mode>', 'Analysis mode: "quick" for fast checks, "full" for comprehensive analysis', 'full')
            .option('-o, --output <format>', 'Output format: "terminal" for console, "html" for web report, "json" for structured data', 'terminal')
            .option('-c, --config <path>', 'Path to custom configuration file (default: .ai-review.json)')
            .option('-v, --verbose', 'Enable detailed logging and progress information', false)
            .option('--no-cache', 'Skip cache and force fresh analysis of all files', false)
            .option('--staged', 'Analyze only staged files (git add) - default behavior', true)
            .option('--all', 'Analyze all modified files, including unstaged changes', false)
            .addHelpText('after', this.getAnalyzeHelpText())
            .action(async (options) => {
            await this.runAnalysis(options);
        });
        // 配置管理命令 - 增强版
        this.program
            .command('config')
            .description('Manage AI Code Review configuration')
            .option('--init', 'Initialize configuration with interactive wizard')
            .option('--wizard', 'Start interactive configuration wizard')
            .option('--template', 'Generate a configuration template file')
            .option('--show', 'Display current configuration (API keys hidden)')
            .option('--validate', 'Validate current configuration')
            .option('--reset', 'Reset configuration to defaults')
            .addHelpText('after', this.getConfigHelpText())
            .action(async (options) => {
            await this.manageConfig(options);
        });
        // 缓存管理命令
        this.program
            .command('cache')
            .description('Manage analysis cache for performance')
            .option('--clear', 'Clear all cached analysis results')
            .option('--info', 'Show cache usage statistics and information')
            .option('--optimize', 'Optimize cache storage by removing old entries')
            .addHelpText('after', this.getCacheHelpText())
            .action(async (options) => {
            await this.manageCache(options);
        });
        // 新增：健康检查命令
        this.program
            .command('doctor')
            .description('Diagnose system health and configuration issues')
            .addHelpText('after', this.getDoctorHelpText())
            .action(async () => {
            await this.runDoctorCheck();
        });
        // 默认命令（直接运行 ai-review）
        this.program
            .argument('[mode]', 'Analysis mode: "quick" or "full" (default: full)', 'full')
            .option('-o, --output <format>', 'Output format: "terminal", "html", or "json" (default: terminal)', 'terminal')
            .option('-c, --config <path>', 'Path to custom configuration file')
            .option('-v, --verbose', 'Enable detailed logging and progress information', false)
            .option('--no-cache', 'Skip cache and force fresh analysis', false)
            .addHelpText('after', this.getDefaultCommandHelpText())
            .action(async (mode, options) => {
            // 首次运行检查
            await this.checkFirstRun();
            const cliOptions = {
                mode: mode,
                ...options
            };
            await this.runAnalysis(cliOptions);
        });
    }
    /**
     * 检查是否首次运行
     */
    async checkFirstRun() {
        const configPath = path.join(process.cwd(), '.ai-review.json');
        const homeConfigPath = path.join(require('os').homedir(), '.ai-review.json');
        if (!await fs.pathExists(configPath) && !await fs.pathExists(homeConfigPath)) {
            console.log(chalk_1.default.yellow('🎉 Welcome to AI Code Review!'));
            console.log(chalk_1.default.gray('It looks like this is your first time running AI Code Review.\n'));
            const runWizard = await progress_1.ConfirmDialog.confirm('Would you like to run the configuration wizard?', true);
            if (runWizard) {
                const wizard = new wizard_1.ConfigWizard();
                const config = await wizard.startWizard({ interactive: true });
                await wizard.saveConfiguration(config);
                wizard.displayConfigSummary(config);
                console.log(chalk_1.default.green('\n✨ Setup complete! You can now run code analysis.'));
                console.log(chalk_1.default.gray('Tip: Use "ai-review analyze" or just "ai-review" to start analyzing.\n'));
            }
            else {
                console.log(chalk_1.default.blue('ℹ You can run "ai-review config --wizard" anytime to configure AI Code Review.\n'));
            }
        }
    }
    async runAnalysis(options) {
        const spinner = new progress_1.SpinnerProgress();
        try {
            // 设置日志级别
            if (options.verbose) {
                logger_1.logger.setVerbose(true);
                logger_1.logger.setLogLevel('debug');
            }
            // 显示欢迎信息
            this.showWelcomeHeader(options);
            // 环境检查
            spinner.start('Checking environment...');
            if (!await this.isGitRepository()) {
                spinner.fail('Not a Git repository');
                console.log(chalk_1.default.red('❌ This command must be run inside a Git repository.'));
                console.log(chalk_1.default.gray('💡 Initialize a Git repository with: git init\n'));
                process.exit(1);
            }
            spinner.succeed('Environment check passed');
            // 加载配置
            spinner.start('Loading configuration...');
            let config;
            try {
                config = await this.configManager.loadConfig(options.config);
                spinner.succeed('Configuration loaded');
            }
            catch (error) {
                spinner.fail('Configuration error');
                console.log(chalk_1.default.red(`❌ Configuration Error: ${error}`));
                if (error instanceof Error && error.message.includes('ENOENT')) {
                    console.log(chalk_1.default.yellow('💡 Configuration file not found. Available options:'));
                    console.log(chalk_1.default.gray('  • Run "ai-review config --wizard" to create configuration interactively'));
                    console.log(chalk_1.default.gray('  • Run "ai-review config --template" to generate a template file'));
                    console.log(chalk_1.default.gray('  • Create .ai-review.json manually in your project directory'));
                }
                else if (error instanceof Error && error.message.includes('JSON')) {
                    console.log(chalk_1.default.yellow('💡 Configuration file has syntax errors:'));
                    console.log(chalk_1.default.gray('  • Check for missing commas, brackets, or quotes'));
                    console.log(chalk_1.default.gray('  • Validate JSON syntax online or with your editor'));
                    console.log(chalk_1.default.gray('  • Run "ai-review config --template" to see correct format'));
                }
                else {
                    console.log(chalk_1.default.yellow('💡 Fix configuration issues:'));
                    console.log(chalk_1.default.gray('  • Run "ai-review config --validate" to check for problems'));
                    console.log(chalk_1.default.gray('  • Run "ai-review config --wizard" to reconfigure'));
                    console.log(chalk_1.default.gray('  • Run "ai-review doctor" to diagnose system issues'));
                }
                console.log();
                process.exit(1);
            }
            // API密钥检查
            if (!config.apiKey && config.aiProvider !== 'local') {
                const envKey = this.getEnvironmentAPIKey(config.aiProvider);
                if (!envKey) {
                    console.log(chalk_1.default.red(`❌ API key required for ${config.aiProvider.toUpperCase()}`));
                    console.log(chalk_1.default.yellow('🔑 API Key Configuration Options:'));
                    console.log(chalk_1.default.gray(`  • Set ${this.getEnvVarName(config.aiProvider)} environment variable:`));
                    console.log(chalk_1.default.cyan(`    export ${this.getEnvVarName(config.aiProvider)}="your-api-key-here"`));
                    console.log(chalk_1.default.gray(`  • Add apiKey to .ai-review.json config file`));
                    console.log(chalk_1.default.gray(`  • Run "ai-review config --wizard" to configure interactively`));
                    if (config.aiProvider === 'openai') {
                        console.log(chalk_1.default.blue('\n💡 Get OpenAI API key: https://platform.openai.com/api-keys'));
                    }
                    else if (config.aiProvider === 'claude') {
                        console.log(chalk_1.default.blue('\n💡 Get Anthropic API key: https://console.anthropic.com/'));
                    }
                    console.log();
                    process.exit(1);
                }
                config.apiKey = envKey;
            }
            // 初始化分析组件
            const taskProgress = new progress_1.TaskProgress();
            taskProgress.addTask('Scanning Git changes');
            taskProgress.addTask('Analyzing code');
            taskProgress.addTask('Generating reports');
            taskProgress.startNext('Scanning Git changes...');
            const gitScanner = new scanner_1.GitScanner();
            const changes = options.all ?
                await gitScanner.getAllModifiedFiles() :
                await gitScanner.getStagedFiles();
            if (changes.length === 0) {
                taskProgress.failCurrentTask();
                console.log(chalk_1.default.yellow('⚠ No files to analyze'));
                if (options.all) {
                    console.log(chalk_1.default.gray('💡 No modified files found. Here\'s what you can do:'));
                    console.log(chalk_1.default.gray('  • Make some changes to your code'));
                    console.log(chalk_1.default.gray('  • Check git status to see repository state'));
                    console.log(chalk_1.default.gray('  • Use --staged to analyze only staged files'));
                }
                else {
                    console.log(chalk_1.default.gray('💡 No staged files found. Here\'s what you can do:'));
                    console.log(chalk_1.default.gray('  • Stage files with: git add <files>'));
                    console.log(chalk_1.default.gray('  • Stage all changes: git add .'));
                    console.log(chalk_1.default.gray('  • Use --all to analyze unstaged changes'));
                    console.log(chalk_1.default.gray('  • Check what\'s available: git status'));
                }
                console.log();
                return;
            }
            taskProgress.completeCurrentTask();
            console.log(chalk_1.default.blue(`📁 Found ${changes.length} file(s) to analyze`));
            // 执行分析
            taskProgress.startNext('Analyzing code with AI...');
            const analysisEngine = new analyzer_1.AnalysisEngine(config);
            const analysisResults = await analysisEngine.analyzeChanges(changes);
            taskProgress.completeCurrentTask();
            // 计算统计信息
            const totalIssues = analysisResults.reduce((sum, result) => sum + result.issues.length, 0);
            if (totalIssues === 0) {
                console.log(chalk_1.default.green('🎉 Great! No issues found in the analyzed code.'));
                console.log(chalk_1.default.gray('Your code looks good! Keep up the excellent work.\n'));
                return;
            }
            // 生成报告
            taskProgress.startNext('Generating reports...');
            const reportGenerator = new generator_1.ReportGenerator(config, {
                outputDir: path.join(process.cwd(), 'reports'),
                formats: [options.output || config.output?.format || 'terminal'],
                projectName: path.basename(process.cwd())
            });
            // 增强问题数据
            const enhancedIssues = analysisResults.flatMap(result => result.issues.map(issue => ({
                ...issue,
                id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                filePath: result.file,
                context: {
                    surroundingLines: [],
                    complexity: 1
                },
                blame: null,
                fixSuggestions: [],
                relatedIssues: [],
                priority: this.calculatePriority(issue)
            })));
            const reportSummary = await reportGenerator.generateAllReports(enhancedIssues, [], analysisResults);
            taskProgress.completeCurrentTask();
            taskProgress.complete();
            // 显示结果摘要
            this.showAnalysisSummary(analysisResults, reportSummary);
            await analysisEngine.cleanup();
        }
        catch (error) {
            spinner.fail('Analysis failed');
            logger_1.logger.error(`❌ Analysis failed: ${error}`);
            if (options.verbose) {
                console.log(chalk_1.default.gray('\nError details:'));
                console.error(error);
            }
            console.log(chalk_1.default.yellow('\n💡 Troubleshooting tips:'));
            console.log(chalk_1.default.gray('  • Check your internet connection'));
            console.log(chalk_1.default.gray('  • Verify your API key is correct'));
            console.log(chalk_1.default.gray('  • Run "ai-review doctor" to diagnose issues'));
            console.log(chalk_1.default.gray('  • Use --verbose flag for more details\n'));
            process.exit(1);
        }
    }
    showWelcomeHeader(options) {
        console.log(chalk_1.default.blue.bold('🤖 AI Code Review'));
        console.log(chalk_1.default.gray(`Version ${packageJson.version} • Mode: ${options.mode} • Output: ${options.output}`));
        console.log(chalk_1.default.gray('─'.repeat(50)));
    }
    showAnalysisSummary(results, reportSummary) {
        const totalIssues = results.reduce((sum, result) => sum + result.issues.length, 0);
        const severityCount = results.reduce((acc, result) => {
            result.issues.forEach((issue) => {
                const severity = issue.severity;
                acc[severity] = (acc[severity] || 0) + 1;
            });
            return acc;
        }, {});
        console.log(chalk_1.default.blue.bold('\n📊 Analysis Summary'));
        console.log(chalk_1.default.gray('─'.repeat(30)));
        console.log(`${chalk_1.default.yellow('Files analyzed:')} ${results.length}`);
        console.log(`${chalk_1.default.yellow('Total issues:')} ${totalIssues}`);
        if (severityCount['Critical'])
            console.log(`${chalk_1.default.red('Critical:')} ${severityCount['Critical']}`);
        if (severityCount['High'])
            console.log(`${chalk_1.default.red('High:')} ${severityCount['High']}`);
        if (severityCount['Medium'])
            console.log(`${chalk_1.default.yellow('Medium:')} ${severityCount['Medium']}`);
        if (severityCount['Low'])
            console.log(`${chalk_1.default.blue('Low:')} ${severityCount['Low']}`);
        if (reportSummary.generatedReports.length > 0) {
            console.log('\n📋 Reports generated:');
            reportSummary.generatedReports.forEach((report) => {
                if (report.success) {
                    console.log(chalk_1.default.green(`✓ ${report.format}: ${report.path}`));
                }
                else {
                    console.log(chalk_1.default.red(`✗ ${report.format}: failed`));
                }
            });
        }
        console.log();
    }
    calculatePriority(issue) {
        const severityWeights = {
            'Critical': 100,
            'High': 75,
            'Medium': 50,
            'Low': 25
        };
        const typeWeights = {
            'Security': 20,
            'Logic': 15,
            'Performance': 10,
            'Style': 5,
            'Business': 12
        };
        const severityWeight = severityWeights[issue.severity] || 25;
        const typeWeight = typeWeights[issue.type] || 10;
        return severityWeight + typeWeight;
    }
    async manageConfig(options) {
        try {
            if (options.init || options.wizard) {
                const wizard = new wizard_1.ConfigWizard();
                const config = await wizard.startWizard({ interactive: true });
                const configPath = await wizard.saveConfiguration(config);
                wizard.displayConfigSummary(config);
                // 验证配置
                const validation = wizard.validateConfiguration(config);
                if (validation.valid) {
                    console.log(chalk_1.default.green('✅ Configuration is valid and ready to use!'));
                }
                else {
                    console.log(chalk_1.default.yellow('⚠ Configuration warnings:'));
                    validation.errors.forEach(error => {
                        console.log(chalk_1.default.gray(`  • ${error}`));
                    });
                }
            }
            else if (options.template) {
                await this.generateTemplate();
            }
            else if (options.show) {
                await this.showConfig();
            }
            else if (options.validate) {
                await this.validateConfig();
            }
            else if (options.reset) {
                await this.resetConfig();
            }
            else {
                console.log(chalk_1.default.yellow('🔧 AI Code Review Configuration Manager'));
                console.log(chalk_1.default.gray('Use one of the following options to manage your configuration:\n'));
                console.log(chalk_1.default.cyan('Setup Commands:'));
                console.log('  --wizard     Interactive configuration setup (recommended for first-time users)');
                console.log('  --init       Same as --wizard, initialize configuration');
                console.log('  --template   Generate a configuration template file (.ai-review.json)');
                console.log(chalk_1.default.cyan('\nManagement Commands:'));
                console.log('  --show       Display current configuration (API keys are hidden)');
                console.log('  --validate   Check configuration for errors and warnings');
                console.log('  --reset      Reset configuration to default settings');
                console.log(chalk_1.default.cyan('\nExamples:'));
                console.log(chalk_1.default.gray('  ai-review config --wizard      # Interactive first-time setup'));
                console.log(chalk_1.default.gray('  ai-review config --show        # View current settings'));
                console.log(chalk_1.default.gray('  ai-review config --validate    # Check for configuration issues'));
                console.log(chalk_1.default.blue('\n💡 For detailed help: ai-review config --help'));
            }
        }
        catch (error) {
            logger_1.logger.error(`Config management failed: ${error}`);
            process.exit(1);
        }
    }
    async manageCache(options) {
        try {
            const CacheManager = (await Promise.resolve().then(() => __importStar(require('./core/cache')))).CacheManager;
            const cacheManager = new CacheManager();
            if (options.clear) {
                const confirmed = await progress_1.ConfirmDialog.confirm('Are you sure you want to clear all cache?', false);
                if (confirmed) {
                    await cacheManager.clearAllCache();
                    console.log(chalk_1.default.green('✅ Cache cleared successfully'));
                }
                else {
                    console.log(chalk_1.default.gray('Cache clearing cancelled'));
                }
            }
            else if (options.info) {
                await this.showCacheInfo(cacheManager);
            }
            else if (options.optimize) {
                console.log(chalk_1.default.blue('🔧 Optimizing cache...'));
                await cacheManager.optimizeCache();
                console.log(chalk_1.default.green('✅ Cache optimization completed'));
            }
            else {
                console.log(chalk_1.default.yellow('💾 AI Code Review Cache Manager'));
                console.log(chalk_1.default.gray('Manage analysis cache to improve performance and reduce costs:\n'));
                console.log(chalk_1.default.cyan('Cache Commands:'));
                console.log('  --info       Show cache statistics, size, and usage information');
                console.log('  --clear      Remove all cached analysis results (use with caution)');
                console.log('  --optimize   Clean up old entries and optimize storage');
                console.log(chalk_1.default.cyan('\nCache Benefits:'));
                console.log(chalk_1.default.gray('  • Faster re-analysis of unchanged code'));
                console.log(chalk_1.default.gray('  • Reduced API costs and usage'));
                console.log(chalk_1.default.gray('  • Improved development workflow'));
                console.log(chalk_1.default.cyan('\nExamples:'));
                console.log(chalk_1.default.gray('  ai-review cache --info        # View cache statistics'));
                console.log(chalk_1.default.gray('  ai-review cache --optimize    # Clean up old entries'));
                console.log(chalk_1.default.gray('  ai-review cache --clear       # Clear entire cache'));
                console.log(chalk_1.default.blue('\n💡 Cache location: ./.ai-review-cache/'));
                console.log(chalk_1.default.blue('💡 For detailed help: ai-review cache --help'));
            }
        }
        catch (error) {
            logger_1.logger.error(`Cache management failed: ${error}`);
            process.exit(1);
        }
    }
    async runDoctorCheck() {
        console.log(chalk_1.default.blue.bold('🩺 AI Code Review Health Check'));
        console.log(chalk_1.default.gray('Diagnosing system configuration and connectivity...\n'));
        const tasks = new progress_1.TaskProgress();
        tasks.addTask('Environment check');
        tasks.addTask('Git repository check');
        tasks.addTask('Configuration validation');
        tasks.addTask('API connectivity test');
        tasks.addTask('Cache system check');
        let hasIssues = false;
        // 环境检查
        tasks.startNext('Checking Node.js environment...');
        const nodeVersion = process.version;
        console.log(`  Node.js: ${nodeVersion}`);
        if (parseInt(nodeVersion.substring(1)) < 16) {
            console.log(chalk_1.default.red('  ⚠ Node.js 16+ recommended'));
            hasIssues = true;
        }
        tasks.completeCurrentTask();
        // Git检查
        tasks.startNext('Checking Git repository...');
        if (await this.isGitRepository()) {
            console.log(chalk_1.default.green('  ✓ Git repository detected'));
        }
        else {
            console.log(chalk_1.default.red('  ✗ Not a Git repository'));
            hasIssues = true;
        }
        tasks.completeCurrentTask();
        // 配置检查
        tasks.startNext('Validating configuration...');
        try {
            const config = await this.configManager.loadConfig();
            console.log(chalk_1.default.green('  ✓ Configuration loaded successfully'));
            const wizard = new wizard_1.ConfigWizard();
            const validation = wizard.validateConfiguration(config);
            if (validation.valid) {
                console.log(chalk_1.default.green('  ✓ Configuration is valid'));
            }
            else {
                console.log(chalk_1.default.red('  ✗ Configuration issues found:'));
                validation.errors.forEach(error => {
                    console.log(chalk_1.default.gray(`    • ${error}`));
                });
                hasIssues = true;
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`  ✗ Configuration error: ${error}`));
            hasIssues = true;
        }
        tasks.completeCurrentTask();
        // API连接测试
        tasks.startNext('Testing API connectivity...');
        try {
            const config = await this.configManager.loadConfig();
            if (config.apiKey || this.getEnvironmentAPIKey(config.aiProvider)) {
                // 这里可以添加实际的API连接测试
                console.log(chalk_1.default.green('  ✓ API key configured'));
            }
            else {
                console.log(chalk_1.default.red('  ✗ No API key found'));
                hasIssues = true;
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`  ✗ API test failed: ${error}`));
            hasIssues = true;
        }
        tasks.completeCurrentTask();
        // 缓存检查
        tasks.startNext('Checking cache system...');
        try {
            const CacheManager = (await Promise.resolve().then(() => __importStar(require('./core/cache')))).CacheManager;
            const cacheManager = new CacheManager();
            const stats = await cacheManager.getCacheStats();
            console.log(chalk_1.default.green('  ✓ Cache system operational'));
            console.log(chalk_1.default.gray(`    Files: ${stats.totalEntries}, Size: ${stats.totalSize.toFixed(2)} MB`));
        }
        catch (error) {
            console.log(chalk_1.default.yellow(`  ⚠ Cache check failed: ${error}`));
        }
        tasks.completeCurrentTask();
        tasks.complete();
        // 显示结果
        if (hasIssues) {
            console.log(chalk_1.default.red('\n❌ Issues detected. Please address the above problems.'));
            console.log(chalk_1.default.yellow('💡 Run "ai-review config --wizard" to fix configuration issues.'));
        }
        else {
            console.log(chalk_1.default.green('\n✅ All checks passed! Your AI Code Review setup is healthy.'));
        }
    }
    async validateConfig() {
        try {
            const config = await this.configManager.loadConfig();
            const validation = await this.configManager.validateConfigurationAdvanced(config);
            console.log(chalk_1.default.blue.bold('🔍 Advanced Configuration Validation'));
            console.log(chalk_1.default.gray('─'.repeat(40)));
            if (validation.valid) {
                console.log(chalk_1.default.green('✅ Configuration is valid!'));
                if (validation.warnings.length > 0) {
                    console.log(chalk_1.default.yellow('\n⚠ Warnings:'));
                    validation.warnings.forEach(warning => {
                        console.log(chalk_1.default.yellow(`  • ${warning}`));
                    });
                }
                if (validation.suggestions.length > 0) {
                    console.log(chalk_1.default.blue('\n💡 Suggestions:'));
                    validation.suggestions.forEach(suggestion => {
                        console.log(chalk_1.default.gray(`  • ${suggestion}`));
                    });
                }
            }
            else {
                console.log(chalk_1.default.red('❌ Configuration has issues:'));
                validation.errors.forEach(error => {
                    console.log(chalk_1.default.red(`  • ${error}`));
                });
                if (validation.warnings.length > 0) {
                    console.log(chalk_1.default.yellow('\n⚠ Additional warnings:'));
                    validation.warnings.forEach(warning => {
                        console.log(chalk_1.default.yellow(`  • ${warning}`));
                    });
                }
                // 提供自动修复选项
                const autoFix = await progress_1.ConfirmDialog.confirm('Would you like to auto-fix these issues?', true);
                if (autoFix) {
                    const { fixed, changes } = await this.configManager.autoFixConfiguration(config);
                    if (changes.length > 0) {
                        console.log(chalk_1.default.blue('\n🔧 Applied fixes:'));
                        changes.forEach(change => {
                            console.log(chalk_1.default.green(`  ✓ ${change}`));
                        });
                        await this.configManager.saveConfig(fixed);
                        console.log(chalk_1.default.green('\n✅ Configuration updated successfully!'));
                    }
                    else {
                        console.log(chalk_1.default.yellow('\n⚠ No automatic fixes available'));
                    }
                }
                console.log(chalk_1.default.yellow('\n💡 Run "ai-review config --wizard" to fix remaining issues interactively.'));
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`Configuration validation failed: ${error}`));
        }
    }
    async resetConfig() {
        const confirmed = await progress_1.ConfirmDialog.confirm('This will reset your configuration to defaults. Continue?', false);
        if (confirmed) {
            const configPath = path.join(process.cwd(), '.ai-review.json');
            if (await fs.pathExists(configPath)) {
                await fs.remove(configPath);
            }
            const wizard = new wizard_1.ConfigWizard();
            const defaultConfig = await wizard.startWizard({ interactive: false });
            await wizard.saveConfiguration(defaultConfig);
            console.log(chalk_1.default.green('✅ Configuration reset to defaults'));
            console.log(chalk_1.default.gray('Run "ai-review config --wizard" to customize your settings.'));
        }
        else {
            console.log(chalk_1.default.gray('Configuration reset cancelled'));
        }
    }
    async isGitRepository() {
        try {
            const gitDir = path.join(process.cwd(), '.git');
            return await fs.pathExists(gitDir);
        }
        catch {
            return false;
        }
    }
    async initializeConfig() {
        const configPath = path.join(process.cwd(), '.ai-review.json');
        if (await fs.pathExists(configPath)) {
            logger_1.logger.warn('Configuration file already exists');
            return;
        }
        await this.configManager.loadConfig();
        await this.configManager.saveConfig(this.configManager.getConfig(), configPath);
        logger_1.logger.success(`Configuration initialized: ${configPath}`);
    }
    async generateTemplate() {
        try {
            // 检测项目特定配置
            const projectInfo = await this.configManager.generateProjectSpecificConfig();
            console.log(chalk_1.default.blue.bold('🏗️ Generating Configuration Template'));
            console.log(chalk_1.default.gray('─'.repeat(40)));
            if (projectInfo.detectedFeatures.length > 0) {
                console.log(chalk_1.default.green('🔍 Detected project features:'));
                projectInfo.detectedFeatures.forEach(feature => {
                    console.log(chalk_1.default.gray(`  • ${feature}`));
                });
                console.log();
            }
            if (projectInfo.recommendations.length > 0) {
                console.log(chalk_1.default.blue('💡 Recommendations for your project:'));
                projectInfo.recommendations.forEach(rec => {
                    console.log(chalk_1.default.gray(`  • ${rec}`));
                });
                console.log();
            }
            // 生成模板
            await this.configManager.generateConfigTemplate();
            console.log(chalk_1.default.green('✅ Configuration template generated: .ai-review.template.json'));
            console.log(chalk_1.default.gray('💡 Edit the template and save it as .ai-review.json to use it'));
            if (projectInfo.detectedFeatures.length > 0) {
                console.log(chalk_1.default.blue('\n🚀 Quick start:'));
                console.log(chalk_1.default.gray('  1. Review the generated template'));
                console.log(chalk_1.default.gray('  2. Copy it to .ai-review.json'));
                console.log(chalk_1.default.gray('  3. Add your API key or set environment variable'));
                console.log(chalk_1.default.gray('  4. Run "ai-review config --validate" to check'));
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`Template generation failed: ${error}`));
        }
    }
    async showConfig() {
        try {
            const config = await this.configManager.loadConfig();
            console.log(chalk_1.default.blue.bold('\n📋 Current Configuration:'));
            console.log(chalk_1.default.gray('─'.repeat(50)));
            const configDisplay = { ...config };
            if (configDisplay.apiKey) {
                configDisplay.apiKey = '***hidden***';
            }
            console.log(JSON.stringify(configDisplay, null, 2));
        }
        catch (error) {
            console.log(chalk_1.default.red(`Failed to load configuration: ${error}`));
        }
    }
    async showCacheInfo(cacheManager) {
        try {
            const stats = await cacheManager.getCacheStats();
            console.log(chalk_1.default.blue.bold('\n📦 Cache Information'));
            console.log(chalk_1.default.gray('─'.repeat(30)));
            console.log(`${chalk_1.default.yellow('Total entries:')} ${stats.totalEntries}`);
            console.log(`${chalk_1.default.yellow('Total size:')} ${stats.totalSize.toFixed(2)} MB`);
            console.log(`${chalk_1.default.yellow('Hit rate:')} ${stats.hitRate.toFixed(1)}%`);
            if (stats.oldestEntry) {
                console.log(`${chalk_1.default.yellow('Oldest entry:')} ${stats.oldestEntry.toLocaleString()}`);
            }
            if (stats.newestEntry) {
                console.log(`${chalk_1.default.yellow('Newest entry:')} ${stats.newestEntry.toLocaleString()}`);
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`Failed to get cache info: ${error}`));
        }
    }
    getEnvironmentAPIKey(provider) {
        switch (provider) {
            case 'openai':
                return process.env.OPENAI_API_KEY;
            case 'claude':
                return process.env.ANTHROPIC_API_KEY;
            default:
                return process.env.AI_REVIEW_API_KEY;
        }
    }
    getEnvVarName(provider) {
        switch (provider) {
            case 'openai':
                return 'OPENAI_API_KEY';
            case 'claude':
                return 'ANTHROPIC_API_KEY';
            default:
                return 'AI_REVIEW_API_KEY';
        }
    }
    getGlobalHelpText() {
        return `
${chalk_1.default.gray('Examples:')}
  ${chalk_1.default.cyan('ai-review')}                    # Quick start with default settings
  ${chalk_1.default.cyan('ai-review full')}               # Comprehensive analysis mode
  ${chalk_1.default.cyan('ai-review analyze --all')}      # Analyze all modified files
  ${chalk_1.default.cyan('ai-review config --wizard')}    # Interactive configuration setup
  ${chalk_1.default.cyan('ai-review doctor')}             # Check system health

${chalk_1.default.gray('Getting Started:')}
  1. Run ${chalk_1.default.cyan('ai-review config --wizard')} to set up configuration
  2. Stage your code changes: ${chalk_1.default.cyan('git add <files>')}
  3. Run ${chalk_1.default.cyan('ai-review')} to analyze your changes

${chalk_1.default.gray('Learn more:')} https://github.com/your-repo/ai-code-review
`;
    }
    getAnalyzeHelpText() {
        return `
${chalk_1.default.gray('Analysis Modes:')}
  ${chalk_1.default.cyan('quick')}  - Fast analysis focusing on critical issues only
  ${chalk_1.default.cyan('full')}   - Comprehensive analysis including style and best practices

${chalk_1.default.gray('Output Formats:')}
  ${chalk_1.default.cyan('terminal')} - Colorful console output (default)
  ${chalk_1.default.cyan('html')}     - Interactive web report with charts
  ${chalk_1.default.cyan('json')}     - Structured data for integration

${chalk_1.default.gray('Examples:')}
  ${chalk_1.default.cyan('ai-review analyze')}                        # Default: full analysis, terminal output
  ${chalk_1.default.cyan('ai-review analyze -m quick')}               # Quick analysis only
  ${chalk_1.default.cyan('ai-review analyze -o html')}                # Generate HTML report
  ${chalk_1.default.cyan('ai-review analyze --all')}                  # Analyze all modified files
  ${chalk_1.default.cyan('ai-review analyze --no-cache -v')}          # Fresh analysis with verbose output

${chalk_1.default.gray('Before running analysis:')}
  • Stage your changes: ${chalk_1.default.cyan('git add <files>')}
  • Or use ${chalk_1.default.cyan('--all')} to analyze unstaged changes
`;
    }
    getConfigHelpText() {
        return `
${chalk_1.default.gray('Configuration Commands:')}
  ${chalk_1.default.cyan('--wizard')}    - Interactive setup wizard (recommended for first-time users)
  ${chalk_1.default.cyan('--show')}      - View current configuration (API keys are hidden)
  ${chalk_1.default.cyan('--validate')}  - Check configuration for errors
  ${chalk_1.default.cyan('--template')}  - Generate a template configuration file
  ${chalk_1.default.cyan('--reset')}     - Reset to default settings

${chalk_1.default.gray('Configuration Files:')}
  • Project: ${chalk_1.default.gray('./.ai-review.json')} (project-specific settings)
  • Global: ${chalk_1.default.gray('~/.ai-review.json')} (user-wide defaults)

${chalk_1.default.gray('Examples:')}
  ${chalk_1.default.cyan('ai-review config --wizard')}      # First-time setup
  ${chalk_1.default.cyan('ai-review config --show')}        # View current settings
  ${chalk_1.default.cyan('ai-review config --validate')}    # Check for issues

${chalk_1.default.gray('Environment Variables:')}
  • ${chalk_1.default.cyan('OPENAI_API_KEY')} - For OpenAI GPT models
  • ${chalk_1.default.cyan('ANTHROPIC_API_KEY')} - For Claude models
`;
    }
    getCacheHelpText() {
        return `
${chalk_1.default.gray('Cache Commands:')}
  ${chalk_1.default.cyan('--info')}      - Show cache statistics and usage
  ${chalk_1.default.cyan('--clear')}     - Remove all cached analysis results
  ${chalk_1.default.cyan('--optimize')}  - Clean up old and unused cache entries

${chalk_1.default.gray('Cache Benefits:')}
  • Faster re-analysis of unchanged code
  • Reduced API costs and usage
  • Improved development workflow

${chalk_1.default.gray('Cache Location:')}
  ${chalk_1.default.gray('./.ai-review-cache/')} (project-specific cache directory)

${chalk_1.default.gray('Examples:')}
  ${chalk_1.default.cyan('ai-review cache --info')}         # View cache statistics
  ${chalk_1.default.cyan('ai-review cache --clear')}        # Clear all cache
  ${chalk_1.default.cyan('ai-review cache --optimize')}     # Clean up cache
`;
    }
    getDoctorHelpText() {
        return `
${chalk_1.default.gray('Health Check includes:')}
  • Node.js version compatibility
  • Git repository status
  • Configuration validation
  • API connectivity testing
  • Cache system health

${chalk_1.default.gray('Common Issues Detected:')}
  • Missing or invalid configuration
  • Network connectivity problems
  • API key configuration issues
  • Git repository problems
  • File permission issues

${chalk_1.default.gray('When to use:')}
  • First-time setup verification
  • Troubleshooting analysis failures
  • Before important code reviews
  • After configuration changes

${chalk_1.default.gray('Example:')}
  ${chalk_1.default.cyan('ai-review doctor')}               # Run full health check
`;
    }
    getDefaultCommandHelpText() {
        return `
${chalk_1.default.gray('Quick Start:')}
  ${chalk_1.default.cyan('ai-review')}           # Analyze staged files with default settings
  ${chalk_1.default.cyan('ai-review quick')}     # Fast analysis mode
  ${chalk_1.default.cyan('ai-review full')}      # Comprehensive analysis

${chalk_1.default.gray('This is equivalent to:')}
  ${chalk_1.default.cyan('ai-review analyze [mode] [options]')}

${chalk_1.default.gray('First time?')} Run ${chalk_1.default.cyan('ai-review config --wizard')} to set up configuration.
`;
    }
    async run() {
        try {
            await this.program.parseAsync(process.argv);
        }
        catch (error) {
            logger_1.logger.error(`CLI error: ${error}`);
            process.exit(1);
        }
    }
}
// 运行CLI
const cli = new AIReviewCLI();
cli.run().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
});
//# sourceMappingURL=cli.js.map