export interface ProgressOptions {
    total: number;
    width?: number;
    format?: string;
    clear?: boolean;
}
export declare class ProgressBar {
    private current;
    private total;
    private width;
    private format;
    private clear;
    private startTime;
    constructor(options: ProgressOptions);
    /**
     * 更新进度
     */
    update(current: number, info?: any): void;
    /**
     * 递增进度
     */
    tick(info?: any): void;
    /**
     * 完成进度条
     */
    complete(): void;
    /**
     * 渲染进度条
     */
    private render;
    /**
     * 格式化时间
     */
    private formatTime;
}
export declare class SpinnerProgress {
    private frames;
    private currentFrame;
    private interval;
    private text;
    /**
     * 启动加载动画
     */
    start(text?: string): void;
    /**
     * 更新文本
     */
    updateText(text: string): void;
    /**
     * 停止动画
     */
    stop(finalText?: string): void;
    /**
     * 成功完成
     */
    succeed(text?: string): void;
    /**
     * 失败停止
     */
    fail(text?: string): void;
    /**
     * 警告停止
     */
    warn(text?: string): void;
    /**
     * 信息停止
     */
    info(text?: string): void;
}
/**
 * 简化的任务进度管理器
 */
export declare class TaskProgress {
    private tasks;
    private currentTask;
    /**
     * 添加任务
     */
    addTask(name: string): void;
    /**
     * 开始下一个任务
     */
    startNext(name?: string): void;
    /**
     * 完成当前任务
     */
    completeCurrentTask(): void;
    /**
     * 失败当前任务
     */
    failCurrentTask(): void;
    /**
     * 渲染任务列表
     */
    private render;
    /**
     * 完成所有任务
     */
    complete(): void;
}
/**
 * 用户确认对话框
 */
export declare class ConfirmDialog {
    /**
     * 询问用户确认
     */
    static confirm(message: string, defaultValue?: boolean): Promise<boolean>;
    /**
     * 选择列表
     */
    static choose(message: string, choices: string[], defaultIndex?: number): Promise<number>;
}
//# sourceMappingURL=progress.d.ts.map