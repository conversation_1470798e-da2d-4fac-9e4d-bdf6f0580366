import { AIReviewConfig } from '../types';
export interface WizardOptions {
    skipExisting?: boolean;
    interactive?: boolean;
}
export declare class ConfigWizard {
    private rl;
    constructor();
    /**
     * 启动配置向导
     */
    startWizard(options?: WizardOptions): Promise<AIReviewConfig>;
    /**
     * 收集配置信息
     */
    private collectConfiguration;
    /**
     * 询问AI提供商
     */
    private askAIProvider;
    /**
     * 询问模型选择
     */
    private askModel;
    /**
     * 询问API密钥
     */
    private askAPIKey;
    /**
     * 询问自定义基础URL
     */
    private askBaseUrl;
    /**
     * 询问严格程度
     */
    private askStrictness;
    /**
     * 询问输出格式
     */
    private askOutputFormat;
    /**
     * 询问问题
     */
    private askQuestion;
    /**
     * 保存配置文件
     */
    saveConfiguration(config: AIReviewConfig, configPath?: string): Promise<string>;
    /**
     * 验证配置
     */
    validateConfiguration(config: AIReviewConfig): {
        valid: boolean;
        errors: string[];
    };
    /**
     * 获取环境变量中的API密钥
     */
    private getEnvironmentAPIKey;
    /**
     * 显示配置摘要
     */
    displayConfigSummary(config: AIReviewConfig): void;
}
//# sourceMappingURL=wizard.d.ts.map