"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfirmDialog = exports.TaskProgress = exports.SpinnerProgress = exports.ProgressBar = void 0;
const chalk_1 = __importDefault(require("chalk"));
class ProgressBar {
    constructor(options) {
        this.current = 0;
        this.total = options.total;
        this.width = options.width || 40;
        this.format = options.format || ':bar :percent :current/:total (:elapseds)';
        this.clear = options.clear !== false;
        this.startTime = Date.now();
    }
    /**
     * 更新进度
     */
    update(current, info) {
        this.current = Math.min(current, this.total);
        this.render(info);
    }
    /**
     * 递增进度
     */
    tick(info) {
        this.update(this.current + 1, info);
    }
    /**
     * 完成进度条
     */
    complete() {
        this.update(this.total);
        if (this.clear) {
            process.stdout.write('\r\x1b[K');
        }
        else {
            process.stdout.write('\n');
        }
    }
    /**
     * 渲染进度条
     */
    render(info) {
        const percent = Math.round((this.current / this.total) * 100);
        const elapsed = Date.now() - this.startTime;
        const elapsedStr = this.formatTime(elapsed);
        // 计算ETA
        const eta = this.current > 0 ? (elapsed / this.current) * (this.total - this.current) : 0;
        const etaStr = this.formatTime(eta);
        // 构建进度条
        const completed = Math.round((this.current / this.total) * this.width);
        const remaining = this.width - completed;
        const bar = chalk_1.default.green('█'.repeat(completed)) +
            chalk_1.default.gray('░'.repeat(remaining));
        // 格式化输出
        let output = this.format
            .replace(':bar', bar)
            .replace(':percent', `${percent}%`.padStart(4))
            .replace(':current', this.current.toString())
            .replace(':total', this.total.toString())
            .replace(':elapseds', elapsedStr)
            .replace(':eta', etaStr);
        if (info) {
            output += ` ${info}`;
        }
        // 清除当前行并输出
        process.stdout.write('\r\x1b[K' + output);
    }
    /**
     * 格式化时间
     */
    formatTime(ms) {
        const seconds = Math.floor(ms / 1000);
        if (seconds < 60) {
            return `${seconds}s`;
        }
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}m${remainingSeconds}s`;
    }
}
exports.ProgressBar = ProgressBar;
class SpinnerProgress {
    constructor() {
        this.frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
        this.currentFrame = 0;
        this.interval = null;
        this.text = '';
    }
    /**
     * 启动加载动画
     */
    start(text = 'Loading...') {
        this.text = text;
        this.interval = setInterval(() => {
            const frame = this.frames[this.currentFrame];
            process.stdout.write('\r\x1b[K' + chalk_1.default.cyan(frame) + ' ' + this.text);
            this.currentFrame = (this.currentFrame + 1) % this.frames.length;
        }, 100);
    }
    /**
     * 更新文本
     */
    updateText(text) {
        this.text = text;
    }
    /**
     * 停止动画
     */
    stop(finalText) {
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        if (finalText) {
            process.stdout.write('\r\x1b[K' + finalText + '\n');
        }
        else {
            process.stdout.write('\r\x1b[K');
        }
    }
    /**
     * 成功完成
     */
    succeed(text) {
        this.stop(chalk_1.default.green('✓ ') + (text || this.text));
    }
    /**
     * 失败停止
     */
    fail(text) {
        this.stop(chalk_1.default.red('✗ ') + (text || this.text));
    }
    /**
     * 警告停止
     */
    warn(text) {
        this.stop(chalk_1.default.yellow('⚠ ') + (text || this.text));
    }
    /**
     * 信息停止
     */
    info(text) {
        this.stop(chalk_1.default.blue('ℹ ') + (text || this.text));
    }
}
exports.SpinnerProgress = SpinnerProgress;
/**
 * 简化的任务进度管理器
 */
class TaskProgress {
    constructor() {
        this.tasks = [];
        this.currentTask = -1;
    }
    /**
     * 添加任务
     */
    addTask(name) {
        this.tasks.push({ name, status: 'pending' });
    }
    /**
     * 开始下一个任务
     */
    startNext(name) {
        if (this.currentTask >= 0) {
            this.tasks[this.currentTask].status = 'completed';
        }
        this.currentTask++;
        if (this.currentTask < this.tasks.length) {
            this.tasks[this.currentTask].status = 'running';
            if (name) {
                this.tasks[this.currentTask].name = name;
            }
        }
        this.render();
    }
    /**
     * 完成当前任务
     */
    completeCurrentTask() {
        if (this.currentTask >= 0 && this.currentTask < this.tasks.length) {
            this.tasks[this.currentTask].status = 'completed';
            this.render();
        }
    }
    /**
     * 失败当前任务
     */
    failCurrentTask() {
        if (this.currentTask >= 0 && this.currentTask < this.tasks.length) {
            this.tasks[this.currentTask].status = 'failed';
            this.render();
        }
    }
    /**
     * 渲染任务列表
     */
    render() {
        // 清除之前的输出
        if (this.tasks.length > 0) {
            process.stdout.write(`\r\x1b[${this.tasks.length}A\x1b[K`);
        }
        // 渲染所有任务
        this.tasks.forEach((task, index) => {
            let icon;
            let color;
            switch (task.status) {
                case 'pending':
                    icon = '○';
                    color = chalk_1.default.gray;
                    break;
                case 'running':
                    icon = '●';
                    color = chalk_1.default.blue;
                    break;
                case 'completed':
                    icon = '✓';
                    color = chalk_1.default.green;
                    break;
                case 'failed':
                    icon = '✗';
                    color = chalk_1.default.red;
                    break;
            }
            console.log(color(`${icon} ${task.name}`));
        });
    }
    /**
     * 完成所有任务
     */
    complete() {
        if (this.currentTask >= 0 && this.currentTask < this.tasks.length) {
            this.tasks[this.currentTask].status = 'completed';
        }
        this.render();
        console.log();
    }
}
exports.TaskProgress = TaskProgress;
/**
 * 用户确认对话框
 */
class ConfirmDialog {
    /**
     * 询问用户确认
     */
    static async confirm(message, defaultValue = true) {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        const suffix = defaultValue ? ' [Y/n]' : ' [y/N]';
        return new Promise((resolve) => {
            rl.question(message + suffix + ': ', (answer) => {
                rl.close();
                const response = answer.trim().toLowerCase();
                if (response === '') {
                    resolve(defaultValue);
                }
                else {
                    resolve(response === 'y' || response === 'yes');
                }
            });
        });
    }
    /**
     * 选择列表
     */
    static async choose(message, choices, defaultIndex = 0) {
        console.log(chalk_1.default.yellow(message));
        choices.forEach((choice, index) => {
            const marker = index === defaultIndex ? '>' : ' ';
            console.log(` ${marker} ${index + 1}. ${choice}`);
        });
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        return new Promise((resolve) => {
            rl.question(`Choose (1-${choices.length}) [${defaultIndex + 1}]: `, (answer) => {
                rl.close();
                const index = parseInt(answer.trim()) - 1;
                if (isNaN(index) || index < 0 || index >= choices.length) {
                    resolve(defaultIndex);
                }
                else {
                    resolve(index);
                }
            });
        });
    }
}
exports.ConfirmDialog = ConfirmDialog;
//# sourceMappingURL=progress.js.map