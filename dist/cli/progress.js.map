{"version": 3, "file": "progress.js", "sourceRoot": "", "sources": ["../../src/cli/progress.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAS1B,MAAa,WAAW;IAQtB,YAAY,OAAwB;QAP5B,YAAO,GAAW,CAAC,CAAC;QAQ1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,2CAA2C,CAAC;QAC5E,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,OAAe,EAAE,IAAU;QACvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,IAAU;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,IAAU;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;QAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE5C,QAAQ;QACR,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1F,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QAEpC,QAAQ;QACR,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;QACvE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAEzC,MAAM,GAAG,GAAG,eAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAClC,eAAK,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;QAE9C,QAAQ;QACR,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM;aACrB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;aACpB,OAAO,CAAC,UAAU,EAAE,GAAG,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;aAC9C,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;aAC5C,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;aACxC,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;aAChC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3B,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACvB,CAAC;QAED,WAAW;QACX,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,EAAU;QAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC;QACtC,IAAI,OAAO,GAAG,EAAE,EAAE,CAAC;YACjB,OAAO,GAAG,OAAO,GAAG,CAAC;QACvB,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;QACzC,MAAM,gBAAgB,GAAG,OAAO,GAAG,EAAE,CAAC;QACtC,OAAO,GAAG,OAAO,IAAI,gBAAgB,GAAG,CAAC;IAC3C,CAAC;CACF;AA3FD,kCA2FC;AAED,MAAa,eAAe;IAA5B;QACU,WAAM,GAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACtE,iBAAY,GAAW,CAAC,CAAC;QACzB,aAAQ,GAA0B,IAAI,CAAC;QACvC,SAAI,GAAW,EAAE,CAAC;IAgE5B,CAAC;IA9DC;;OAEG;IACI,KAAK,CAAC,OAAe,YAAY;QACtC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC7C,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QACnE,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;IAED;;OAEG;IACI,UAAU,CAAC,IAAY;QAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,SAAkB;QAC5B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC;QACtD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,OAAO,CAAC,IAAa;QAC1B,IAAI,CAAC,IAAI,CAAC,eAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,IAAa;QACvB,IAAI,CAAC,IAAI,CAAC,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,IAAa;QACvB,IAAI,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,IAAI,CAAC,IAAa;QACvB,IAAI,CAAC,IAAI,CAAC,eAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IACpD,CAAC;CACF;AApED,0CAoEC;AAED;;GAEG;AACH,MAAa,YAAY;IAAzB;QACU,UAAK,GAAoF,EAAE,CAAC;QAC5F,gBAAW,GAAW,CAAC,CAAC,CAAC;IA+FnC,CAAC;IA7FC;;OAEG;IACI,OAAO,CAAC,IAAY;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,SAAS,CAAC,IAAa;QAC5B,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACzC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,SAAS,CAAC;YAChD,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;YAC3C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC;YAClD,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,MAAM;QACZ,UAAU;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;QAC7D,CAAC;QAED,SAAS;QACT,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACjC,IAAI,IAAY,CAAC;YACjB,IAAI,KAAyB,CAAC;YAE9B,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;gBACpB,KAAK,SAAS;oBACZ,IAAI,GAAG,GAAG,CAAC;oBACX,KAAK,GAAG,eAAK,CAAC,IAAI,CAAC;oBACnB,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,GAAG,GAAG,CAAC;oBACX,KAAK,GAAG,eAAK,CAAC,IAAI,CAAC;oBACnB,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,GAAG,GAAG,CAAC;oBACX,KAAK,GAAG,eAAK,CAAC,KAAK,CAAC;oBACpB,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,GAAG,GAAG,CAAC;oBACX,KAAK,GAAG,eAAK,CAAC,GAAG,CAAC;oBAClB,MAAM;YACV,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,QAAQ;QACb,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;CACF;AAjGD,oCAiGC;AAED;;GAEG;AACH,MAAa,aAAa;IACxB;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAe,EAAE,eAAwB,IAAI;QACvE,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;QAElD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,EAAE,CAAC,QAAQ,CAAC,OAAO,GAAG,MAAM,GAAG,IAAI,EAAE,CAAC,MAAc,EAAE,EAAE;gBACtD,EAAE,CAAC,KAAK,EAAE,CAAC;gBAEX,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBAC7C,IAAI,QAAQ,KAAK,EAAE,EAAE,CAAC;oBACpB,OAAO,CAAC,YAAY,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,KAAK,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,OAAe,EAAE,OAAiB,EAAE,eAAuB,CAAC;QACrF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAChC,MAAM,MAAM,GAAG,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QACrC,MAAM,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YAClC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;QAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,EAAE,CAAC,QAAQ,CAAC,aAAa,OAAO,CAAC,MAAM,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,CAAC,MAAc,EAAE,EAAE;gBACrF,EAAE,CAAC,KAAK,EAAE,CAAC;gBAEX,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC1C,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;oBACzD,OAAO,CAAC,YAAY,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAK,CAAC,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAxDD,sCAwDC"}