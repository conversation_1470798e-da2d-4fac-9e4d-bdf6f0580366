# AI Code Review - Project Context for AI Assistant

## Project Overview

This is **AI Code Review**, a CLI tool that provides AI-powered code analysis for Git changes. It helps developers identify potential issues in their code before committing by analyzing staged files using advanced AI models like GPT-4 and Claude.

### Core Features

- **Multi-AI Support**: Integrates with OpenAI GPT-4, Anthropic Claude, and local LLMs
- **Intelligent Analysis**: Detects logic errors, performance bottlenecks, security vulnerabilities, code style issues, and business logic problems
- **Smart Caching**: Avoids re-analyzing unchanged code for faster reviews and reduced API costs
- **Multiple Output Formats**: Provides results in terminal, interactive HTML reports, and structured JSON
- **Flexible Configuration**: Project-specific and global configuration with auto-detection
- **Rich CLI Experience**: Progress bars, interactive wizards, and comprehensive help

## Project Structure

```
ai-code-review/
├── src/
│   ├── cli.ts              # Main CLI entry point and command definitions
│   ├── config/
│   │   └── manager.ts      # Configuration loading, validation and management
│   ├── ai/
│   │   └── manager.ts      # AI provider management and fallback logic
│   ├── core/
│   │   └── analyzer.ts     # Core analysis engine
│   ├── git/
│   │   └── scanner.ts      # Git repository integration
│   ├── report/
│   │   └── generator.ts    # Report generation in various formats
│   ├── cli/
│   │   └── wizard.ts       # Interactive configuration wizard
│   ├── utils/
│   └── types/
│       └── index.ts        # TypeScript type definitions
├── dist/                   # Compiled JavaScript output
├── docs/                   # Documentation files
├── tests/                  # Test files
├── package.json            # Project metadata and scripts
├── tsconfig.json           # TypeScript configuration
├── jest.config.js          # Jest testing configuration
└── README.md               # Project documentation
```

## Key Technologies

- **Language**: TypeScript
- **Runtime**: Node.js (16+)
- **Package Manager**: pnpm
- **Build Tool**: TypeScript compiler (tsc)
- **Testing**: Jest
- **CLI Framework**: Commander.js
- **AI Providers**: OpenAI, Anthropic, and local model support
- **Git Integration**: simple-git

## Development Workflow

1. **Setup**:
   - Clone the repository
   - Run `pnpm install` to install dependencies
   - Run `pnpm run build` to compile TypeScript to JavaScript

2. **Development**:
   - Run `pnpm run dev` to run the CLI directly from TypeScript source
   - Run `pnpm run lint` to check code style
   - Run `pnpm run test` to execute unit tests

3. **Building**:
   - Run `pnpm run build` to compile the project
   - Run `pnpm run package` to create standalone executables

4. **Testing**:
   - Run `pnpm run test` for unit tests
   - Run `pnpm run test:e2e` for end-to-end tests

## CLI Commands

- `ai-review` - Quick analysis of staged files with default settings
- `ai-review analyze` - Full analysis with customizable options
- `ai-review config` - Configuration management and setup
- `ai-review cache` - Cache management for performance
- `ai-review doctor` - System health check and diagnostics

## Configuration

The tool uses a JSON configuration file (`.ai-review.json`) that can be generated using the interactive wizard (`ai-review config --wizard`). Key configuration options include:

- AI provider (OpenAI, Claude, local)
- Analysis rules (strictness levels for logic, performance, security, style, business)
- Output format (terminal, HTML, JSON)
- Cache settings
- Ignore patterns

## Key Implementation Details

### CLI Architecture

The main CLI entry point is `src/cli.ts`, which uses Commander.js to define commands and options. The CLI supports various commands like `analyze`, `config`, `cache`, and `doctor`.

### Configuration Management

The `ConfigManager` class in `src/config/manager.ts` handles loading, validating, and saving configuration. It supports project-specific and global configuration files, with environment variables for sensitive data like API keys.

### AI Integration

The `AIManager` class in `src/ai/manager.ts` manages different AI providers (OpenAI, Claude, local models) and implements fallback logic. It also handles circuit breaker patterns and provider statistics.

### Analysis Engine

The `AnalysisEngine` class in `src/core/analyzer.ts` orchestrates the code analysis process. It processes Git changes and performs AI analysis on the code.

## Common Development Tasks

1. **Adding a new AI provider**:
   - Create a new provider class in `src/ai/providers/`
   - Update `AIManager` to initialize and use the new provider
   - Add provider-specific configuration options

2. **Adding new analysis rules**:
   - Update the prompt in `AnalysisEngine` to include new analysis categories
   - Update the `Issue` type in `src/types/index.ts` if needed
   - Update configuration validation schemas

3. **Adding new output formats**:
   - Create a new report generator in `src/report/generators/`
   - Update `ReportGenerator` to use the new format
   - Add the format to the configuration schema

4. **Enhancing Git integration**:
   - Modify `GitScanner` to support new Git operations
   - Update the analysis engine to use new Git data

When contributing to this project, please follow the guidelines in `CONTRIBUTING.md` and ensure all tests pass.