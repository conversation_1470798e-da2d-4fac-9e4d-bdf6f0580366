# 🤖 AI Code Review

[![npm version](https://badge.fury.io/js/ai-code-review.svg)](https://badge.fury.io/js/ai-code-review)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-16%2B-brightgreen.svg)](https://nodejs.org/)

> **AI-powered code review tool for Git changes** - Get intelligent analysis of your staged files using GPT-4 and Claude AI models.

## ✨ Features

- 🧠 **Multi-AI Support**: OpenAI GPT-4, Anthropic Claude, and local LLM integration
- 🎯 **Intelligent Analysis**: Logic errors, performance issues, security vulnerabilities, code style, and business logic
- ⚡ **Smart Caching**: Avoid re-analyzing unchanged code for faster reviews
- 📊 **Multiple Output Formats**: Beautiful terminal output, interactive HTML reports, and structured JSON
- 🔧 **Flexible Configuration**: Project-specific and global configuration with auto-detection
- 🎨 **Rich CLI Experience**: Progress bars, interactive wizards, and comprehensive help
- 🩺 **Health Diagnostics**: Built-in system health checks and troubleshooting

## 🚀 Quick Start

### Installation

```bash
# Install globally
npm install -g ai-code-review
# or with pnpm
pnpm add -g ai-code-review
```

### Setup

```bash
# Interactive configuration wizard
ai-review config --wizard

# Or set environment variable
export OPENAI_API_KEY="your-api-key"
```

### Usage

```bash
# Stage your changes
git add .

# Analyze your code
ai-review

# Or with options
ai-review analyze --mode full --output html
```

## 📋 Commands

### Core Commands

- `ai-review` - Quick analysis of staged files with default settings
- `ai-review analyze` - Full analysis with customizable options
- `ai-review config` - Configuration management and setup
- `ai-review cache` - Cache management for performance
- `ai-review doctor` - System health check and diagnostics

### Quick Examples

```bash
# Basic usage
ai-review

# Comprehensive analysis
ai-review analyze --mode full --output html --all

# Configuration management
ai-review config --wizard          # Interactive setup
ai-review config --show            # View current config
ai-review config --validate        # Check configuration
ai-review config --template        # Generate template

# Cache management  
ai-review cache --info             # Cache statistics
ai-review cache --clear            # Clear cache
ai-review cache --optimize         # Optimize cache

# Health check
ai-review doctor                    # Full system diagnostic
```

## ⚙️ Configuration

### Configuration File

Create `.ai-review.json` in your project root:

```json
{
  "aiProvider": "openai",
  "model": "gpt-4",
  "rules": {
    "logic": "strict",
    "performance": "normal", 
    "security": "strict",
    "style": "normal",
    "business": "normal"
  },
  "output": {
    "format": "terminal",
    "detailed": true,
    "verbose": false
  },
  "cache": {
    "enabled": true,
    "ttl": 24
  },
  "ignore": [
    "node_modules/**",
    "*.min.js",
    "dist/**"
  ]
}
```

### Environment Variables

```bash
# API Keys
export OPENAI_API_KEY="sk-..."        # For OpenAI models
export ANTHROPIC_API_KEY="sk-ant-..." # For Claude models

# Optional overrides
export AI_REVIEW_PROVIDER="openai"    # Default provider
export AI_REVIEW_MODEL="gpt-4"        # Default model
```

## 🎯 Analysis Types

| Category | Description | Examples |
|----------|-------------|----------|
| **Logic** | Programming errors and bugs | Null checks, undefined variables, logic flaws |
| **Performance** | Efficiency and optimization | Inefficient loops, memory leaks, redundant operations |
| **Security** | Vulnerability detection | XSS, injection attacks, sensitive data exposure |
| **Style** | Code style and conventions | Formatting, naming, code organization |
| **Business** | Domain-specific logic | Business rules, workflow validation |

## 📊 Output Formats

### Terminal Output (Default)
- 🎨 Colorful, structured display
- ⚡ Real-time progress indicators
- 📈 Summary statistics

### HTML Reports
- 📊 Interactive charts and graphs
- 🔍 Searchable and filterable issues
- 📱 Mobile-friendly responsive design

### JSON Export
- 🔧 Machine-readable structured data
- 🔗 Perfect for CI/CD integration
- 📋 Complete analysis metadata

## 🛠️ Supported File Types

- **JavaScript**: `.js`, `.jsx`
- **TypeScript**: `.ts`, `.tsx`  
- **Vue.js**: `.vue`
- **Styles**: `.css`, `.scss`, `.sass`, `.less`
- **Markup**: `.html`, `.htm`
- **Data**: `.json`

## 🏗️ Project Integration

### Git Hooks

Add to `.git/hooks/pre-commit`:

```bash
#!/bin/sh
ai-review --mode quick
```

### CI/CD Pipeline

```yaml
# GitHub Actions example
- name: AI Code Review
  run: |
    npm install -g ai-code-review
    ai-review analyze --output json > review-results.json
```

### Package.json Scripts

```json
{
  "scripts": {
    "review": "ai-review",
    "review:full": "ai-review analyze --mode full --output html"
  }
}
```

## 🔧 Advanced Usage

### Analysis Modes

- **Quick Mode**: Fast analysis focusing on critical issues only
- **Full Mode**: Comprehensive analysis including style and best practices

### Custom Rules

Configure analysis strictness per category:

- `strict`: Maximum scrutiny, catches minor issues
- `normal`: Balanced analysis, focuses on important issues  
- `loose`: Minimal analysis, only critical problems

### Caching System

- 📈 Improves performance by 60-80%
- 💰 Reduces API costs significantly
- 🔄 Automatic cache invalidation on file changes

## 🚦 Requirements

- **Node.js**: 16.0.0 or higher
- **Git**: Any recent version
- **API Key**: OpenAI or Anthropic account (for AI analysis)

## 📖 Documentation

- [Configuration Guide](docs/configuration.md)
- [CLI Reference](docs/cli-reference.md)
- [Integration Examples](docs/integration.md)
- [API Documentation](docs/api.md)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙋‍♀️ Support

- 📖 [Documentation](https://github.com/ai-code-review/ai-code-review/wiki)
- 🐛 [Issue Tracker](https://github.com/ai-code-review/ai-code-review/issues)
- 💬 [Discussions](https://github.com/ai-code-review/ai-code-review/discussions)

## 🎉 Acknowledgments

- OpenAI for GPT models
- Anthropic for Claude models
- The open-source community for inspiration

---

**Made with ❤️ by the AI Code Review Team**

> Helping developers write better code, one commit at a time.