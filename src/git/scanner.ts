import simpleGit, { SimpleGit } from 'simple-git';
import * as path from 'path';
import * as fs from 'fs-extra';
import { GitFileChange, GitChange } from '../types';
import { logger } from '../utils/logger';

export class GitScanner {
  private git: SimpleGit;
  private supportedExtensions = new Set([
    '.js', '.jsx', '.ts', '.tsx', 
    '.vue', '.svelte',
    '.css', '.scss', '.sass', '.less',
    '.html', '.htm', '.json'
  ]);

  constructor(workingDir?: string) {
    this.git = simpleGit(workingDir || process.cwd());
  }

  /**
   * 获取Git暂存区的变更文件
   */
  public async getStagedFiles(): Promise<GitFileChange[]> {
    try {
      logger.debug('Scanning staged files...');
      
      // 获取暂存区状态
      const status = await this.git.status();
      
      if (status.staged.length === 0) {
        logger.warn('No staged files found. Please stage files using "git add" before running analysis.');
        return [];
      }

      const changes: GitFileChange[] = [];

      for (const file of status.staged) {
        // 检查文件类型
        if (!this.isSupportedFile(file)) {
          logger.debug(`Skipping unsupported file: ${file}`);
          continue;
        }

        // 检查文件是否存在（排除删除的文件）
        const filePath = path.resolve(file);
        const exists = await fs.pathExists(filePath);

        if (!exists && !status.deleted.includes(file)) {
          logger.debug(`File does not exist: ${file}`);
          continue;
        }

        const fileChange: GitFileChange = {
          file,
          status: this.getFileStatus(file, status),
          changes: []
        };

        // 获取文件的具体变更
        if (exists) {
          fileChange.changes = await this.getFileChanges(file);
        }

        changes.push(fileChange);
        logger.debug(`Added file for analysis: ${file} (${fileChange.status})`);
      }

      logger.info(`Found ${changes.length} staged files for analysis`);
      return changes;

    } catch (error) {
      logger.error(`Failed to scan staged files: ${error}`);
      throw error;
    }
  }

  /**
   * 获取所有修改的文件（包括暂存和未暂存）
   */
  public async getAllModifiedFiles(): Promise<GitFileChange[]> {
    try {
      logger.debug('Scanning all modified files...');
      
      const status = await this.git.status();
      const changes: GitFileChange[] = [];
      
      // 收集所有变更文件（暂存区 + 工作区）
      const allFiles = new Set([
        ...status.staged,
        ...status.modified,
        ...status.not_added
      ]);

      if (allFiles.size === 0) {
        logger.warn('No modified files found.');
        return [];
      }

      for (const file of allFiles) {
        // 检查文件类型
        if (!this.isSupportedFile(file)) {
          logger.debug(`Skipping unsupported file: ${file}`);
          continue;
        }

        // 检查文件是否存在（排除删除的文件）
        const filePath = path.resolve(file);
        const exists = await fs.pathExists(filePath);

        if (!exists && !status.deleted.includes(file)) {
          logger.debug(`File does not exist: ${file}`);
          continue;
        }

        const fileChange: GitFileChange = {
          file,
          status: this.getFileStatus(file, status),
          changes: []
        };

        // 获取文件的具体变更
        if (exists) {
          fileChange.changes = await this.getFileChanges(file);
        }

        changes.push(fileChange);
        logger.debug(`Added file for analysis: ${file} (${fileChange.status})`);
      }

      logger.info(`Found ${changes.length} modified files for analysis`);
      return changes;

    } catch (error) {
      logger.error(`Failed to scan modified files: ${error}`);
      throw error;
    }
  }
  public async getUnstagedFiles(): Promise<GitFileChange[]> {
    try {
      logger.debug('Scanning unstaged files...');
      
      const status = await this.git.status();
      const changes: GitFileChange[] = [];

      // 合并修改和新增的文件
      const modifiedFiles = [...status.modified, ...status.not_added];

      for (const file of modifiedFiles) {
        if (!this.isSupportedFile(file)) {
          logger.debug(`Skipping unsupported file: ${file}`);
          continue;
        }

        const filePath = path.resolve(file);
        if (!await fs.pathExists(filePath)) {
          continue;
        }

        const fileChange: GitFileChange = {
          file,
          status: status.not_added.includes(file) ? 'added' : 'modified',
          changes: await this.getFileChanges(file, false) // false = unstaged
        };

        changes.push(fileChange);
      }

      logger.info(`Found ${changes.length} unstaged files`);
      return changes;

    } catch (error) {
      logger.error(`Failed to scan unstaged files: ${error}`);
      throw error;
    }
  }

  /**
   * 获取所有变更文件（暂存区 + 工作区）
   */
  public async getAllChangedFiles(): Promise<GitFileChange[]> {
    const [stagedFiles, unstagedFiles] = await Promise.all([
      this.getStagedFiles(),
      this.getUnstagedFiles()
    ]);

    // 合并结果，暂存区优先
    const fileMap = new Map<string, GitFileChange>();
    
    // 先添加暂存区文件
    stagedFiles.forEach(file => {
      fileMap.set(file.file, file);
    });

    // 添加工作区文件（如果不在暂存区中）
    unstagedFiles.forEach(file => {
      if (!fileMap.has(file.file)) {
        fileMap.set(file.file, file);
      }
    });

    return Array.from(fileMap.values());
  }

  /**
   * 获取文件的具体变更内容
   */
  private async getFileChanges(file: string, staged: boolean = true): Promise<GitChange[]> {
    try {
      // 验证和清理文件路径以防止命令注入
      const sanitizedFile = this.sanitizeFilePath(file);
      if (!sanitizedFile) {
        logger.warn(`Invalid file path detected: ${file}`);
        return [];
      }

      // 获取diff信息
      const diffOptions = staged ? ['--cached'] : [];
      const diff = await this.git.diff([...diffOptions, sanitizedFile]);
      
      return this.parseDiffResult(diff);

    } catch (error) {
      logger.debug(`Failed to get changes for file ${file}: ${error}`);
      return [];
    }
  }

  /**
   * 解析diff结果
   */
  private parseDiffResult(diffContent: string): GitChange[] {
    const changes: GitChange[] = [];
    const lines = diffContent.split('\n');
    
    let currentLine = 0;
    let inHunk = false;

    for (const line of lines) {
      // 跳过文件头信息
      if (line.startsWith('diff --git') || 
          line.startsWith('index ') || 
          line.startsWith('---') || 
          line.startsWith('+++')) {
        continue;
      }

      // 解析hunk头 (@@行)
      const hunkMatch = line.match(/^@@ -(\d+),?\d* \+(\d+),?\d* @@/);
      if (hunkMatch) {
        currentLine = parseInt(hunkMatch[2]) - 1; // 新文件的起始行号
        inHunk = true;
        continue;
      }

      if (!inHunk) continue;

      // 解析变更行
      if (line.startsWith('+') && !line.startsWith('+++')) {
        currentLine++;
        changes.push({
          line: currentLine,
          type: 'added',
          content: line.substring(1) // 移除+号
        });
      } else if (line.startsWith('-') && !line.startsWith('---')) {
        changes.push({
          line: currentLine,
          type: 'removed',
          content: line.substring(1) // 移除-号
        });
      } else if (line.startsWith(' ')) {
        currentLine++;
        // 上下文行，不是变更但需要计算行号
      }
    }

    return changes;
  }

  /**
   * 检查文件是否为支持的类型
   */
  private isSupportedFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return this.supportedExtensions.has(ext);
  }

  /**
   * 获取文件的变更状态
   */
  private getFileStatus(file: string, status: any): GitFileChange['status'] {
    if (status.created.includes(file)) return 'added';
    if (status.deleted.includes(file)) return 'deleted';
    return 'modified';
  }

  /**
   * 检查当前目录是否为Git仓库
   */
  public async isGitRepository(): Promise<boolean> {
    try {
      await this.git.status();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取当前分支信息
   */
  public async getCurrentBranch(): Promise<string> {
    try {
      const status = await this.git.status();
      return status.current || 'unknown';
    } catch (error) {
      logger.debug(`Failed to get current branch: ${error}`);
      return 'unknown';
    }
  }

  /**
   * 获取仓库根目录
   */
  public async getRepositoryRoot(): Promise<string> {
    try {
      const result = await this.git.revparse(['--show-toplevel']);
      return result.trim();
    } catch (error) {
      logger.debug(`Failed to get repository root: ${error}`);
      return process.cwd();
    }
  }

  /**
   * 获取文件的完整内容（用于上下文分析）
   */
  public async getFileContent(filePath: string): Promise<string> {
    try {
      // 验证和清理文件路径以防止路径遍历攻击
      const sanitizedPath = this.sanitizeFilePath(filePath);
      if (!sanitizedPath) {
        logger.warn(`Invalid file path detected: ${filePath}`);
        return '';
      }

      const fullPath = path.resolve(sanitizedPath);
      
      // 确保路径在项目目录内
      const repoRoot = await this.getRepositoryRoot();
      if (!fullPath.startsWith(repoRoot)) {
        logger.warn(`File path outside repository: ${filePath}`);
        return '';
      }

      return await fs.readFile(fullPath, 'utf-8');
    } catch (error) {
      logger.debug(`Failed to read file content: ${filePath}`);
      return '';
    }
  }

  /**
   * 清理和验证文件路径，防止命令注入和路径遍历
   */
  private sanitizeFilePath(filePath: string): string | null {
    // 移除任何可能的命令注入字符
    const dangerousChars = /[;&|`$()<>\n\r]/g;
    if (dangerousChars.test(filePath)) {
      return null;
    }

    // 防止路径遍历
    const normalizedPath = path.normalize(filePath);
    if (normalizedPath.includes('..') || path.isAbsolute(normalizedPath)) {
      // 只允许相对路径且不包含父目录引用
      if (path.isAbsolute(normalizedPath)) {
        // 如果是绝对路径，尝试转换为相对路径
        const cwd = process.cwd();
        if (normalizedPath.startsWith(cwd)) {
          return path.relative(cwd, normalizedPath);
        }
      }
      return null;
    }

    return normalizedPath;
  }
}