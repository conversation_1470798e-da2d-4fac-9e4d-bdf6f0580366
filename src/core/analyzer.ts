import { AIManager } from '../ai/manager';
import { Issue, AnalysisResult, GitFileChange, AIReviewConfig } from '../types';
import { logger } from '../utils/logger';
import { performanceMonitor } from '../utils/performance';
import { errorHandler } from '../utils/errorHandler';

export interface AnalysisOptions {
  includeContext: boolean;
  maxContextLines: number;
  analysisDepth: 'quick' | 'detailed';
  ignorePatterns: string[];
}

export class AnalysisEngine {
  private aiManager: AIManager;
  private config: AIReviewConfig;

  constructor(config: AIReviewConfig) {
    this.config = config;
    this.aiManager = new AIManager(config);
  }

  /**
   * 分析文件变更并返回发现的问题
   */
  public async analyzeChanges(
    changes: GitFileChange[],
    options: Partial<AnalysisOptions> = {}
  ): Promise<AnalysisResult[]> {
    const finalOptions: AnalysisOptions = {
      includeContext: true,
      maxContextLines: 20,
      analysisDepth: 'detailed',
      ignorePatterns: [],
      ...options
    };

    logger.info(`Starting analysis of ${changes.length} changed files`);
    
    return performanceMonitor.timeOperation(
      'analyze_changes',
      async () => {
        // 确定并发数量（根据CPU核心数和文件数量）
        const os = require('os');
        const cpuCount = os.cpus().length;
        const batchSize = Math.min(cpuCount, Math.max(2, Math.floor(cpuCount / 2)));
        
        logger.debug(`Using parallel processing with batch size: ${batchSize}`);
        
        // 批处理文件以实现并行分析
        const results: AnalysisResult[] = [];
        
        for (let i = 0; i < changes.length; i += batchSize) {
          const batch = changes.slice(i, i + batchSize);
          
          // 并行分析批次中的文件
          const batchPromises = batch.map(async (change) => {
            try {
              return await this.analyzeSingleFile(change, finalOptions);
            } catch (error) {
              logger.error(`Failed to analyze file ${change.file}: ${error}`);
              return this.createEmptyAnalysisResult(change.file, error);
            }
          });
          
          // 等待批次完成
          const batchResults = await Promise.all(batchPromises);
          results.push(...batchResults);
          
          // 记录进度
          const progress = Math.min(i + batchSize, changes.length);
          logger.debug(`Progress: ${progress}/${changes.length} files analyzed`);
        }
        
        logger.info(`Analysis completed: ${results.length} files processed`);
        return results;
      },
      { fileCount: changes.length, depth: finalOptions.analysisDepth }
    );
  }

  /**
   * 分析单个文件
   */
  private async analyzeSingleFile(
    change: GitFileChange,
    options: AnalysisOptions
  ): Promise<AnalysisResult> {
    return performanceMonitor.timeOperation(
      'analyze_single_file',
      async () => {
        // 检查文件是否存在
        const fs = require('fs-extra');
        if (!await fs.pathExists(change.file)) {
          logger.debug(`File ${change.file} does not exist, returning empty result`);
          return this.createEmptyAnalysisResult(change.file, new Error('File not found'));
        }
        
        // 简化版本 - 直接分析文件变更
        const issues = await this.performAIAnalysis(change, options);

        const result: AnalysisResult = {
          file: change.file,
          issues,
          statistics: this.calculateStatistics(issues),
          metadata: {
            analyzedAt: new Date().toISOString(),
            aiModel: 'ai-analysis',
            fromCache: false
          }
        };

        return result;
      },
      { file: change.file }
    );
  }

  /**
   * 执行AI分析
   */
  private async performAIAnalysis(
    change: GitFileChange,
    options: AnalysisOptions
  ): Promise<Issue[]> {
    const prompt = this.buildAnalysisPrompt(change, options);
    
    return errorHandler.withRetry(
      async () => {
        // 检查文件是否存在
        const fs = require('fs-extra');
        if (!await fs.pathExists(change.file)) {
          logger.debug(`File ${change.file} does not exist, skipping analysis`);
          return [];
        }
        
        // TODO: 实际的AI调用将在后续版本实现
        // 现在返回模拟结果用于演示
        logger.debug(`Analyzing ${change.file} with AI (simulated)`);
        
        return this.createMockIssues(change);
      },
      `AI analysis for ${change.file}`,
      { maxRetries: 1, baseDelay: 1000 }
    );
  }

  /**
   * 构建分析提示词
   */
  private buildAnalysisPrompt(change: GitFileChange, options: AnalysisOptions): string {
    const fileContent = change.changes.map(c => c.content).join('\n');
    
    return `Analyze this ${this.getFileType(change.file)} code for issues:

File: ${change.file}
Status: ${change.status}

Code:
\`\`\`${this.getFileType(change.file)}
${fileContent}
\`\`\`

Focus on:
1. Logic errors and bugs
2. Performance issues
3. Security vulnerabilities
4. Code quality issues

Return JSON array of issues with: type, severity, line, message, description, suggestion.`;
  }

  /**
   * 创建模拟问题（用于演示和测试）
   */
  private createMockIssues(change: GitFileChange): Issue[] {
    const issues: Issue[] = [];
    
    // 为每个变更创建一个示例问题
    change.changes.forEach((gitChange, index) => {
      if (gitChange.type === 'added' && gitChange.content.trim()) {
        issues.push({
          type: 'Logic',
          severity: 'Medium',
          line: gitChange.line,
          message: 'Code review suggestion',
          description: `New code added at line ${gitChange.line} should be reviewed for potential improvements`,
          suggestion: 'Consider adding error handling and validation',
          author: 'AI Analysis',
          commitTime: new Date().toISOString()
        });
      }
    });

    return issues;
  }

  /**
   * 计算统计信息
   */
  private calculateStatistics(issues: Issue[]): AnalysisResult['statistics'] {
    return {
      critical: issues.filter(i => i.severity === 'Critical').length,
      high: issues.filter(i => i.severity === 'High').length,
      medium: issues.filter(i => i.severity === 'Medium').length,
      low: issues.filter(i => i.severity === 'Low').length
    };
  }

  /**
   * 创建空的分析结果（用于失败情况）
   */
  private createEmptyAnalysisResult(fileName: string, error: any): AnalysisResult {
    return {
      file: fileName,
      issues: [],
      statistics: {
        critical: 0,
        high: 0,
        medium: 0,
        low: 0
      },
      metadata: {
        analyzedAt: new Date().toISOString(),
        aiModel: 'failed',
        fromCache: false
      }
    };
  }

  /**
   * 获取文件类型
   */
  private getFileType(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();
    
    switch (ext) {
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'vue':
        return 'vue';
      case 'css':
      case 'scss':
      case 'sass':
        return 'css';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'go':
        return 'go';
      default:
        return 'text';
    }
  }

  /**
   * 获取支持的文件类型
   */
  public getSupportedFileTypes(): string[] {
    return [
      'javascript', 'typescript', 'vue', 'css', 'python', 'java', 'go'
    ];
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<AIReviewConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取分析统计信息
   */
  public getAnalysisStats(): {
    totalAnalyzed: number;
    cacheHitRate: number;
    averageAnalysisTime: number;
  } {
    const perfStats = performanceMonitor.getAggregatedMetrics('analyze_single_file');
    
    return {
      totalAnalyzed: perfStats?.count || 0,
      cacheHitRate: 0, // Simplified - no cache
      averageAnalysisTime: perfStats?.averageDuration || 0
    };
  }

  /**
   * 清理资源
   */
  public async cleanup(): Promise<void> {
    logger.debug('Analysis engine cleanup completed');
  }
}