import { logger } from './logger';

export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
  };
  metadata?: Record<string, any>;
}

export interface AggregatedMetrics {
  operationName: string;
  count: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  successCount: number;
  errorCount: number;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics[]> = new Map();
  private activeOperations: Map<string, PerformanceMetrics> = new Map();
  private enabled: boolean = true;
  private maxMetricsPerOperation: number = 1000; // 限制每个操作的最大指标数
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    // 启动定期清理任务
    this.startAutoCleanup();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  public startOperation(operationName: string, metadata?: Record<string, any>): string {
    if (!this.enabled) return '';

    const operationId = `${operationName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startMetrics: PerformanceMetrics = {
      operationName,
      startTime: performance.now(),
      memoryUsage: this.getCurrentMemoryUsage(),
      metadata
    };

    this.activeOperations.set(operationId, startMetrics);
    return operationId;
  }

  /**
   * End timing an operation
   */
  public endOperation(operationId: string, success: boolean = true, error?: Error): PerformanceMetrics | null {
    if (!this.enabled || !operationId) return null;

    const activeMetric = this.activeOperations.get(operationId);
    if (!activeMetric) {
      logger.warn(`No active operation found for ID: ${operationId}`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - activeMetric.startTime;
    const endMemoryUsage = this.getCurrentMemoryUsage();

    const completedMetric: PerformanceMetrics = {
      ...activeMetric,
      endTime,
      duration,
      metadata: {
        ...activeMetric.metadata,
        success,
        error: error ? error.message : undefined,
        memoryDelta: this.calculateMemoryDelta(activeMetric.memoryUsage, endMemoryUsage)
      }
    };

    // Store the completed metric
    const operationMetrics = this.metrics.get(activeMetric.operationName) || [];
    operationMetrics.push(completedMetric);
    
    // 限制存储的指标数量以防止内存泄漏
    if (operationMetrics.length > this.maxMetricsPerOperation) {
      operationMetrics.shift(); // 移除最旧的指标
    }
    
    this.metrics.set(activeMetric.operationName, operationMetrics);

    // Clean up active operation
    this.activeOperations.delete(operationId);

    // Log performance for slow operations
    if (duration > 1000) { // Log operations taking more than 1 second
      logger.info(`Slow operation detected: ${activeMetric.operationName} took ${duration.toFixed(2)}ms`);
    }

    return completedMetric;
  }

  /**
   * Time an async operation automatically
   */
  public async timeOperation<T>(
    operationName: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const operationId = this.startOperation(operationName, metadata);
    
    try {
      const result = await operation();
      this.endOperation(operationId, true);
      return result;
    } catch (error) {
      this.endOperation(operationId, false, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Time a synchronous operation automatically
   */
  public timeSync<T>(
    operationName: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    const operationId = this.startOperation(operationName, metadata);
    
    try {
      const result = operation();
      this.endOperation(operationId, true);
      return result;
    } catch (error) {
      this.endOperation(operationId, false, error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Get aggregated metrics for an operation
   */
  public getAggregatedMetrics(operationName: string): AggregatedMetrics | null {
    const operationMetrics = this.metrics.get(operationName);
    if (!operationMetrics || operationMetrics.length === 0) {
      return null;
    }

    const durations = operationMetrics.map(m => m.duration || 0);
    const successCount = operationMetrics.filter(m => m.metadata?.success !== false).length;
    const errorCount = operationMetrics.length - successCount;

    return {
      operationName,
      count: operationMetrics.length,
      totalDuration: durations.reduce((sum, d) => sum + d, 0),
      averageDuration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      successCount,
      errorCount
    };
  }

  /**
   * Get all performance metrics
   */
  public getAllMetrics(): Map<string, AggregatedMetrics> {
    const result = new Map<string, AggregatedMetrics>();
    
    for (const operationName of this.metrics.keys()) {
      const aggregated = this.getAggregatedMetrics(operationName);
      if (aggregated) {
        result.set(operationName, aggregated);
      }
    }

    return result;
  }

  /**
   * Generate performance report
   */
  public generateReport(): string {
    const allMetrics = this.getAllMetrics();
    
    if (allMetrics.size === 0) {
      return 'No performance metrics collected';
    }

    const lines: string[] = [];
    lines.push('📊 Performance Report');
    lines.push('='.repeat(50));
    lines.push('');

    // Sort by total duration descending
    const sortedMetrics = Array.from(allMetrics.entries()).sort(
      ([, a], [, b]) => b.totalDuration - a.totalDuration
    );

    for (const [operationName, metrics] of sortedMetrics) {
      lines.push(`🔧 ${operationName}`);
      lines.push(`   Count: ${metrics.count}`);
      lines.push(`   Total Time: ${metrics.totalDuration.toFixed(2)}ms`);
      lines.push(`   Average Time: ${metrics.averageDuration.toFixed(2)}ms`);
      lines.push(`   Min/Max: ${metrics.minDuration.toFixed(2)}ms / ${metrics.maxDuration.toFixed(2)}ms`);
      lines.push(`   Success Rate: ${((metrics.successCount / metrics.count) * 100).toFixed(1)}%`);
      lines.push('');
    }

    // Add memory usage summary
    const currentMemory = this.getCurrentMemoryUsage();
    lines.push('💾 Current Memory Usage');
    lines.push(`   Heap Used: ${(currentMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    lines.push(`   Heap Total: ${(currentMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);
    lines.push(`   RSS: ${(currentMemory.rss / 1024 / 1024).toFixed(2)} MB`);
    lines.push('');

    // Add bottleneck analysis
    const bottlenecks = this.identifyBottlenecks();
    if (bottlenecks.length > 0) {
      lines.push('⚠️  Performance Bottlenecks Detected');
      bottlenecks.forEach(bottleneck => {
        lines.push(`   • ${bottleneck}`);
      });
      lines.push('');
    }

    return lines.join('\n');
  }

  /**
   * Clear all metrics
   */
  public clearMetrics(): void {
    this.metrics.clear();
    this.activeOperations.clear();
  }

  /**
   * 启动自动清理任务
   */
  private startAutoCleanup(): void {
    // 每5分钟清理一次旧指标
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldMetrics();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理旧指标以防止内存泄漏
   */
  private cleanupOldMetrics(): void {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1小时

    for (const [operationName, metrics] of this.metrics.entries()) {
      // 过滤掉超过1小时的指标
      const recentMetrics = metrics.filter(m => {
        return m.endTime && (now - m.endTime) < maxAge;
      });

      if (recentMetrics.length > 0) {
        this.metrics.set(operationName, recentMetrics);
      } else {
        // 如果没有最近的指标，删除整个条目
        this.metrics.delete(operationName);
      }
    }

    // 清理超时的活动操作（超过10分钟未完成的操作）
    const operationTimeout = 10 * 60 * 1000;
    for (const [operationId, metric] of this.activeOperations.entries()) {
      if ((now - metric.startTime) > operationTimeout) {
        logger.warn(`Cleaning up stale operation: ${operationId}`);
        this.activeOperations.delete(operationId);
      }
    }

    // 如果内存使用过高，触发垃圾回收
    const memUsage = process.memoryUsage();
    if (memUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
      this.trimMetrics();
      if (global.gc) {
        global.gc();
      }
    }
  }

  /**
   * 裁剪指标以减少内存使用
   */
  private trimMetrics(): void {
    for (const [operationName, metrics] of this.metrics.entries()) {
      if (metrics.length > 100) {
        // 只保留最近的100个指标
        const trimmed = metrics.slice(-100);
        this.metrics.set(operationName, trimmed);
      }
    }
  }

  /**
   * 销毁监视器实例
   */
  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clearMetrics();
  }

  /**
   * Enable or disable performance monitoring
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    if (!enabled) {
      this.clearMetrics();
    }
  }

  /**
   * Check if monitoring is enabled
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Export metrics to JSON format
   */
  public exportMetrics(): string {
    const data = {
      timestamp: new Date().toISOString(),
      aggregatedMetrics: Object.fromEntries(this.getAllMetrics()),
      rawMetrics: Object.fromEntries(this.metrics),
      currentMemory: this.getCurrentMemoryUsage()
    };

    return JSON.stringify(data, null, 2);
  }

  /**
   * Get current memory usage
   */
  private getCurrentMemoryUsage() {
    const memUsage = process.memoryUsage();
    return {
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss
    };
  }

  /**
   * Calculate memory usage delta
   */
  private calculateMemoryDelta(start: any, end: any) {
    return {
      heapUsedDelta: end.heapUsed - start.heapUsed,
      heapTotalDelta: end.heapTotal - start.heapTotal,
      rssDelta: end.rss - start.rss
    };
  }

  /**
   * Identify performance bottlenecks
   */
  private identifyBottlenecks(): string[] {
    const bottlenecks: string[] = [];
    const allMetrics = this.getAllMetrics();

    for (const [operationName, metrics] of allMetrics) {
      // Check for slow operations (> 2 seconds average)
      if (metrics.averageDuration > 2000) {
        bottlenecks.push(`${operationName}: Slow average duration (${metrics.averageDuration.toFixed(2)}ms)`);
      }

      // Check for high error rates (> 10%)
      const errorRate = (metrics.errorCount / metrics.count) * 100;
      if (errorRate > 10) {
        bottlenecks.push(`${operationName}: High error rate (${errorRate.toFixed(1)}%)`);
      }

      // Check for operations with high variance (max > 5x average)
      if (metrics.maxDuration > metrics.averageDuration * 5) {
        bottlenecks.push(`${operationName}: High performance variance (max ${metrics.maxDuration.toFixed(2)}ms vs avg ${metrics.averageDuration.toFixed(2)}ms)`);
      }
    }

    return bottlenecks;
  }
}

// Global performance monitor instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Decorator for automatic performance monitoring
export function monitored(operationName?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const finalOperationName = operationName || `${target.constructor.name}.${propertyName}`;

    descriptor.value = async function (...args: any[]) {
      if (method.constructor.name === 'AsyncFunction') {
        return performanceMonitor.timeOperation(finalOperationName, () => method.apply(this, args));
      } else {
        return performanceMonitor.timeSync(finalOperationName, () => method.apply(this, args));
      }
    };

    return descriptor;
  };
}

// Memory monitoring utilities
export class MemoryMonitor {
  private static memoryCheckInterval: NodeJS.Timeout | null = null;
  private static memoryThreshold = 500 * 1024 * 1024; // 500MB threshold

  /**
   * Start monitoring memory usage
   */
  public static startMonitoring(intervalMs: number = 30000): void {
    this.stopMonitoring(); // Ensure no duplicate intervals

    this.memoryCheckInterval = setInterval(() => {
      const usage = process.memoryUsage();
      
      if (usage.heapUsed > this.memoryThreshold) {
        logger.warn(`High memory usage detected: ${(usage.heapUsed / 1024 / 1024).toFixed(2)} MB`);
        
        // Suggest garbage collection if available
        if (global.gc) {
          logger.info('Running garbage collection...');
          global.gc();
        }
      }
    }, intervalMs);
  }

  /**
   * Stop memory monitoring
   */
  public static stopMonitoring(): void {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
    }
  }

  /**
   * Force garbage collection if available
   */
  public static forceGC(): void {
    if (global.gc) {
      const beforeGC = process.memoryUsage().heapUsed;
      global.gc();
      const afterGC = process.memoryUsage().heapUsed;
      const recovered = beforeGC - afterGC;
      
      logger.info(`Garbage collection completed. Recovered ${(recovered / 1024 / 1024).toFixed(2)} MB`);
    } else {
      logger.warn('Garbage collection not available. Run with --expose-gc flag to enable.');
    }
  }

  /**
   * Set memory usage threshold for warnings
   */
  public static setMemoryThreshold(thresholdMB: number): void {
    this.memoryThreshold = thresholdMB * 1024 * 1024;
  }
}