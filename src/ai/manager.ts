import { AIReviewConfig } from '../types';
import { OpenAIProvider, AIAnalysisRequest, AIAnalysisResponse } from './providers/openai';
import { ClaudeProvider } from './providers/claude';
import { LocalProvider } from './providers/local';
import { DeepSeekProvider } from './providers/deepseek';
import { logger } from '../utils/logger';

export interface AIProviderStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalTokensUsed: number;
  averageResponseTime: number;
  lastUsed: Date | null;
}

export class AIManager {
  private providers: Map<string, any> = new Map();
  private stats: Map<string, AIProviderStats> = new Map();
  private currentProvider: string;
  private fallbackProviders: string[];
  private circuitBreaker: Map<string, { failures: number; lastFailure: Date }> = new Map();

  constructor(config: AIReviewConfig) {
    this.currentProvider = config.aiProvider;
    this.fallbackProviders = this.determineFallbackOrder(config.aiProvider);
    this.initializeProviders(config);
    this.initializeStats();
  }

  /**
   * 初始化AI提供商
   */
  private initializeProviders(config: AIReviewConfig): void {
    try {
      // 初始化OpenAI提供商
      if (config.apiKey || process.env.OPENAI_API_KEY) {
        const apiKey = config.apiKey || process.env.OPENAI_API_KEY!;
        const model = config.aiProvider === 'openai' ? config.model : 'gpt-4';
        this.providers.set('openai', new OpenAIProvider(apiKey, model));
        logger.debug('OpenAI provider initialized');
      }

      // 初始化Claude提供商
      if (config.apiKey || process.env.ANTHROPIC_API_KEY) {
        const apiKey = config.apiKey || process.env.ANTHROPIC_API_KEY!;
        const model = config.aiProvider === 'claude' ? config.model : 'claude-3-sonnet-20240229';
        this.providers.set('claude', new ClaudeProvider(apiKey, model));
        logger.debug('Claude provider initialized');
      }

      // 初始化DeepSeek提供商
      if (config.aiProvider === 'deepseek' || process.env.DEEPSEEK_API_KEY) {
        const apiKey = config.apiKey || process.env.DEEPSEEK_API_KEY!;
        const model = config.aiProvider === 'deepseek' ? config.model : 'deepseek-coder';
        const deepseekConfig = {
          apiKey,
          model,
          baseUrl: config.baseUrl // 使用自定义baseUrl
        };
        this.providers.set('deepseek', new DeepSeekProvider(deepseekConfig));
        logger.debug('DeepSeek provider initialized');
      }

      // 初始化本地模型提供商
      const localModel = config.aiProvider === 'local' ? config.model : 'codellama';
      this.providers.set('local', new LocalProvider(undefined, localModel));
      logger.debug('Local provider initialized');

    } catch (error) {
      logger.error(`Failed to initialize AI providers: ${error}`);
      throw error;
    }
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(): void {
    for (const provider of this.providers.keys()) {
      this.stats.set(provider, {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalTokensUsed: 0,
        averageResponseTime: 0,
        lastUsed: null
      });
    }
  }

  /**
   * 分析代码
   */
  public async analyzeCode(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    const startTime = Date.now();
    let lastError: Error | null = null;

    // 尝试使用主要提供商
    try {
      if (this.isProviderAvailable(this.currentProvider)) {
        const result = await this.callProvider(this.currentProvider, request);
        this.updateStats(this.currentProvider, startTime, result.usage?.totalTokens || 0, true);
        return result;
      }
    } catch (error) {
      lastError = error as Error;
      logger.warn(`Primary provider ${this.currentProvider} failed: ${error}`);
      this.updateStats(this.currentProvider, startTime, 0, false);
      this.updateCircuitBreaker(this.currentProvider);
    }

    // 尝试使用后备提供商
    for (const fallbackProvider of this.fallbackProviders) {
      if (!this.isProviderAvailable(fallbackProvider)) {
        continue;
      }

      try {
        logger.info(`Trying fallback provider: ${fallbackProvider}`);
        const result = await this.callProvider(fallbackProvider, request);
        this.updateStats(fallbackProvider, startTime, result.usage?.totalTokens || 0, true);
        return result;
      } catch (error) {
        lastError = error as Error;
        logger.warn(`Fallback provider ${fallbackProvider} failed: ${error}`);
        this.updateStats(fallbackProvider, startTime, 0, false);
        this.updateCircuitBreaker(fallbackProvider);
      }
    }

    // 所有提供商都失败，返回错误
    throw new Error(`All AI providers failed. Last error: ${lastError?.message}`);
  }

  /**
   * 调用指定的AI提供商
   */
  private async callProvider(providerName: string, request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }

    return await provider.analyzeCode(request);
  }

  /**
   * 检查提供商是否可用
   */
  private isProviderAvailable(providerName: string): boolean {
    if (!this.providers.has(providerName)) {
      return false;
    }

    // 检查熔断器状态
    const circuitState = this.circuitBreaker.get(providerName);
    if (circuitState && circuitState.failures >= 3) {
      const timeSinceLastFailure = Date.now() - circuitState.lastFailure.getTime();
      // 5分钟后重试
      if (timeSinceLastFailure < 5 * 60 * 1000) {
        return false;
      } else {
        // 重置熔断器
        this.circuitBreaker.delete(providerName);
      }
    }

    return true;
  }

  /**
   * 更新熔断器状态
   */
  private updateCircuitBreaker(providerName: string): void {
    const current = this.circuitBreaker.get(providerName) || { failures: 0, lastFailure: new Date() };
    current.failures++;
    current.lastFailure = new Date();
    this.circuitBreaker.set(providerName, current);

    if (current.failures >= 3) {
      logger.warn(`Circuit breaker activated for ${providerName} due to repeated failures`);
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    providerName: string,
    startTime: number,
    tokensUsed: number,
    success: boolean
  ): void {
    const stats = this.stats.get(providerName);
    if (!stats) return;

    const responseTime = Date.now() - startTime;

    stats.totalRequests++;
    stats.totalTokensUsed += tokensUsed;
    stats.lastUsed = new Date();

    if (success) {
      stats.successfulRequests++;
    } else {
      stats.failedRequests++;
    }

    // 更新平均响应时间
    stats.averageResponseTime = 
      (stats.averageResponseTime * (stats.successfulRequests - (success ? 1 : 0)) + responseTime) / 
      stats.successfulRequests;

    this.stats.set(providerName, stats);
  }

  /**
   * 确定后备提供商顺序
   */
  private determineFallbackOrder(primaryProvider: string): string[] {
    const allProviders = ['openai', 'claude', 'deepseek', 'local'];
    return allProviders.filter(p => p !== primaryProvider);
  }

  /**
   * 测试所有提供商连接
   */
  public async testAllConnections(): Promise<Map<string, boolean>> {
    const results = new Map<string, boolean>();

    for (const [name, provider] of this.providers) {
      try {
        const isConnected = await provider.testConnection();
        results.set(name, isConnected);
        
        if (isConnected) {
          logger.info(`✓ ${name} provider connection successful`);
        } else {
          logger.warn(`✗ ${name} provider connection failed`);
        }
      } catch (error) {
        results.set(name, false);
        logger.warn(`✗ ${name} provider connection error: ${error}`);
      }
    }

    return results;
  }

  /**
   * 获取提供商统计信息
   */
  public getProviderStats(): Map<string, AIProviderStats> {
    return new Map(this.stats);
  }

  /**
   * 获取当前最佳提供商
   */
  public getBestProvider(): string {
    let bestProvider = this.currentProvider;
    let bestScore = this.calculateProviderScore(this.currentProvider);

    for (const [providerName] of this.providers) {
      if (!this.isProviderAvailable(providerName)) continue;

      const score = this.calculateProviderScore(providerName);
      if (score > bestScore) {
        bestProvider = providerName;
        bestScore = score;
      }
    }

    return bestProvider;
  }

  /**
   * 计算提供商评分
   */
  private calculateProviderScore(providerName: string): number {
    const stats = this.stats.get(providerName);
    if (!stats || stats.totalRequests === 0) {
      return 0;
    }

    const successRate = stats.successfulRequests / stats.totalRequests;
    const responseTimePenalty = Math.min(stats.averageResponseTime / 10000, 0.5); // 最大扣0.5分
    
    return successRate - responseTimePenalty;
  }

  /**
   * 获取所有可用模型
   */
  public async getAvailableModels(): Promise<Map<string, string[]>> {
    const models = new Map<string, string[]>();

    for (const [name, provider] of this.providers) {
      try {
        if (this.isProviderAvailable(name)) {
          const providerModels = await provider.getAvailableModels();
          models.set(name, providerModels);
        }
      } catch (error) {
        logger.warn(`Failed to get models for ${name}: ${error}`);
        models.set(name, []);
      }
    }

    return models;
  }

  /**
   * 估算token使用量
   */
  public estimateTokenUsage(request: AIAnalysisRequest, providerName?: string): number {
    const targetProvider = providerName || this.currentProvider;
    const provider = this.providers.get(targetProvider);
    
    if (!provider) {
      return 1000; // 默认估算
    }

    try {
      return provider.estimateTokenUsage(request);
    } catch (error) {
      logger.debug(`Failed to estimate token usage for ${targetProvider}: ${error}`);
      return 1000;
    }
  }

  /**
   * 切换主要提供商
   */
  public switchProvider(newProvider: string): boolean {
    if (!this.providers.has(newProvider)) {
      logger.error(`Provider ${newProvider} not available`);
      return false;
    }

    if (!this.isProviderAvailable(newProvider)) {
      logger.error(`Provider ${newProvider} is currently unavailable`);
      return false;
    }

    const oldProvider = this.currentProvider;
    this.currentProvider = newProvider;
    this.fallbackProviders = this.determineFallbackOrder(newProvider);
    
    logger.info(`Switched AI provider from ${oldProvider} to ${newProvider}`);
    return true;
  }

  /**
   * 生成提供商状态报告
   */
  public generateStatusReport(): string {
    const lines = ['📊 AI Provider Status Report:', ''];
    
    for (const [name, stats] of this.stats) {
      const available = this.isProviderAvailable(name);
      const status = available ? '🟢' : '🔴';
      const primary = name === this.currentProvider ? '⭐' : '  ';
      
      lines.push(`${primary}${status} ${name.toUpperCase()}`);
      lines.push(`   Requests: ${stats.totalRequests} (${stats.successfulRequests} success, ${stats.failedRequests} failed)`);
      lines.push(`   Success Rate: ${stats.totalRequests > 0 ? ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(1) : 0}%`);
      lines.push(`   Avg Response: ${stats.averageResponseTime.toFixed(0)}ms`);
      lines.push(`   Tokens Used: ${stats.totalTokensUsed.toLocaleString()}`);
      lines.push(`   Last Used: ${stats.lastUsed ? stats.lastUsed.toLocaleString() : 'Never'}`);
      lines.push('');
    }

    return lines.join('\n');
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.initializeStats();
    this.circuitBreaker.clear();
    logger.info('AI provider statistics reset');
  }
}