import * as readline from 'readline';
import chalk from 'chalk';
import * as fs from 'fs-extra';
import * as path from 'path';
import { logger } from '../utils/logger';
import { AIReviewConfig } from '../types';

export interface WizardOptions {
  skipExisting?: boolean;
  interactive?: boolean;
}

export class ConfigWizard {
  private rl: readline.Interface;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  /**
   * 启动配置向导
   */
  public async startWizard(options: WizardOptions = {}): Promise<AIReviewConfig> {
    console.log(chalk.blue.bold('\n🚀 AI Code Review Configuration Wizard'));
    console.log(chalk.gray('This wizard will help you set up AI Code Review for your project.\n'));

    const config = await this.collectConfiguration(options.interactive !== false);
    
    this.rl.close();
    return config;
  }

  /**
   * 收集配置信息
   */
  private async collectConfiguration(interactive: boolean): Promise<AIReviewConfig> {
    const config: Partial<AIReviewConfig> = {};

    // 选择AI提供商
    if (interactive) {
      config.aiProvider = await this.askAIProvider();
      config.model = await this.askModel(config.aiProvider);
      config.apiKey = await this.askAPIKey(config.aiProvider);
      config.baseUrl = await this.askBaseUrl(config.aiProvider);
    } else {
      // 非交互模式，使用默认值
      config.aiProvider = 'openai';
      config.model = 'gpt-4';
    }

    // 分析规则配置
    if (interactive) {
      console.log(chalk.yellow('\n📋 Analysis Rules Configuration'));
      console.log('Configure analysis strictness for different categories:\n');
      
      config.rules = {
        logic: await this.askStrictness('Logic errors (null checks, undefined variables)'),
        performance: await this.askStrictness('Performance issues (inefficient loops, memory leaks)'),
        security: await this.askStrictness('Security vulnerabilities (XSS, injection)'),
        style: await this.askStrictness('Code style and conventions'),
        business: await this.askStrictness('Business logic and domain rules')
      };
    } else {
      config.rules = {
        logic: 'strict',
        performance: 'normal', 
        security: 'strict',
        style: 'normal',
        business: 'normal'
      };
    }

    // 输出格式
    if (interactive) {
      config.output = await this.askOutputFormat();
    } else {
      config.output = {
        format: 'terminal',
        detailed: true,
        verbose: false
      };
    }

    // 缓存设置
    if (interactive) {
      const enableCache = await this.askQuestion('Enable caching for faster subsequent runs? (Y/n)', 'y');
      config.cache = {
        enabled: enableCache.toLowerCase() !== 'n',
        ttl: 24
      };
    } else {
      config.cache = {
        enabled: true,
        ttl: 24
      };
    }

    // 忽略文件
    config.ignore = [
      'node_modules/**',
      '*.min.js',
      '*.min.css',
      'dist/**',
      'build/**',
      '.git/**'
    ];

    return config as AIReviewConfig;
  }

  /**
   * 询问AI提供商
   */
  private async askAIProvider(): Promise<'openai' | 'claude' | 'deepseek' | 'local'> {
    console.log(chalk.yellow('🤖 AI Provider Selection'));
    console.log('Choose an AI provider for code analysis:\n');
    console.log('1. OpenAI (GPT-4) - Excellent general analysis');
    console.log('2. Anthropic Claude - Great for security and logic');
    console.log('3. DeepSeek - Code-focused AI with competitive pricing');
    console.log('4. Local Model - Use local LLM (requires setup)');
    
    const choice = await this.askQuestion('\nSelect provider (1-4)', '1');
    
    switch (choice) {
      case '2': return 'claude';
      case '3': return 'deepseek';
      case '4': return 'local';
      default: return 'openai';
    }
  }

  /**
   * 询问模型选择
   */
  private async askModel(provider: string): Promise<string> {
    console.log(chalk.yellow(`\n🧠 Model Selection for ${provider.toUpperCase()}`));
    
    let models: string[];
    let defaultModel: string;
    
    switch (provider) {
      case 'openai':
        models = ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'];
        defaultModel = 'gpt-4';
        break;
      case 'claude':
        models = ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307'];
        defaultModel = 'claude-3-sonnet-20240229';
        break;
      case 'deepseek':
        models = ['deepseek-coder', 'deepseek-chat', 'deepseek-coder-33b-instruct', 'deepseek-coder-6.7b-instruct'];
        defaultModel = 'deepseek-coder';
        break;
      case 'local':
        models = ['codellama', 'deepseek-coder', 'starcoder'];
        defaultModel = 'codellama';
        break;
      default:
        return 'gpt-4';
    }

    console.log('Available models:');
    models.forEach((model, index) => {
      console.log(`${index + 1}. ${model}${model === defaultModel ? ' (recommended)' : ''}`);
    });

    const choice = await this.askQuestion(`\nSelect model (1-${models.length})`, '1');
    const index = parseInt(choice) - 1;
    
    return models[index] || defaultModel;
  }

  /**
   * 询问API密钥
   */
  private async askAPIKey(provider: string): Promise<string | undefined> {
    if (provider === 'local') {
      return undefined;
    }

    const envVar = provider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY';
    const existing = process.env[envVar];

    if (existing) {
      console.log(chalk.green(`\n✓ Found API key in environment variable ${envVar}`));
      const useExisting = await this.askQuestion('Use existing API key? (Y/n)', 'y');
      if (useExisting.toLowerCase() !== 'n') {
        return existing;
      }
    }

    console.log(chalk.yellow(`\n🔑 API Key for ${provider.toUpperCase()}`));
    console.log(`You can also set the ${envVar} environment variable instead.`);
    
    const apiKey = await this.askQuestion('Enter API key (leave empty to use env var)', '', true);
    return apiKey || undefined;
  }

  /**
   * 询问自定义基础URL
   */
  private async askBaseUrl(provider: string): Promise<string | undefined> {
    // 只有特定提供商需要自定义URL
    if (provider !== 'deepseek') {
      return undefined;
    }

    console.log(chalk.yellow(`\n🌐 Custom Base URL for ${provider.toUpperCase()}`));
    console.log('You can set a custom base URL for the API endpoint.');
    console.log('Leave empty to use the default URL.');
    
    const baseUrl = await this.askQuestion('Enter custom base URL (optional)', '');
    return baseUrl || undefined;
  }

  /**
   * 询问严格程度
   */
  private async askStrictness(category: string): Promise<'strict' | 'normal' | 'loose'> {
    const choice = await this.askQuestion(
      `${category} - Strictness (1=strict, 2=normal, 3=loose)`,
      '2'
    );
    
    switch (choice) {
      case '1': return 'strict';
      case '3': return 'loose';
      default: return 'normal';
    }
  }

  /**
   * 询问输出格式
   */
  private async askOutputFormat(): Promise<AIReviewConfig['output']> {
    console.log(chalk.yellow('\n📊 Output Configuration'));
    
    const format = await this.askQuestion(
      'Output format (1=terminal, 2=html, 3=json, 4=all)',
      '1'
    );

    let outputFormat: 'terminal' | 'html' | 'json';
    switch (format) {
      case '2': outputFormat = 'html'; break;
      case '3': outputFormat = 'json'; break;
      default: outputFormat = 'terminal'; break;
    }

    const detailed = await this.askQuestion('Show detailed explanations? (Y/n)', 'y');
    const verbose = await this.askQuestion('Enable verbose output? (y/N)', 'n');

    return {
      format: outputFormat,
      detailed: detailed.toLowerCase() !== 'n',
      verbose: verbose.toLowerCase() === 'y'
    };
  }

  /**
   * 询问问题
   */
  private async askQuestion(question: string, defaultValue?: string, isPassword = false): Promise<string> {
    const prompt = defaultValue 
      ? `${question} [${defaultValue}]: `
      : `${question}: `;

    return new Promise((resolve) => {
      if (isPassword) {
        // 简单的密码输入处理
        process.stdout.write(prompt);
        const input = process.stdin;
        input.setRawMode(true);
        input.resume();
        input.setEncoding('utf8');
        
        let password = '';
        const onData = (char: string) => {
          switch (char) {
            case '\n':
            case '\r':
            case '\u0004':
              input.setRawMode(false);
              input.pause();
              input.removeListener('data', onData);
              console.log('');
              resolve(password || defaultValue || '');
              break;
            case '\u0003':
              process.exit(0);
              break;
            case '\u007f': // backspace
              if (password.length > 0) {
                password = password.slice(0, -1);
                process.stdout.write('\b \b');
              }
              break;
            default:
              password += char;
              process.stdout.write('*');
              break;
          }
        };
        
        input.on('data', onData);
      } else {
        this.rl.question(prompt, (answer) => {
          resolve(answer.trim() || defaultValue || '');
        });
      }
    });
  }

  /**
   * 保存配置文件
   */
  public async saveConfiguration(config: AIReviewConfig, configPath?: string): Promise<string> {
    const finalPath = configPath || path.join(process.cwd(), '.ai-review.json');
    
    // 创建配置副本，移除敏感信息
    const configToSave = { ...config };
    if (configToSave.apiKey && !configPath) {
      // 如果没有指定路径，建议使用环境变量
      delete configToSave.apiKey;
      console.log(chalk.yellow('\n💡 Tip: For security, API key was not saved to config file.'));
      const envVar = config.aiProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY';
      console.log(`Set the ${envVar} environment variable instead.`);
    }

    await fs.writeJSON(finalPath, configToSave, { spaces: 2 });
    console.log(chalk.green(`\n✅ Configuration saved to: ${finalPath}`));
    
    return finalPath;
  }

  /**
   * 验证配置
   */
  public validateConfiguration(config: AIReviewConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.aiProvider) {
      errors.push('AI provider is required');
    }

    if (!config.model) {
      errors.push('Model selection is required');
    }

    if (config.aiProvider !== 'local' && !config.apiKey && !this.getEnvironmentAPIKey(config.aiProvider)) {
      errors.push(`API key is required for ${config.aiProvider}`);
    }

    // 验证 baseUrl 格式（如果提供）
    if (config.baseUrl) {
      try {
        new URL(config.baseUrl);
      } catch (e) {
        errors.push('Base URL must be a valid URL');
      }
    }

    if (!config.rules) {
      errors.push('Analysis rules configuration is required');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取环境变量中的API密钥
   */
  private getEnvironmentAPIKey(provider: string): string | undefined {
    switch (provider) {
      case 'openai':
        return process.env.OPENAI_API_KEY;
      case 'claude':
        return process.env.ANTHROPIC_API_KEY;
      default:
        return process.env.AI_REVIEW_API_KEY;
    }
  }

  /**
   * 显示配置摘要
   */
  public displayConfigSummary(config: AIReviewConfig): void {
    console.log(chalk.blue.bold('\n📋 Configuration Summary'));
    console.log(chalk.gray('═'.repeat(50)));
    
    console.log(`${chalk.yellow('AI Provider:')} ${config.aiProvider}`);
    console.log(`${chalk.yellow('Model:')} ${config.model}`);
    console.log(`${chalk.yellow('API Key:')} ${config.apiKey ? '***configured***' : 'from environment'}`);
    if (config.baseUrl) {
      console.log(`${chalk.yellow('Base URL:')} ${config.baseUrl}`);
    }
    
    console.log(`\n${chalk.yellow('Analysis Rules:')}`);
    if (config.rules) {
      Object.entries(config.rules).forEach(([rule, strictness]) => {
        const icon = strictness === 'strict' ? '🔴' : strictness === 'normal' ? '🟡' : '🟢';
        console.log(`  ${icon} ${rule}: ${strictness}`);
      });
    }
    
    console.log(`\n${chalk.yellow('Output:')} ${config.output?.format} (detailed: ${config.output?.detailed})`);
    console.log(`${chalk.yellow('Cache:')} ${config.cache?.enabled ? 'enabled' : 'disabled'}`);
    
    console.log(chalk.gray('═'.repeat(50)));
  }
}