# Contributing Guide

Thank you for your interest in contributing to AI Code Review! This guide will help you get started with contributing to the project.

## 🎯 Ways to Contribute

### Code Contributions
- Bug fixes and improvements
- New features and enhancements  
- Performance optimizations
- Documentation improvements
- Test coverage improvements

### Non-Code Contributions
- Bug reports and feature requests
- Documentation improvements
- Community support and discussions
- Translation and localization
- UI/UX improvements

## 🚀 Getting Started

### Prerequisites

- **Node.js:** Version 16.0.0 or higher
- **pnpm:** Package manager (recommended) or npm
- **Git:** Version control
- **TypeScript:** Basic knowledge required
- **AI API Keys:** For testing (OpenAI or Anthropic)

### Development Setup

1. **Fork and Clone:**
   ```bash
   # Fork the repository on GitHub
   git clone https://github.com/YOUR_USERNAME/ai-code-review.git
   cd ai-code-review
   ```

2. **Install Dependencies:**
   ```bash
   pnpm install
   ```

3. **Set Up Environment:**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Add your API keys for testing
   echo "OPENAI_API_KEY=your-key-here" >> .env
   ```

4. **Build and Test:**
   ```bash
   # Build the project
   pnpm build
   
   # Run tests
   pnpm test
   
   # Test CLI locally
   node dist/cli.js --version
   ```

5. **Verify Setup:**
   ```bash
   # Run health check
   node dist/cli.js doctor
   
   # Test with sample code
   echo "console.log('test');" > test.js
   git add test.js
   node dist/cli.js quick
   ```

## 🔄 Development Workflow

### Branch Strategy

We use **GitHub Flow** with feature branches:

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/issue-description

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push and create PR
git push origin feature/your-feature-name
```

### Commit Convention

We follow **Conventional Commits** specification:

```bash
# Format: <type>(<scope>): <description>
git commit -m "feat(cli): add new command option"
git commit -m "fix(analyzer): resolve null pointer exception"
git commit -m "docs(readme): update installation instructions"
git commit -m "test(core): add unit tests for classifier"
```

**Types:**
- `feat:` New feature
- `fix:` Bug fix
- `docs:` Documentation changes
- `test:` Adding or fixing tests
- `refactor:` Code refactoring
- `style:` Code style changes
- `perf:` Performance improvements
- `chore:` Build/tooling changes

**Scopes:**
- `cli:` CLI interface changes
- `core:` Core analysis logic
- `config:` Configuration management
- `ai:` AI provider integration
- `git:` Git integration
- `report:` Report generation
- `cache:` Caching system
- `utils:` Utility functions

### Code Style

1. **TypeScript Strict Mode:**
   ```typescript
   // Good: Explicit types
   interface GitFileChange {
     file: string;
     status: 'added' | 'modified' | 'deleted';
   }
   
   // Bad: Any types
   const config: any = loadConfig();
   ```

2. **Error Handling:**
   ```typescript
   // Good: Proper error handling
   try {
     const result = await riskyOperation();
     return result;
   } catch (error) {
     logger.error('Operation failed:', error);
     throw new AIReviewError(ErrorCode.OPERATION_FAILED, error.message);
   }
   
   // Bad: Swallowing errors
   try {
     await riskyOperation();
   } catch (error) {
     console.log('Something went wrong');
   }
   ```

3. **Async/Await Pattern:**
   ```typescript
   // Good: Consistent async/await
   async function analyzeFile(file: string): Promise<AnalysisResult> {
     const content = await fs.readFile(file);
     const result = await aiProvider.analyze(content);
     return result;
   }
   
   // Bad: Mixed promise styles
   function analyzeFile(file: string) {
     return fs.readFile(file).then(content => {
       return aiProvider.analyze(content);
     });
   }
   ```

## 🧪 Testing Guidelines

### Test Structure

```
tests/
├── unit/                     # Unit tests (fast, isolated)
├── integration/              # Integration tests (slower, real dependencies)
├── fixtures/                 # Test data and samples
└── helpers/                  # Test utilities
```

### Writing Tests

1. **Unit Tests:**
   ```typescript
   // tests/unit/core/classifier.test.ts
   describe('IssueClassifier', () => {
     let classifier: IssueClassifier;
   
     beforeEach(() => {
       classifier = new IssueClassifier();
     });
   
     it('should classify security issues correctly', async () => {
       const issue = { type: 'security', description: 'SQL injection risk' };
       const result = await classifier.classify(issue);
       expect(result.severity).toBe('Critical');
     });
   });
   ```

2. **Integration Tests:**
   ```typescript
   // tests/integration/analysis-flow.test.ts
   describe('Analysis Flow', () => {
     it('should complete full analysis workflow', async () => {
       const tempDir = await createTestRepo();
       const cli = new AIReviewCLI();
       const result = await cli.analyze({ mode: 'quick' });
       expect(result.issues).toBeDefined();
     });
   });
   ```

3. **Mocking AI Providers:**
   ```typescript
   jest.mock('../src/ai/providers/openai', () => ({
     OpenAIProvider: jest.fn().mockImplementation(() => ({
       analyzeCode: jest.fn().mockResolvedValue('Mock analysis result')
     }))
   }));
   ```

### Test Commands

```bash
# Run all tests
pnpm test

# Run specific test file
pnpm test -- tests/unit/core/classifier.test.ts

# Run tests with coverage
pnpm test -- --coverage

# Run tests in watch mode
pnpm test -- --watch

# Run integration tests only
pnpm test:e2e
```

## 📋 Code Review Process

### Before Submitting PR

1. **Self Review:**
   - [ ] Code follows style guidelines
   - [ ] All tests pass
   - [ ] No linting errors
   - [ ] Documentation updated if needed

2. **Testing Checklist:**
   ```bash
   # Run full test suite
   pnpm test
   
   # Check linting
   pnpm lint
   
   # Build successfully
   pnpm build
   
   # Manual testing
   ./scripts/verify-build.sh
   ```

3. **PR Description:**
   Include:
   - Clear description of changes
   - Motivation for the change
   - Testing instructions
   - Screenshots (if UI changes)
   - Breaking changes (if any)

### PR Template

```markdown
## Description
Brief description of changes and why they were needed.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] New tests added for new functionality

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or documented)
```

### Review Criteria

**Reviewers will check:**
- Code quality and maintainability
- Test coverage and quality
- Documentation completeness
- Performance implications
- Security considerations
- Breaking change impact

## 🐛 Bug Reports

### Good Bug Report

Include:
1. **Environment:** OS, Node.js version, AI Code Review version
2. **Steps to Reproduce:** Clear, numbered steps
3. **Expected Behavior:** What should happen
4. **Actual Behavior:** What actually happens
5. **Additional Context:** Logs, screenshots, config files

### Bug Report Template

```markdown
**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Run command '...'
2. With configuration '...'
3. See error

**Expected behavior**
What you expected to happen.

**Screenshots/Logs**
Add screenshots or log output to help explain your problem.

**Environment (please complete):**
- OS: [e.g. macOS 12.0]
- Node.js: [e.g. 18.0.0]
- AI Code Review: [e.g. 1.0.0]
- Git: [e.g. 2.34.1]

**Additional context**
Configuration file, unusual setup, etc.
```

## 🌟 Feature Requests

### Good Feature Request

Include:
1. **Problem Statement:** What problem does this solve?
2. **Proposed Solution:** How should it work?
3. **Alternatives Considered:** Other approaches you've thought about
4. **Use Cases:** Real-world scenarios where this would help

### Feature Request Template

```markdown
**Is your feature request related to a problem?**
A clear and concise description of what the problem is.

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
Other solutions or features you've considered.

**Use cases**
Real-world scenarios where this feature would be valuable.

**Additional context**
Mockups, examples, or any other context.
```

## 🔧 Development Areas

### High Priority Areas

1. **AI Provider Integration:**
   - New AI providers (Google Vertex AI, Azure OpenAI)
   - Local LLM support (Ollama, LM Studio)
   - Custom model fine-tuning

2. **Language Support:**
   - Python analysis
   - Java/Kotlin support
   - Go language support
   - Rust integration

3. **Output Formats:**
   - SARIF format support
   - XML report generation
   - Markdown reports
   - Custom template support

4. **Performance Improvements:**
   - Parallel file processing
   - Streaming analysis for large files
   - Memory usage optimization
   - Cache performance tuning

### Medium Priority Areas

1. **IDE Integration:**
   - VSCode extension
   - JetBrains plugin
   - Vim/Neovim plugin
   - Emacs integration

2. **CI/CD Integration:**
   - GitHub Actions workflow
   - GitLab CI templates
   - Jenkins plugin
   - Azure DevOps extension

3. **Team Features:**
   - Shared configuration templates
   - Team analytics dashboard
   - Custom rule definitions
   - Review assignment logic

## 🏗 Architecture Guidelines

### Adding New Modules

1. **Follow existing patterns:**
   - Use TypeScript interfaces
   - Implement error handling
   - Add comprehensive logging
   - Include unit tests

2. **Module structure:**
   ```typescript
   // src/newmodule/index.ts
   export class NewModule {
     constructor(private config: Config) {}
     
     async performOperation(): Promise<Result> {
       try {
         logger.info('Starting operation');
         const result = await this.doWork();
         logger.info('Operation completed');
         return result;
       } catch (error) {
         logger.error('Operation failed:', error);
         throw new AIReviewError(ErrorCode.OPERATION_FAILED, error.message);
       }
     }
   }
   ```

3. **Integration points:**
   - Register in appropriate managers
   - Update configuration schema
   - Add to CLI commands if needed
   - Update documentation

### Performance Considerations

1. **Memory Management:**
   - Stream large files
   - Clear unused objects
   - Use weak references where appropriate
   - Monitor memory usage in tests

2. **Async Operations:**
   - Use Promise.all for parallel operations
   - Implement proper timeout handling
   - Add cancellation support
   - Avoid blocking the event loop

3. **Caching Strategy:**
   - Cache expensive operations
   - Use appropriate cache keys
   - Implement cache invalidation
   - Monitor cache hit rates

## 📚 Documentation Standards

### Code Documentation

1. **JSDoc Comments:**
   ```typescript
   /**
    * Analyzes code changes and returns issues found.
    * @param changes - Git changes to analyze
    * @param options - Analysis configuration options
    * @returns Promise resolving to analysis results
    * @throws {AIReviewError} When analysis fails
    */
   async analyzeChanges(changes: GitChange[], options: AnalysisOptions): Promise<AnalysisResult[]> {
     // Implementation
   }
   ```

2. **README Updates:**
   - Update feature lists
   - Add configuration examples
   - Include usage instructions
   - Update troubleshooting guides

3. **Changelog Entries:**
   ```markdown
   ## [1.1.0] - 2024-XX-XX
   
   ### Added
   - New Python file analysis support
   - Custom prompt templates
   
   ### Changed
   - Improved error messages for API failures
   - Updated dependencies
   
   ### Fixed
   - Cache invalidation edge case
   - Memory leak in large file analysis
   ```

## 🎉 Recognition

### Contributors

All contributors are recognized in:
- GitHub contributors page
- CHANGELOG.md acknowledgments
- Annual contributor highlights

### Significant Contributions

Major contributions may receive:
- Maintainer status invitation
- Special recognition in releases
- Conference talk opportunities
- Direct collaboration on roadmap

## ❓ Getting Help

### Development Questions

1. **GitHub Discussions:** General questions and ideas
2. **GitHub Issues:** Bug reports and feature requests
3. **Discord/Slack:** Real-time community chat (coming soon)
4. **Code Review:** PR feedback and suggestions

### Maintainer Contact

- **Project Lead:** [@maintainer-github-handle]
- **Technical Lead:** [@tech-lead-github-handle]
- **Community Manager:** [@community-github-handle]

## 📝 Legal

### License

By contributing to AI Code Review, you agree that your contributions will be licensed under the MIT License.

### Code of Conduct

This project follows the [Contributor Covenant Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

---

**Thank you for contributing to AI Code Review!** 🚀

Every contribution, no matter how small, helps make the project better for everyone. We appreciate your time and effort in improving the developer experience for code review processes.

*For questions about this guide, please open a GitHub Discussion or issue.*