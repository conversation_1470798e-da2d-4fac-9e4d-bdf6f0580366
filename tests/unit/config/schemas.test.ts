import { validateConfig, defaultConfig } from '../../../src/config/schemas';

describe('Configuration Schemas', () => {
  describe('validateConfig', () => {
    it('should validate correct configuration', () => {
      const result = validateConfig(defaultConfig);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid configuration object', () => {
      const result = validateConfig(null);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Configuration must be an object');
    });

    it('should validate aiProvider field', () => {
      const invalidConfig = { ...defaultConfig, aiProvider: 'invalid' };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('aiProvider must be one of');
    });

    it('should validate model field', () => {
      const invalidConfig = { ...defaultConfig, model: '' };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('model must be a non-empty string');
    });

    it('should validate ignore array', () => {
      const invalidConfig = { ...defaultConfig, ignore: 'not-an-array' };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('ignore must be an array');
    });

    it('should validate rules object', () => {
      const invalidConfig = { ...defaultConfig, rules: null };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('rules must be an object');
    });

    it('should validate rule strictness levels', () => {
      const invalidConfig = {
        ...defaultConfig,
        rules: {
          ...defaultConfig.rules,
          logic: 'invalid-level'
        }
      };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('rules.logic must be one of');
    });

    it('should validate output format', () => {
      const invalidConfig = {
        ...defaultConfig,
        output: {
          ...defaultConfig.output,
          format: 'invalid-format'
        }
      };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('output.format must be one of');
    });

    it('should validate cache settings', () => {
      const invalidConfig = {
        ...defaultConfig,
        cache: {
          enabled: true,
          ttl: 200 // exceeds max of 168
        }
      };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain('cache.ttl must be a number between 1 and 168');
    });

    it('should collect multiple validation errors', () => {
      const invalidConfig = {
        aiProvider: 'invalid',
        model: '',
        ignore: 'not-array',
        rules: null,
        output: null,
        cache: null
      };
      const result = validateConfig(invalidConfig);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });

  describe('defaultConfig', () => {
    it('should have valid default configuration', () => {
      const result = validateConfig(defaultConfig);
      expect(result.valid).toBe(true);
    });

    it('should include essential ignore patterns', () => {
      expect(defaultConfig.ignore).toContain('node_modules/**');
      expect(defaultConfig.ignore).toContain('dist/**');
      expect(defaultConfig.ignore).toContain('.git/**');
    });

    it('should have reasonable default rules', () => {
      expect(defaultConfig.rules.logic).toBe('strict');
      expect(defaultConfig.rules.security).toBe('strict');
      expect(defaultConfig.rules.performance).toBe('normal');
    });

    it('should have cache enabled by default', () => {
      expect(defaultConfig.cache.enabled).toBe(true);
      expect(defaultConfig.cache.ttl).toBe(24);
    });
  });
});