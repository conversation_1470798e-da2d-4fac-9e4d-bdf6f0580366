import { GitScanner } from '../../../src/git/scanner';
import * as fs from 'fs-extra';
import simpleGit from 'simple-git';

jest.mock('simple-git');
jest.mock('fs-extra');

describe('GitScanner', () => {
  let scanner: GitScanner;
  let mockGit: any;

  beforeEach(() => {
    mockGit = {
      status: jest.fn(),
      diff: jest.fn(),
      revparse: jest.fn()
    };
    (simpleGit as jest.Mock).mockReturnValue(mockGit);
    scanner = new GitScanner();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('isGitRepository', () => {
    it('should return true for valid git repository', async () => {
      mockGit.status.mockResolvedValue({});
      const result = await scanner.isGitRepository();
      expect(result).toBe(true);
    });

    it('should return false for non-git directory', async () => {
      mockGit.status.mockRejectedValue(new Error('Not a git repository'));
      const result = await scanner.isGitRepository();
      expect(result).toBe(false);
    });
  });

  describe('getStagedFiles', () => {
    it('should return empty array when no files are staged', async () => {
      mockGit.status.mockResolvedValue({
        staged: [],
        modified: [],
        not_added: [],
        deleted: [],
        created: [],
        current: 'main'
      });

      const result = await scanner.getStagedFiles();
      expect(result).toEqual([]);
    });

    it('should filter out unsupported file types', async () => {
      mockGit.status.mockResolvedValue({
        staged: ['test.js', 'image.png', 'style.css'],
        modified: [],
        not_added: [],
        deleted: [],
        created: [],
        current: 'main'
      });

      (fs.pathExists as jest.Mock).mockResolvedValue(true);
      mockGit.diff.mockResolvedValue('');

      const result = await scanner.getStagedFiles();
      expect(result).toHaveLength(2); // Only .js and .css files
      expect(result.map(r => r.file)).toEqual(['test.js', 'style.css']);
    });

    it('should handle deleted files correctly', async () => {
      mockGit.status.mockResolvedValue({
        staged: ['deleted.js'],
        modified: [],
        not_added: [],
        deleted: ['deleted.js'],
        created: [],
        current: 'main'
      });

      (fs.pathExists as jest.Mock).mockResolvedValue(false);

      const result = await scanner.getStagedFiles();
      expect(result).toHaveLength(1);
      expect(result[0].status).toBe('deleted');
    });
  });

  describe('getCurrentBranch', () => {
    it('should return current branch name', async () => {
      mockGit.status.mockResolvedValue({
        current: 'feature/test-branch'
      });

      const result = await scanner.getCurrentBranch();
      expect(result).toBe('feature/test-branch');
    });

    it('should return unknown when branch cannot be determined', async () => {
      mockGit.status.mockRejectedValue(new Error('Git error'));

      const result = await scanner.getCurrentBranch();
      expect(result).toBe('unknown');
    });
  });

  describe('getFileContent', () => {
    it('should return file content when file exists', async () => {
      const testContent = 'const test = "hello world";';
      (fs.readFile as unknown as jest.Mock).mockResolvedValue(testContent);

      const result = await scanner.getFileContent('test.js');
      expect(result).toBe(testContent);
    });

    it('should return empty string when file does not exist', async () => {
      (fs.readFile as unknown as jest.Mock).mockRejectedValue(new Error('File not found'));

      const result = await scanner.getFileContent('nonexistent.js');
      expect(result).toBe('');
    });

    it('should prevent path traversal attacks', async () => {
      mockGit.revparse.mockResolvedValue('/project/root\n');
      
      const result = await scanner.getFileContent('../../../etc/passwd');
      expect(result).toBe('');
      expect(fs.readFile).not.toHaveBeenCalled();
    });
  });

  describe('getRepositoryRoot', () => {
    it('should return repository root path', async () => {
      mockGit.revparse.mockResolvedValue('/path/to/repo\n');

      const result = await scanner.getRepositoryRoot();
      expect(result).toBe('/path/to/repo');
    });

    it('should return current directory on failure', async () => {
      mockGit.revparse.mockRejectedValue(new Error('Not a git repository'));

      const result = await scanner.getRepositoryRoot();
      expect(result).toBe(process.cwd());
    });
  });
});