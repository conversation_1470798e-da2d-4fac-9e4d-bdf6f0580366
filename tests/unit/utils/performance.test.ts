import { PerformanceMonitor } from '../../../src/utils/performance';

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor;

  beforeEach(() => {
    // 获取新的实例并清理之前的指标
    monitor = PerformanceMonitor.getInstance();
    monitor.clearMetrics();
  });

  afterEach(() => {
    monitor.clearMetrics();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = PerformanceMonitor.getInstance();
      const instance2 = PerformanceMonitor.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Operation Timing', () => {
    it('should track operation timing', () => {
      const operationId = monitor.startOperation('test-operation');
      expect(operationId).toBeTruthy();

      // 模拟一些工作
      const work = () => {
        let sum = 0;
        for (let i = 0; i < 1000; i++) {
          sum += i;
        }
        return sum;
      };
      work();

      const metrics = monitor.endOperation(operationId, true);
      expect(metrics).toBeTruthy();
      expect(metrics?.duration).toBeGreaterThan(0);
      expect(metrics?.operationName).toBe('test-operation');
    });

    it('should handle operation failures', () => {
      const operationId = monitor.startOperation('failing-operation');
      const error = new Error('Test error');
      
      const metrics = monitor.endOperation(operationId, false, error);
      expect(metrics?.metadata?.success).toBe(false);
      expect(metrics?.metadata?.error).toBe('Test error');
    });

    it('should return null for invalid operation ID', () => {
      const metrics = monitor.endOperation('invalid-id');
      expect(metrics).toBeNull();
    });
  });

  describe('Async Operation Timing', () => {
    it('should time async operations', async () => {
      const asyncOperation = async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'success';
      };

      const result = await monitor.timeOperation(
        'async-test',
        asyncOperation,
        { testMeta: 'value' }
      );

      expect(result).toBe('success');
      
      const aggregated = monitor.getAggregatedMetrics('async-test');
      expect(aggregated?.count).toBe(1);
      expect(aggregated?.successCount).toBe(1);
    });

    it('should handle async operation errors', async () => {
      const failingOperation = async () => {
        throw new Error('Async error');
      };

      await expect(
        monitor.timeOperation('async-fail', failingOperation)
      ).rejects.toThrow('Async error');

      const aggregated = monitor.getAggregatedMetrics('async-fail');
      expect(aggregated?.errorCount).toBe(1);
    });
  });

  describe('Sync Operation Timing', () => {
    it('should time sync operations', () => {
      const syncOperation = () => {
        return 42;
      };

      const result = monitor.timeSync('sync-test', syncOperation);
      expect(result).toBe(42);

      const aggregated = monitor.getAggregatedMetrics('sync-test');
      expect(aggregated?.count).toBe(1);
    });

    it('should handle sync operation errors', () => {
      const failingOperation = () => {
        throw new Error('Sync error');
      };

      expect(() => 
        monitor.timeSync('sync-fail', failingOperation)
      ).toThrow('Sync error');

      const aggregated = monitor.getAggregatedMetrics('sync-fail');
      expect(aggregated?.errorCount).toBe(1);
    });
  });

  describe('Aggregated Metrics', () => {
    it('should calculate aggregated metrics correctly', async () => {
      // 执行多个操作
      for (let i = 0; i < 5; i++) {
        const opId = monitor.startOperation('multi-test');
        await new Promise(resolve => setTimeout(resolve, 5));
        monitor.endOperation(opId, i % 2 === 0); // 交替成功/失败
      }

      const aggregated = monitor.getAggregatedMetrics('multi-test');
      expect(aggregated?.count).toBe(5);
      expect(aggregated?.successCount).toBe(3);
      expect(aggregated?.errorCount).toBe(2);
      expect(aggregated?.averageDuration).toBeGreaterThan(0);
    });

    it('should return null for non-existent operations', () => {
      const aggregated = monitor.getAggregatedMetrics('non-existent');
      expect(aggregated).toBeNull();
    });
  });

  describe('Report Generation', () => {
    it('should generate performance report', () => {
      const opId = monitor.startOperation('report-test');
      monitor.endOperation(opId);

      const report = monitor.generateReport();
      expect(report).toContain('Performance Report');
      expect(report).toContain('report-test');
    });

    it('should export metrics as JSON', () => {
      const opId = monitor.startOperation('export-test');
      monitor.endOperation(opId);

      const exported = monitor.exportMetrics();
      const parsed = JSON.parse(exported);
      
      expect(parsed).toHaveProperty('timestamp');
      expect(parsed).toHaveProperty('aggregatedMetrics');
      expect(parsed).toHaveProperty('currentMemory');
    });
  });

  describe('Memory Management', () => {
    it('should limit metrics per operation', () => {
      // 注意：这个测试需要访问私有属性，在实际中可能需要调整
      const operationName = 'memory-test';
      
      // 创建超过限制的指标
      for (let i = 0; i < 1100; i++) {
        const opId = monitor.startOperation(operationName);
        monitor.endOperation(opId);
      }

      const aggregated = monitor.getAggregatedMetrics(operationName);
      // 由于内部限制，不应该无限增长
      expect(aggregated?.count).toBeLessThanOrEqual(1000);
    });
  });

  describe('Enable/Disable', () => {
    it('should respect enabled flag', () => {
      monitor.setEnabled(false);
      expect(monitor.isEnabled()).toBe(false);

      const opId = monitor.startOperation('disabled-test');
      expect(opId).toBe('');

      monitor.setEnabled(true);
      expect(monitor.isEnabled()).toBe(true);
    });

    it('should clear metrics when disabled', () => {
      const opId = monitor.startOperation('clear-test');
      monitor.endOperation(opId);

      monitor.setEnabled(false);
      const allMetrics = monitor.getAllMetrics();
      expect(allMetrics.size).toBe(0);
    });
  });
});