import { AnalysisEngine } from '../../../src/core/analyzer';
import { GitFileChange, AIReviewConfig } from '../../../src/types';
import * as fs from 'fs-extra';
import * as os from 'os';

jest.mock('fs-extra');
jest.mock('os');

describe('AnalysisEngine', () => {
  let analyzer: AnalysisEngine;
  let mockConfig: AIReviewConfig;

  beforeEach(() => {
    mockConfig = {
      aiProvider: 'openai',
      model: 'gpt-4',
      apiKey: 'test-key',
      ignore: ['node_modules/**'],
      rules: {
        logic: 'strict',
        performance: 'normal',
        security: 'strict',
        style: 'normal',
        business: 'normal'
      },
      output: {
        format: 'terminal',
        detailed: true
      },
      cache: {
        enabled: true,
        ttl: 24
      }
    };

    analyzer = new AnalysisEngine(mockConfig);
    
    // Mock CPU count for parallel processing tests
    (os.cpus as jest.Mock).mockReturnValue(new Array(4));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('File Analysis', () => {
    it('should analyze changed files', async () => {
      const changes: GitFileChange[] = [
        {
          file: 'test.js',
          status: 'modified',
          changes: [
            { line: 1, type: 'added', content: 'console.log("test");' }
          ]
        }
      ];

      (fs.pathExists as jest.Mock).mockResolvedValue(true);

      const results = await analyzer.analyzeChanges(changes);
      
      expect(results).toHaveLength(1);
      expect(results[0].file).toBe('test.js');
      expect(results[0].statistics).toBeDefined();
    });

    it('should handle missing files gracefully', async () => {
      const changes: GitFileChange[] = [
        {
          file: 'missing.js',
          status: 'modified',
          changes: []
        }
      ];

      (fs.pathExists as jest.Mock).mockResolvedValue(false);

      const results = await analyzer.analyzeChanges(changes);
      
      expect(results).toHaveLength(1);
      expect(results[0].issues).toHaveLength(0);
      expect(results[0].metadata?.fromCache).toBe(false);
    });

    it('should process files in parallel batches', async () => {
      const changes: GitFileChange[] = [];
      for (let i = 0; i < 10; i++) {
        changes.push({
          file: `file${i}.js`,
          status: 'modified',
          changes: []
        });
      }

      (fs.pathExists as jest.Mock).mockResolvedValue(true);

      const startTime = Date.now();
      const results = await analyzer.analyzeChanges(changes);
      const endTime = Date.now();

      expect(results).toHaveLength(10);
      // 验证并行处理（时间应该比串行短）
      expect(endTime - startTime).toBeLessThan(10000);
    });
  });

  describe('File Type Detection', () => {
    it('should correctly identify JavaScript files', () => {
      const fileTypes = analyzer.getSupportedFileTypes();
      expect(fileTypes).toContain('javascript');
      expect(fileTypes).toContain('typescript');
    });

    it('should detect file type from extension', () => {
      // 这个测试需要访问私有方法，可能需要调整
      const result = analyzer.getAnalysisStats();
      expect(result).toHaveProperty('totalAnalyzed');
      expect(result).toHaveProperty('cacheHitRate');
      expect(result).toHaveProperty('averageAnalysisTime');
    });
  });

  describe('Configuration', () => {
    it('should update configuration', () => {
      const newConfig: Partial<AIReviewConfig> = {
        model: 'gpt-3.5-turbo'
      };

      analyzer.updateConfig(newConfig);
      // 配置应该被更新
      expect(analyzer).toBeDefined();
    });
  });

  describe('Analysis Options', () => {
    it('should respect analysis options', async () => {
      const changes: GitFileChange[] = [
        {
          file: 'test.js',
          status: 'modified',
          changes: []
        }
      ];

      (fs.pathExists as jest.Mock).mockResolvedValue(true);

      const options = {
        includeContext: false,
        maxContextLines: 10,
        analysisDepth: 'quick' as const
      };

      const results = await analyzer.analyzeChanges(changes, options);
      expect(results).toHaveLength(1);
    });
  });

  describe('Statistics', () => {
    it('should calculate analysis statistics', () => {
      const stats = analyzer.getAnalysisStats();
      
      expect(stats).toHaveProperty('totalAnalyzed');
      expect(stats).toHaveProperty('cacheHitRate');
      expect(stats).toHaveProperty('averageAnalysisTime');
      expect(stats.totalAnalyzed).toBeGreaterThanOrEqual(0);
      expect(stats.cacheHitRate).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Resource Cleanup', () => {
    it('should cleanup resources', async () => {
      await expect(analyzer.cleanup()).resolves.not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle analysis errors gracefully', async () => {
      const changes: GitFileChange[] = [
        {
          file: 'error.js',
          status: 'modified',
          changes: []
        }
      ];

      // 模拟文件读取错误
      (fs.pathExists as jest.Mock).mockRejectedValue(new Error('Read error'));

      const results = await analyzer.analyzeChanges(changes);
      
      expect(results).toHaveLength(1);
      expect(results[0].issues).toHaveLength(0);
      expect(results[0].metadata?.aiModel).toBe('failed');
    });
  });
});