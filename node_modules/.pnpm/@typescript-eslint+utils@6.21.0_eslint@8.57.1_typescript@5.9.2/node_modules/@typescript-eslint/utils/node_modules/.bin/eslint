#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules/eslint/bin/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules/eslint/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules/eslint/bin/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules/eslint/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/eslint@8.57.1/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../eslint@8.57.1/node_modules/eslint/bin/eslint.js" "$@"
else
  exec node  "$basedir/../../../../../../eslint@8.57.1/node_modules/eslint/bin/eslint.js" "$@"
fi
