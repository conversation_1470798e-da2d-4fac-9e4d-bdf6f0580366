{"version": 3, "names": ["cleanJSXElementLiteralChild", "child", "args", "lines", "value", "split", "lastNonEmptyLine", "i", "length", "match", "str", "line", "isFirstLine", "isLastLine", "isLastNonEmptyLine", "trimmedLine", "replace", "push", "stringLiteral"], "sources": ["../../../src/utils/react/cleanJSXElementLiteralChild.ts"], "sourcesContent": ["import { stringLiteral } from \"../../builders/generated\";\nimport type * as t from \"../..\";\n\nexport default function cleanJSXElementLiteralChild(\n  child: {\n    value: string;\n  },\n  args: Array<t.Node>,\n) {\n  const lines = child.value.split(/\\r\\n|\\n|\\r/);\n\n  let lastNonEmptyLine = 0;\n\n  for (let i = 0; i < lines.length; i++) {\n    if (lines[i].match(/[^ \\t]/)) {\n      lastNonEmptyLine = i;\n    }\n  }\n\n  let str = \"\";\n\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n\n    const isFirstLine = i === 0;\n    const isLastLine = i === lines.length - 1;\n    const isLastNonEmptyLine = i === lastNonEmptyLine;\n\n    // replace rendered whitespace tabs with spaces\n    let trimmedLine = line.replace(/\\t/g, \" \");\n\n    // trim whitespace touching a newline\n    if (!isFirstLine) {\n      trimmedLine = trimmedLine.replace(/^[ ]+/, \"\");\n    }\n\n    // trim whitespace touching an endline\n    if (!isLastLine) {\n      trimmedLine = trimmedLine.replace(/[ ]+$/, \"\");\n    }\n\n    if (trimmedLine) {\n      if (!isLastNonEmptyLine) {\n        trimmedLine += \" \";\n      }\n\n      str += trimmedLine;\n    }\n  }\n\n  if (str) args.push(stringLiteral(str));\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,2BAAT,CACbC,KADa,EAIbC,IAJa,EAKb;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAN,CAAYC,KAAZ,CAAkB,YAAlB,CAAd;EAEA,IAAIC,gBAAgB,GAAG,CAAvB;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,KAAK,CAACK,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;IACrC,IAAIJ,KAAK,CAACI,CAAD,CAAL,CAASE,KAAT,CAAe,QAAf,CAAJ,EAA8B;MAC5BH,gBAAgB,GAAGC,CAAnB;IACD;EACF;;EAED,IAAIG,GAAG,GAAG,EAAV;;EAEA,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,KAAK,CAACK,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;IACrC,MAAMI,IAAI,GAAGR,KAAK,CAACI,CAAD,CAAlB;IAEA,MAAMK,WAAW,GAAGL,CAAC,KAAK,CAA1B;IACA,MAAMM,UAAU,GAAGN,CAAC,KAAKJ,KAAK,CAACK,MAAN,GAAe,CAAxC;IACA,MAAMM,kBAAkB,GAAGP,CAAC,KAAKD,gBAAjC;IAGA,IAAIS,WAAW,GAAGJ,IAAI,CAACK,OAAL,CAAa,KAAb,EAAoB,GAApB,CAAlB;;IAGA,IAAI,CAACJ,WAAL,EAAkB;MAChBG,WAAW,GAAGA,WAAW,CAACC,OAAZ,CAAoB,OAApB,EAA6B,EAA7B,CAAd;IACD;;IAGD,IAAI,CAACH,UAAL,EAAiB;MACfE,WAAW,GAAGA,WAAW,CAACC,OAAZ,CAAoB,OAApB,EAA6B,EAA7B,CAAd;IACD;;IAED,IAAID,WAAJ,EAAiB;MACf,IAAI,CAACD,kBAAL,EAAyB;QACvBC,WAAW,IAAI,GAAf;MACD;;MAEDL,GAAG,IAAIK,WAAP;IACD;EACF;;EAED,IAAIL,GAAJ,EAASR,IAAI,CAACe,IAAL,CAAU,IAAAC,wBAAA,EAAcR,GAAd,CAAV;AACV"}