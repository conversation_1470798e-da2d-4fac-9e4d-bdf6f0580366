{"version": 3, "names": ["createTSUnionType", "typeAnnotations", "types", "map", "type", "isTSTypeAnnotation", "typeAnnotation", "flattened", "removeTypeDuplicates", "length", "tsUnionType"], "sources": ["../../../src/builders/typescript/createTSUnionType.ts"], "sourcesContent": ["import { tsUnionType } from \"../generated\";\nimport removeTypeDuplicates from \"../../modifications/typescript/removeTypeDuplicates\";\nimport { isTSTypeAnnotation } from \"../../validators/generated/index\";\nimport type * as t from \"../..\";\n\n/**\n * Takes an array of `types` and flattens them, removing duplicates and\n * returns a `UnionTypeAnnotation` node containing them.\n */\nexport default function createTSUnionType(\n  typeAnnotations: Array<t.TSTypeAnnotation | t.TSType>,\n): t.TSType {\n  const types = typeAnnotations.map(type => {\n    return isTSTypeAnnotation(type) ? type.typeAnnotation : type;\n  });\n  const flattened = removeTypeDuplicates(types);\n\n  if (flattened.length === 1) {\n    return flattened[0];\n  } else {\n    return tsUnionType(flattened);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAOe,SAASA,iBAAT,CACbC,eADa,EAEH;EACV,MAAMC,KAAK,GAAGD,eAAe,CAACE,GAAhB,CAAoBC,IAAI,IAAI;IACxC,OAAO,IAAAC,yBAAA,EAAmBD,IAAnB,IAA2BA,IAAI,CAACE,cAAhC,GAAiDF,IAAxD;EACD,CAFa,CAAd;EAGA,MAAMG,SAAS,GAAG,IAAAC,6BAAA,EAAqBN,KAArB,CAAlB;;EAEA,IAAIK,SAAS,CAACE,MAAV,KAAqB,CAAzB,EAA4B;IAC1B,OAAOF,SAAS,CAAC,CAAD,CAAhB;EACD,CAFD,MAEO;IACL,OAAO,IAAAG,sBAAA,EAAYH,SAAZ,CAAP;EACD;AACF"}