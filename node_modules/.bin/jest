#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules/jest/bin/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules/jest/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules/jest/bin/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules/jest/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/jest@29.7.0_@types+node@20.19.10_ts-node@10.9.2/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../jest/bin/jest.js" "$@"
fi
