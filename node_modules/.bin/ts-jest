#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_741c48a9a112e5ba09e1879515061d93/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_741c48a9a112e5ba09e1879515061d93/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_741c48a9a112e5ba09e1879515061d93/node_modules/ts-jest/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/ts-jest@29.4.1_@babel+core@7.28.0_@jest+transform@29.7.0_@jest+types@29.6.3_babel-jest@_741c48a9a112e5ba09e1879515061d93/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-jest/cli.js" "$@"
else
  exec node  "$basedir/../ts-jest/cli.js" "$@"
fi
