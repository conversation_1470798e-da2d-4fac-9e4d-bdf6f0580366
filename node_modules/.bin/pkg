#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules/pkg/lib-es5/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules/pkg/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules/pkg/lib-es5/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules/pkg/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/pkg@5.8.1/node_modules:/Users/<USER>/Desktop/AI/ai-code-review/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pkg/lib-es5/bin.js" "$@"
else
  exec node  "$basedir/../pkg/lib-es5/bin.js" "$@"
fi
