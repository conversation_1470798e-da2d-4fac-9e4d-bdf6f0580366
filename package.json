{"name": "ai-code-review", "version": "1.0.0", "description": "AI-powered code review tool for Git changes - Intelligent analysis of staged files using GPT-4 and Claude", "main": "dist/cli.js", "bin": {"ai-review": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli.ts", "start": "node dist/cli.js", "test": "jest", "test:e2e": "jest --config jest.config.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist", "prepublishOnly": "pnpm run clean && pnpm run build", "postinstall": "node -e \"console.log('✅ AI Code Review installed! Run \\\"ai-review config --wizard\\\" to get started.')\"", "package": "pkg dist/cli.js --targets node16-linux-x64,node16-macos-x64,node16-win-x64 --output-path ./bin"}, "keywords": ["ai", "artificial-intelligence", "code-review", "code-analysis", "git", "typescript", "javascript", "cli", "gpt-4", "claude", "openai", "anthropic", "static-analysis", "linting", "code-quality", "developer-tools"], "author": {"name": "AI Code Review Team", "email": "<EMAIL>", "url": "https://github.com/ai-code-review/ai-code-review"}, "license": "MIT", "homepage": "https://github.com/ai-code-review/ai-code-review#readme", "repository": {"type": "git", "url": "https://github.com/ai-code-review/ai-code-review.git"}, "bugs": {"url": "https://github.com/ai-code-review/ai-code-review/issues"}, "files": ["dist/**/*", "README.md", "LICENSE", "CHANGELOG.md"], "dependencies": {"@anthropic-ai/sdk": "^0.9.1", "@babel/parser": "^7.23.6", "@typescript-eslint/typescript-estree": "^6.15.0", "chalk": "^4.1.2", "commander": "^11.1.0", "fs-extra": "^11.1.1", "openai": "^4.24.0", "simple-git": "^3.20.0"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "pkg": "^5.8.1", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64"], "preferGlobal": true}